using System.Linq;
using UnityEngine.Events;

namespace RibCageGames.Behaviour
{
    
    using UnityEngine;
    using System.Collections.Generic;
    using RibCageGames.Base;

    /// <summary>
    /// A service to better handle Update style callbacks
    /// </summary>
    [CreateAssetMenu(fileName = "PointOfInterestService", menuName = "RibCageGames/Services/PointOfInterestService")]
    public class PointOfInterestService : BaseService
    {

        public UnityEvent<PointOfInterestTypes, Transform> OnPointAdded { get; } = new UnityEvent<PointOfInterestTypes, Transform>();
        public UnityEvent<PointOfInterestTypes, Transform> OnPointRemoved { get; } = new UnityEvent<PointOfInterestTypes, Transform>();
        
        public UnityEvent<PointOfInterestTypes, List<Transform>> OnBulkChange { get; } = new UnityEvent<PointOfInterestTypes, List<Transform>>();
        
        private Dictionary<PointOfInterestTypes, HashSet<Transform>> pointOfInterestDict;

        public override void Init(GameObject servicePrefab = null)
        {
            pointOfInterestDict = new Dictionary<PointOfInterestTypes, HashSet<Transform>>();
        }

        public override void StartService(MonoInjector injector) { }

        public override void Dispose() {}
        
        public bool GetPointsOfType(PointOfInterestTypes type, out List<Transform> result)
        {
            if(pointOfInterestDict.TryGetValue(type, out HashSet<Transform> points))
            {
                result = points.ToList();
                
                return true;
            }

            result = null;
            return false;
        }
        
        public List<Transform> GetPointsOfType(PointOfInterestTypes type)
        {
            List<Transform> result = null;
            if(pointOfInterestDict.TryGetValue(type, out HashSet<Transform> points))
            {
                result = new List<Transform>();
                result = points.ToList();
            }
            
            return result;
        }

        public void AddPoint(PointOfInterestTypes type, Transform transform, bool triggerEvent = true)
        {
            if(pointOfInterestDict.TryGetValue(type, out HashSet<Transform> points))
            {
                if (points.Add(transform))
                {
                    OnPointAdded?.Invoke(type, transform);
                }
            }
            else
            {
                pointOfInterestDict.Add(type, new HashSet<Transform>());
                pointOfInterestDict[type].Add(transform);
                OnPointAdded?.Invoke(type, transform);
            }
        }

        public void RemovePoint(PointOfInterestTypes type, Transform transform, bool triggerEvent = true)
        {
            if(pointOfInterestDict.TryGetValue(type, out HashSet<Transform> points))
            {
                points.Remove(transform);
                OnPointRemoved?.Invoke(type, transform);
            }
            else
            {
                //TODO: make this not clog logs on exit
                //Debug.Log("Point of interest type to remove not found");
            }
        }
        
        public void AddPointList(PointOfInterestTypes type, List<Transform> transforms)
        {
            foreach (Transform transform in transforms)
            {
                AddPoint(type, transform, false);
            }
            OnBulkChange?.Invoke(type, GetPointsOfType(type));
        }

        public void RemovePointList(PointOfInterestTypes type, List<Transform> transforms)
        {
            foreach (Transform transform in transforms)
            {
                RemovePoint(type, transform, false);
            }
            OnBulkChange?.Invoke(type, GetPointsOfType(type));
        }
    }

    public enum PointOfInterestTypes
    {
        Player,
        Enemy, //Used for engaged enemies
        Terrain, //Used for Terrain nodes
        Other, //Used for non-engaged enemies
    }
}