namespace RibCageGames.Animation.Editor
{
    #if UNITY_EDITOR
    using UnityEditor;
    using UnityEngine;

/*[CustomPropertyDrawer(typeof(TargetedAnimatorParameterAttribute))]
public class TargetedAnimatorParameterPropertyDrawer : AnimatorParameterPropertyDrawer
{
    
    protected override SerializedProperty GetAnimatorProperty(SerializedProperty property) {
        
        Animator animator = property.serializedObject.FindProperty(Atribute.AnimatorVarName).objectReferenceValue as Animator;
        //((Animatable) property.serializedObject.targetObject).Animator;
        
        SerializedProperty animatorReferenceProp = property.FindPropertyRelative("m_animator");
        animatorReferenceProp.objectReferenceValue = animator;
        return animatorReferenceProp;
    }
    
    //protected override float GetAnimatorReferenceWidth(bool hasAnimator, Rect position) {
    //    return 0f;
    //}
    
    public override float GetPropertyHeight(SerializedProperty property,
                                    GUIContent label)
    {
        return EditorGUI.GetPropertyHeight(property, label, true);
    }
    protected virtual TargetedAnimatorParameterAttribute Atribute
    {
        get { return (TargetedAnimatorParameterAttribute)attribute; }
    }
}*/

    [CustomPropertyDrawer(typeof(TargetedAnimatorParameterAttribute))]
    public class TargetedAnimatorParameterAttributeDrawer : TargetedAnimatorParameterDrawer
    {
        protected override Animator GetAnimator(Rect position, SerializedProperty property)
        {
            if (m_animator == null)
            {
                SerializedProperty serializedProperty = property.serializedObject.GetIterator();
                do
                {
                    if (serializedProperty.name == Attribute.AnimatorVarName)
                    {
                        Animator candidate = (Animator) (serializedProperty.objectReferenceValue);
                        SerializedProperty innerSerializedProperty = serializedProperty;
                        
                        do
                        {
                            if (innerSerializedProperty.propertyPath == property.propertyPath)
                            {
                                m_animator = candidate;
                                return m_animator;
                            }
                        }
                        while (innerSerializedProperty.Next(true));
                    }
                }
                while (serializedProperty.Next(true));
            }

            return m_animator;
            //return (Animator) property.serializedObject.FindProperty(Attribute.AnimatorVarName).objectReferenceValue;
        }

        protected virtual TargetedAnimatorParameterAttribute Attribute => (TargetedAnimatorParameterAttribute) attribute;
    }

    [CustomPropertyDrawer(typeof(TargetedAnimatorParameter), true)]
    public class TargetedAnimatorParameterDrawer : AnimatorParameterDrawer
    {
        protected override Animator GetAnimator(Rect position, SerializedProperty property)
        {
            return (Animator) property.serializedObject.FindProperty("m_animator").objectReferenceValue;
        }

        protected override Rect GetDropdownRect(Rect position)
        {
            return new Rect(position.x + 0.3f * position.width, position.y, 0.7f * position.width, position.height);
        }

        protected override void DrawAdditionalProperties(Rect position, SerializedProperty property)
        {
            EditorGUI.LabelField(
                new Rect(position.x + 0.05f * position.width, position.y, 0.2f * position.width, position.height),
                "Parameter");
        }
    }
    #endif
}