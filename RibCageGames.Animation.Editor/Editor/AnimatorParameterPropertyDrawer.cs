namespace RibCageGames.Animation.Editor
{
    #if UNITY_EDITOR
    
    using System.Collections.Generic;
    using System.Linq;
    using UnityEngine;
    using UnityEditor;

    public abstract class AnimatorParameterDrawer : PropertyDrawer
    {

        protected Animator m_animator = null;
        protected AnimatorControllerParameter[] m_parameters = null;

        protected abstract Animator GetAnimator(Rect position, SerializedProperty property);

        protected abstract Rect GetDropdownRect(Rect position);

        protected virtual void DrawAdditionalProperties(Rect position, SerializedProperty property)
        {
        }

        // Draw the property inside the given rect
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            SerializedProperty idProp = property.FindPropertyRelative("m_parameterHash");
            m_animator = GetAnimator(position,
                property); //(Animator) EditorGUI.ObjectField(animatorReferencePos, "Animator", animator, typeof(Animator), true);
            property.serializedObject.ApplyModifiedProperties();

            Rect parameterNameDropdownPosition = GetDropdownRect(position);

            if (m_animator != null)
            {

                AnimatorControllerParameter[] animParameters = m_animator.parameters;
                if (animParameters != null && animParameters.Length > 0)
                {
                    m_parameters = animParameters;
                }

                if (m_parameters == null)
                {
                    return;
                }

                IEnumerable<string> parameterNames = m_parameters.Select(param => param.name);
                string parameterName =
                    parameterNames.FirstOrDefault(parameter => Animator.StringToHash(parameter) == idProp.intValue);

                //if(parameterName)
                //foreach (string name in parameterNames)
                //{
                //    Debug.Log("Showing " + name + " parameter");
                //}

                DrawAdditionalProperties(position, property);

                if (GUI.Button(parameterNameDropdownPosition,
                    string.IsNullOrEmpty(parameterName) ? "Parameter not present in animator" : parameterName,
                    EditorStyles.popup))
                {
                    GenericMenu menu = new GenericMenu();
                    foreach (string parameter in parameterNames)
                    {
                        menu.AddItem(new GUIContent(parameter),
                            parameter == parameterName,
                            enumValue =>
                            {
                                //nameProp.stringValue = parameter;
                                //property.serializedObject.ApplyModifiedProperties(); //Should be specifically string prop?
                                idProp.intValue = Animator.StringToHash(parameter);
                                idProp.serializedObject.ApplyModifiedProperties();
                                Debug.Log("Set hash for parameter " + parameter + " to: " + idProp.intValue);
                                property.serializedObject.ApplyModifiedProperties();
                            }, parameter);
                    }

                    menu.ShowAsContext();
                }
            }
        }
    }


    [CustomPropertyDrawer(typeof(AnimatorParameter))]
    public class NonTargetedAnimatorParameterDrawer : AnimatorParameterDrawer
    {
        protected override Animator GetAnimator(Rect position, SerializedProperty property)
        {
            Rect animatorReferencePos = new Rect(position.x, position.y, 0.7f * position.width, position.height);
            return (Animator) EditorGUI.ObjectField(animatorReferencePos, "Animator", m_animator, typeof(Animator),
                true);
        }

        protected override Rect GetDropdownRect(Rect position)
        {
            return new Rect(position.x + 0.7f * position.width, position.y, 0.3f * position.width, position.height);
        }
    }
    
    #endif
}


//[CustomPropertyDrawer(typeof(UnboundAnimatorParameter))]
    /*public class AnimatorParameterPropertyDrawer : PropertyDrawer
    {

        protected virtual SerializedProperty GetAnimatorProperty(SerializedProperty property) {
            return property.FindPropertyRelative("m_animator");
        }

        protected virtual float GetAnimatorReferenceWidth(bool hasAnimator, Rect position) {
            return hasAnimator? position.width * 0.3f : position.width;
        }
        
        protected virtual float GetParameterDropdownWidth(float animatorReferenceWidth, Rect position) {
            return position.width - animatorReferenceWidth;
        }

        protected virtual void DrawAdditionalProperties(Rect position, SerializedProperty property) {
            //Empty Default
        }

        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            position = EditorGUI.PrefixLabel(position, GUIUtility.GetControlID(FocusType.Passive), label);            
            position.height = EditorGUIUtility.singleLineHeight;
            //SerializedProperty animatorReferenceProp = property.FindPropertyRelative("m_animator");
            SerializedProperty idProp = property.FindPropertyRelative("m_id");
            SerializedProperty nameProp = property.FindPropertyRelative("m_name");

            string storedParameterName = nameProp.stringValue;

            Animator animator = GetAnimatorProperty(property).objectReferenceValue as Animator;
            bool hasAnimator = animator != null;
            
            AnimatorControllerParameter[] animParameters = new AnimatorControllerParameter[0];
            
            if (hasAnimator) {
                animParameters = animator.parameters;
            }

            float animatorReferenceWidth = GetAnimatorReferenceWidth(hasAnimator, position);
            //hasAnimator? position.width * 0.3f : position.width;

            float parameterDropdownWidth = GetParameterDropdownWidth(animatorReferenceWidth, position);
            //position.width - animatorReferenceWidth;

            // Create Rects based on calculated sizes
            Rect animatorReferencePosition = new Rect(position.x, position.y,
                animatorReferenceWidth, position.height);
            
            Rect parameterNameDropdownPosition = new Rect(
                animatorReferencePosition.x + animatorReferenceWidth,
                position.y,
                parameterDropdownWidth, position.height);
            

            EditorGUI.ObjectField(animatorReferencePosition, GetAnimatorProperty(property), GUIContent.none);

            if (hasAnimator && !animParameters.Select(param => param.name).Contains(storedParameterName))
            {
                Debug.LogError("Stored parameter name " + storedParameterName + " doesn't exist for this animator");
                nameProp.stringValue = "";
            }
            
            IEnumerable<string> parameterNames = animParameters.Select(param => param.name);

            if (storedParameterName == null ||
                (string.IsNullOrEmpty(nameProp.stringValue))
                && parameterNames.Any())
            {
                nameProp.stringValue = parameterNames.First();
            }

            if (GUI.Button(parameterNameDropdownPosition,
                string.IsNullOrEmpty(nameProp.stringValue) ? "Select parameter" : nameProp.stringValue,
                EditorStyles.popup))
            {
                GenericMenu menu = new GenericMenu();
                foreach (string parameter in parameterNames)
                {
                    menu.AddItem(new GUIContent(parameter),
                        parameter == nameProp.stringValue,
                        enumValue =>
                        {
                            nameProp.stringValue = parameter;
                            property.serializedObject.ApplyModifiedProperties(); //Should be specifically string prop?
                            idProp.intValue = Animator.StringToHash(nameProp.stringValue);
                            idProp.serializedObject.ApplyModifiedProperties();
                            Debug.Log("Set hash for parameter " + nameProp.stringValue + " to: " + idProp.intValue);
                        }
                        , parameter);
                }

                menu.ShowAsContext();
            }
            Rect additionalPropertiesRect = new Rect(
                parameterNameDropdownPosition.x + parameterNameDropdownPosition.width,
                position.y,
                position.width - animatorReferenceWidth - parameterDropdownWidth,
                position.height);
            DrawAdditionalProperties(additionalPropertiesRect, property);
            property.serializedObject.ApplyModifiedProperties();
            EditorGUI.EndProperty();
        }
    }
}*/

/*using UnityEngine;
using Object = UnityEngine.Object;
using UnityEditor;
using System;
using System.Collections.Generic;
using Upopa;

namespace Upopa {
[CustomPropertyDrawer(typeof(ServiceLocator.Service))]
public class ServicePropertyDrawer : PropertyDrawer {
    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label) {
        EditorGUI.BeginProperty(position, label, property);

        position.height = EditorGUIUtility.singleLineHeight;
        var serviceReferenceProp = property.FindPropertyRelative("reference");
        var mockReferenceProp = property.FindPropertyRelative("mockReference");
        var useMockProp = property.FindPropertyRelative("useMock");
        var storedTypeProp = property.FindPropertyRelative("storedType");

        var storedType = ServiceLocator.ParseType(storedTypeProp.stringValue);

        bool hasReference = serviceReferenceProp.objectReferenceValue != null;
        bool hasMockReference = mockReferenceProp.objectReferenceValue != null;

        float referenceWidth = 0f;
        float mockReferenceWidth = 0f;
        float useMockWidth = 0f;
        float typeDropdownWidth = 0f;

        if (hasReference) { // Decide what the sizes of the elements should be
            if (hasMockReference) {
                referenceWidth = position.width * 0.5f;
                useMockWidth = 35f;
                mockReferenceWidth = position.width * 0.3f;
            } else {
                referenceWidth = position.width * 0.5f;
                mockReferenceWidth = Mathf.Max(position.width * 0.15f, 40f);
            }
            mockReferenceWidth = Mathf.Max(mockReferenceWidth, 60f);
            typeDropdownWidth = position.width - referenceWidth - useMockWidth - mockReferenceWidth;
        } else {
            referenceWidth = position.width;
        }

        // Create Rects based on calculated sizes
        var referencePosition = new Rect(position.x, position.y, referenceWidth, position.height);
        var mockPosition = new Rect(referencePosition.x + referencePosition.width - 10f, position.y, mockReferenceWidth, position.height);
        var useMockPosition = new Rect(mockPosition.x + mockPosition.width, position.y, useMockWidth, position.height);
        var typeDropdownPosition = new Rect(useMockPosition.x + useMockWidth, position.y, typeDropdownWidth, position.height);

        // Get references of regular service and mock version + validations
        EditorGUI.ObjectField(referencePosition, serviceReferenceProp, GUIContent.none);
        var serviceRef = serviceReferenceProp.objectReferenceValue as ScriptableObject;

        if (serviceRef == null) {
            serviceReferenceProp.objectReferenceValue = null;
            return;
        }

        EditorGUI.ObjectField(mockPosition, mockReferenceProp, GUIContent.none);
        var mockRef = mockReferenceProp.objectReferenceValue as ScriptableObject;

        if (mockRef && !storedType.IsInstanceOfType(mockRef)) {
            Debug.LogError("Given mock is not of type " + storedTypeProp.stringValue + " deleting.");
            mockReferenceProp.objectReferenceValue = null;
        }

        // Create use mock checkmark if both references are present
        if (hasReference && hasMockReference) {
            EditorGUI.PropertyField(useMockPosition, useMockProp, GUIContent.none);
        }

        var typesFound = new List<Type>();

        typesFound.AddRange(GetAllParentTypes(serviceRef.GetType()));
        Type[] interfaces = serviceRef.GetType().GetInterfaces();
        typesFound.AddRange(interfaces);

        if (storedType == null ||
            (string.IsNullOrEmpty(storedTypeProp.stringValue)
                || !storedType.IsInstanceOfType(serviceRef))
            && typesFound.Count > 0) {
            storedTypeProp.stringValue = typesFound[0].ToString();
        }

        if (GUI.Button(typeDropdownPosition, string.IsNullOrEmpty(storedTypeProp.stringValue) ? "Select type" : storedTypeProp.stringValue, EditorStyles.popup)) {
            var menu = new GenericMenu();
            foreach (var foundType in typesFound) {
                menu.AddItem(new GUIContent(foundType.ToString()), 
                    foundType.ToString() == storedTypeProp.stringValue, 
                    enumValue => {
                        storedTypeProp.stringValue = foundType.ToString();
                        property.serializedObject.ApplyModifiedProperties(); //Should be specifically string prop?
                    }
                    , foundType.ToString());
            }
            menu.ShowAsContext();
        }
        property.serializedObject.ApplyModifiedProperties();
        EditorGUI.EndProperty();

    }

    public static List<Type> GetAllParentTypes(Type type, bool includeBaseClasses = false) {
        var currentType = type;
        var typesFound = new List<Type>();
        while (currentType != null && !IsBaseClass(currentType) || (includeBaseClasses && currentType != null)) {
            typesFound.Add(currentType);
            currentType = currentType.BaseType;
        }
        return typesFound;
    }

    public static bool IsBaseClass(Type type) {
        if (type == typeof(Object)
            || type == typeof(System.Object)
            || type == typeof(ScriptableObject)
            || type == typeof(UpopaScriptableObject)) {
            return true;
        } else {
            return false;
        }
    }
}
}*/