namespace RibCageGames.Animation.Editor
{   
    using System.Linq;
    using UnityEditor;
    using UnityEditor.Animations;
    using UnityEngine;

    [CustomPropertyDrawer(typeof(AnimatorParameterControlAttribute), true)]
    public class AnimatorParameterControlPropertyDrawer : PropertyDrawer
    {

        protected Animator m_animator = null;
        protected AnimatorControllerParameter[] m_parameters = null;
        AnimatorControllerParameter m_currentParameter = null;

        private float m_propertyHeight = 16;

        private Animator GetAnimator(SerializedProperty property)
        {
            if (m_animator == null)
            {
                SerializedProperty serializedProperty = property.serializedObject.GetIterator();
                do
                {
                    if (serializedProperty.name == Attribute.AnimatorVarName)
                    {
                        Animator candidate = (Animator) (serializedProperty.objectReferenceValue);
                        SerializedProperty innerSerializedProperty = serializedProperty;
                        
                        do
                        {
                            if (innerSerializedProperty.propertyPath == property.propertyPath)
                            {
                                m_animator = candidate;
                                return m_animator;
                            }
                        }
                        while (innerSerializedProperty.Next(true));
                    }
                }
                while (serializedProperty.Next(true));
            }

            return m_animator;
            
            //return (Animator) property.serializedObject.FindProperty(Attribute.AnimatorVarName).objectReferenceValue;
        }

        private AnimatorParameterControlAttribute Attribute => (AnimatorParameterControlAttribute) attribute;

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            return m_propertyHeight;
        }

        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {

            EditorGUI.BeginProperty(position, label, property);

            SerializedProperty idProp = property.FindPropertyRelative("m_parameterHash");
            SerializedProperty typeProp = property.FindPropertyRelative("m_parameterType");
            SerializedProperty resetProp = property.FindPropertyRelative("m_resetParameter");

            SerializedProperty floatValueProp = property.FindPropertyRelative("m_floatValue");
            SerializedProperty intValueProp = property.FindPropertyRelative("m_intValue");
            SerializedProperty boolValueProp = property.FindPropertyRelative("m_boolValue");

            m_propertyHeight = EditorGUIUtility.singleLineHeight;
            m_animator = GetAnimator(property);

            if (m_animator == null)
            {
                Debug.LogError($"Animator reference is null");
                return;
            }

            property.serializedObject.ApplyModifiedProperties();
            AnimatorController animController = (AnimatorController) AssetDatabase.LoadAssetAtPath(
                AssetDatabase.GetAssetPath(m_animator.runtimeAnimatorController), typeof(AnimatorController));
            
            if (animController == null) //Get base if controller is an override 
            {   
                animController = (AnimatorController) AssetDatabase.LoadAssetAtPath(
                    AssetDatabase.GetAssetPath(((AnimatorOverrideController) m_animator.runtimeAnimatorController).runtimeAnimatorController), typeof(AnimatorController));
            }

            Rect parameterNameDropdownPosition =
                new Rect(position.x, position.y, position.width * 0.75f, EditorGUIUtility.singleLineHeight);


            if (m_animator != null)
            {

                //Parameter selection dropdown
                AnimatorControllerParameter[] animParameters = animController.parameters;
                if (animParameters != null && animParameters.Length > 0)
                {
                    m_parameters = animParameters;
                }
                else if (m_parameters == null)
                {
                    m_parameters = new AnimatorControllerParameter[0];
                }

                for (int i = 0; i < m_parameters.Length; i++)
                {
                    if (m_parameters[i].nameHash == idProp.intValue)
                    {
                        m_currentParameter = m_parameters[i];
                    }
                }

                if (m_currentParameter == null && m_parameters.Length > 0)
                {
                    m_currentParameter = m_parameters[0];
                }

                if (m_currentParameter == null)
                {
                    m_propertyHeight += EditorGUIUtility.singleLineHeight + 2;

                    EditorGUI.indentLevel--;

                    EditorGUI.EndProperty();
                    return;
                }

                string parameterName = m_currentParameter.name;


                if (GUI.Button(parameterNameDropdownPosition,
                    string.IsNullOrEmpty(parameterName) ? "Parameter not present in animator" : parameterName,
                    EditorStyles.popup))
                {
                    GenericMenu menu = new GenericMenu();
                    foreach (AnimatorControllerParameter parameter in m_parameters)
                    {
                        menu.AddItem(new GUIContent(parameter.name),
                            parameter == m_currentParameter,
                            enumValue =>
                            {
                                //nameProp.stringValue = parameter;
                                //property.serializedObject.ApplyModifiedProperties(); //Should be specifically string prop?
                                m_currentParameter = parameter;
                                idProp.intValue = m_currentParameter.nameHash;
                                typeProp.enumValueIndex = typeProp.enumDisplayNames
                                    .TakeWhile(t => !t.Equals(m_currentParameter.type.ToString())).Count();
                                
                                //Debug.Log("type is: " + m_currentParameter.type + " with index: " + typeProp.enumValueIndex);

                                //Debug.Log("Set hash for parameter " + parameter + " to: " + idProp.intValue
                                //          + " type is: " + typeProp.enumNames[typeProp.enumValueIndex]);
                                property.serializedObject.ApplyModifiedProperties();
                            }, parameter);
                    }

                    menu.ShowAsContext();
                }

                EditorGUI.LabelField(
                    new Rect(position.x, position.y + m_propertyHeight + 2, position.width / 4f,
                        EditorGUIUtility.singleLineHeight),
                    m_currentParameter.type.ToString());

                typeProp.enumValueIndex = typeProp.enumDisplayNames
                    .TakeWhile(t => !t.Equals(m_currentParameter.type.ToString())).Count();

                switch (m_currentParameter.type)
                {
                    case AnimatorControllerParameterType.Float:
                        EditorGUI.PropertyField(
                            new Rect(position.x + position.width / 4f, position.y + m_propertyHeight + 2,
                                position.width / 2f, EditorGUIUtility.singleLineHeight),
                            floatValueProp);
                        //m_propertyHeight += EditorGUIUtility.singleLineHeight;
                        break;
                    case AnimatorControllerParameterType.Int:
                        EditorGUI.PropertyField(
                            new Rect(position.x + position.width / 4f, position.y + m_propertyHeight + 2,
                                position.width / 2f, EditorGUIUtility.singleLineHeight),
                            intValueProp);
                        //m_propertyHeight += EditorGUIUtility.singleLineHeight;
                        break;
                    case AnimatorControllerParameterType.Bool:
                        EditorGUI.PropertyField(
                            new Rect(position.x + position.width / 4f, position.y + m_propertyHeight + 2,
                                position.width / 2f, EditorGUIUtility.singleLineHeight),
                            boolValueProp);
                        //m_propertyHeight += EditorGUIUtility.singleLineHeight;
                        break;
                    case AnimatorControllerParameterType.Trigger:
                        break;
                }
                
                resetProp.boolValue = EditorGUI.ToggleLeft(
                    new Rect(position.x + position.width * 0.75f, position.y, position.width * 0.25f, EditorGUIUtility.singleLineHeight),
                    "Reset",
                    resetProp.boolValue);

                m_propertyHeight += EditorGUIUtility.singleLineHeight + 2;
                
                EditorGUI.EndProperty();
                //property.serializedObject.ApplyModifiedProperties();
            }
        }
    }
}
