namespace RibCageGames.UI
{
    using UnityEngine;
    using TMPro;
    using UnityEngine.UI;

    public class DialogPopup : MonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI m_mainText;
        [SerializeField] private TextMeshProUGUI m_titleTextLeft;
        [SerializeField] private TextMeshProUGUI m_titleTextRight;
        [SerializeField] private TextMeshProUGUI m_continuePromptText;
        [SerializeField] private Image m_titleTextImageLeft;
        [SerializeField] private Image m_titleTextImageRight;
        [SerializeField] private Image m_backgroundImage;
        [SerializeField] private Image m_characterImageLeft;
        [SerializeField] private Image m_characterImageRight;
        [SerializeField] private Image m_continuePrompt;
        [SerializeField] private GameObject m_continueSymbolJump;
        [SerializeField] private GameObject m_continueSymbolAttack;
        [SerializeField] private GameObject m_continueSymbolDodge;
        [SerializeField] private GameObject m_continueSymbolSpecial;
        
        public TextMeshProUGUI MainText => m_mainText;
        public Image BackgroundImage => m_backgroundImage;
        public Image CharacterImageLeft => m_characterImageLeft;
        public Image CharacterImageRight => m_characterImageRight;
        public TextMeshProUGUI TitleTextLeft => m_titleTextLeft;
        public TextMeshProUGUI TitleTextRight => m_titleTextRight;
        public Image TitleTextImageLeft => m_titleTextImageLeft;
        public Image TitleTextImageRight => m_titleTextImageRight;
        
        public Image ContinuePrompt => m_continuePrompt;
        public GameObject ContinueSymbolJump => m_continueSymbolJump;
        public GameObject ContinueSymbolAttack => m_continueSymbolAttack;
        public GameObject ContinueSymbolDodge => m_continueSymbolDodge;
        public GameObject ContinueSymbolSpecial => m_continueSymbolSpecial;
        public TextMeshProUGUI ContinuePromptText => m_continuePromptText;
    }
}