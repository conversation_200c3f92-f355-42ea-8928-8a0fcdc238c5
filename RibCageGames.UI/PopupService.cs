using Cysharp.Threading.Tasks;

namespace RibCageGames.UI
{
    using System.Linq;
    using Music;
    using System.Collections.Generic;
    using Input;
    using Base;
    using System;
    using UnityEngine;

    [CreateAssetMenu(fileName = "PopupService", menuName = "RibCageGames/Services/UI/PopupService")]
    public class PopupService : BaseService
    {   
        [SerializeField] private float m_blackBarShowTime;
        [SerializeField] private float m_blackBarHideTime;
        
        public PopupSettings CurrentPopupSettings { get; private set; }
        
        private PopupServiceCanvas m_canvasHolder;
        private ServiceReference<InputService> m_inputService = new();
        private ServiceReference<MonoService> m_monoService = new();
        private ServiceReference<MusicSystem> m_musicSystem = new();
        private ServiceReference<BeatSystem> m_beatSystem = new();

        private MainGameUI m_mainGameUI;

        public void ShowDialogPopup(PopupSettings popupSettings)
        {
            ShowDialogPopup(popupSettings, null);
        }

        public void ShowDialogWithDelay(float delay, DialogPopupSettings dialog, Action onComplete)
        {
            MonoProcess.WaitForSecondsProcess(delay).Do(
                () =>
                {
                    ShowDialogPopup(dialog, onComplete);
                });
        }

        public async UniTask ShowDialogWithDelayAsync(float delay, DialogPopupSettings dialog)
        {
            await UniTask.WaitForSeconds(delay);
            
            UniTaskCompletionSource completionSource = new UniTaskCompletionSource();
            
            ShowDialogPopup(dialog, () =>
            {
                completionSource.TrySetResult();
            });
            
            await completionSource.Task;
        }

        public void ShowDialogPopup(PopupSettings popupSettings, Action dialogComplete)
        {
            if (popupSettings.PauseGameTime)
            {
                m_monoService.Value.SetTimeScale(0f);
                m_beatSystem.Value.PauseMusic();
                
                ShowDialogPopupInternal(popupSettings, () =>
                {
                    m_monoService.Value.SetTimeScale(1f);
                    m_beatSystem.Value.UnPauseMusic();
                    dialogComplete?.Invoke();
                });
            }
            else
            {
                ShowDialogPopupInternal(popupSettings, dialogComplete);
            }
        }

        public void ShowBlackBarsImmediate(bool show)
        {
            foreach (RectTransform rect in m_canvasHolder.BlackBars)
            {
                rect.sizeDelta = new Vector2(rect.sizeDelta.x, show ? m_canvasHolder.BlackBarHeight : 0f);
            }
        }
        
        public void ShowBlackBars(bool show)
        {
            float elapsedTime = 0f;
            float duration = show ? m_blackBarShowTime : m_blackBarHideTime;
            MonoProcess.New()
                .WaitWhile(() =>
                {
                    elapsedTime += Time.deltaTime;
                    float part = elapsedTime / duration;
                    foreach (RectTransform rect in m_canvasHolder.BlackBars)
                    {
                        rect.sizeDelta = new Vector2(rect.sizeDelta.x, 
                            (show ? part : 1f - part)
                            * m_canvasHolder.BlackBarHeight);
                    }

                    return elapsedTime < duration;
                })
                .Do(() =>
                {
                    foreach (RectTransform rect in m_canvasHolder.BlackBars)
                    {
                        rect.sizeDelta = new Vector2(rect.sizeDelta.x, show ? m_canvasHolder.BlackBarHeight : 0f);
                    }
                });
        }
        
        private void ShowDialogPopupInternal(PopupSettings popupSettings, Action dialogComplete)
        {
            DialogPopup popup = m_canvasHolder.DialogPopup;
            popup.MainText.text = popupSettings.PopupText;
            popup.MainText.spriteAsset = popupSettings.TextSpriteAsset;
            popup.BackgroundImage.sprite = popupSettings.BackgroundImageSprite;
            
            Sprite leftSprite = ((DialogPopupSettings) popupSettings).FloatingImageSpriteLeft;
            Sprite rightSprite = ((DialogPopupSettings) popupSettings).FloatingImageSpriteRight;
            
            CurrentPopupSettings = popupSettings;
            
            if (!string.IsNullOrWhiteSpace(popupSettings.TitleTextLeft))
            {
                popup.TitleTextLeft.text = popupSettings.TitleTextLeft;
                popup.TitleTextImageLeft.gameObject.SetActive(true);
            }
            else
            {
                popup.TitleTextImageLeft.gameObject.SetActive(false);
            }
            
            if (!string.IsNullOrWhiteSpace(popupSettings.TitleTextRight))
            {
                popup.TitleTextRight.text = popupSettings.TitleTextRight;
                popup.TitleTextImageRight.gameObject.SetActive(true);
            }
            else
            {
                popup.TitleTextImageRight.gameObject.SetActive(false);
            }
            
            if (leftSprite != null)
            {
                popup.CharacterImageLeft.sprite = leftSprite;
                popup.CharacterImageLeft.GetComponent<RectTransform>().sizeDelta = new Vector2(leftSprite.rect.width, leftSprite.rect.height); 
            }

            popup.CharacterImageLeft.enabled = leftSprite != null;
            
            if (rightSprite != null)
            {
                popup.CharacterImageRight.sprite = rightSprite;
                popup.CharacterImageRight.GetComponent<RectTransform>().sizeDelta = new Vector2(rightSprite.rect.width, rightSprite.rect.height);
            }

            popup.CharacterImageRight.enabled = rightSprite != null;


            PopupClosingStrategy continueButton =
                popupSettings.ClosingStrategies.FirstOrDefault(str => str != PopupClosingStrategy.Duration);
            if (popupSettings.ClosingStrategies.Any(str => str != PopupClosingStrategy.Duration))
            {
                popup.ContinuePrompt.gameObject.SetActive(true);
                popup.ContinuePromptText.text = popupSettings.ContinueText;
                popup.ContinueSymbolJump.SetActive(continueButton == PopupClosingStrategy.AnyPress || continueButton == PopupClosingStrategy.JumpPress);
                popup.ContinueSymbolAttack.SetActive(continueButton == PopupClosingStrategy.LightPress);
                popup.ContinueSymbolDodge.SetActive(continueButton == PopupClosingStrategy.DodgePress);
                popup.ContinueSymbolSpecial.SetActive(continueButton == PopupClosingStrategy.SpecialPress);

                if (popupSettings.UsesClosePredicate)
                {
                    popup.ContinuePrompt.gameObject.SetActive(false);
                    MonoProcess.New().WaitFor(
                        () => popupSettings.ClosingPredicate != null && popupSettings.ClosingPredicate(), popupSettings.MaximalDuration)
                        .Do(() => { popup.ContinuePrompt.gameObject.SetActive(true); });
                }
            }
            else
            {
                popup.ContinuePrompt.gameObject.SetActive(false);
            }
            
            popupSettings.OnStart?.Invoke();
            MonoProcess closeProcess = MonoProcess.WaitForSecondsUnscaledProcess(popupSettings.MinimalDuration);
            
            List<Action> unregisters = new List<Action>();

            if (popupSettings.BlockInput)
            {
                HashSet<PlayerInputActions> unlockingInputs = new HashSet<PlayerInputActions>();
                HashSet<PlayerInputActions> excludedInputs = null;

                if (popupSettings.ExcludedInputs.Count > 0)
                {
                    excludedInputs = new HashSet<PlayerInputActions>();
                    foreach (PlayerInputActions input in popupSettings.ExcludedInputs)
                    {
                        excludedInputs.Add(input);
                    }   
                }

                foreach (PopupClosingStrategy strategy in popupSettings.ClosingStrategies)
                {
                    switch (strategy)
                    {
                        case PopupClosingStrategy.JumpPress:
                            unlockingInputs.Add(PlayerInputActions.Jump);
                            break;
                        case PopupClosingStrategy.DodgePress:
                            unlockingInputs.Add(PlayerInputActions.Dodge);
                            break;
                        case PopupClosingStrategy.LightPress:
                            unlockingInputs.Add(PlayerInputActions.LightAttack);
                            break;
                        case PopupClosingStrategy.SpecialPress:
                            unlockingInputs.Add(PlayerInputActions.Special);
                            break;
                        case PopupClosingStrategy.AnyPress:
                            unlockingInputs.Add(PlayerInputActions.LightAttack);
                            unlockingInputs.Add(PlayerInputActions.Special);
                            unlockingInputs.Add(PlayerInputActions.Dodge);
                            unlockingInputs.Add(PlayerInputActions.Jump);
                            break;
                    }
                }

                m_inputService.Value.DisableInput(null);
                closeProcess.Do(() =>
                {
                    m_inputService.Value.DisableInput(unlockingInputs, popupSettings.InputPassThrough, excludedInputs);
                });
            }
            
            void ClosePopupCallback()
            {
                // Check if there's a closing predicate and if it allows closing
                if (popupSettings.ClosingPredicate != null && !popupSettings.ClosingPredicate())
                {
                    // Predicate failed, don't close the popup
                    return;
                }

                ClosePopup(popup);
                closeProcess?.Stop();
                popupSettings.OnComplete?.Invoke();
                if (popupSettings.FollowingPopup != null)
                {
                    ShowDialogPopup(popupSettings.FollowingPopup, dialogComplete);
                }
                else
                {
                    dialogComplete?.Invoke();
                }

                foreach (Action unregister in unregisters)
                {
                    unregister.Invoke();
                }
            }

            foreach (PopupClosingStrategy strategy in popupSettings.ClosingStrategies)
            {
                Action unregister = () => { };
                switch (strategy)
                {
                    case PopupClosingStrategy.Duration:
                        closeProcess.
                            WaitForSecondsUnscaled(popupSettings.MaximalDuration - popupSettings.MinimalDuration)
                            .Do(ClosePopupCallback);
                        break;
                    case PopupClosingStrategy.AnyPress:
                        
                        void Listener(PlayerInputActions input, PlayerInputTypes type)
                        {
                            ClosePopupCallback();
                        }

                        closeProcess.WaitForFrame(1).Do(() =>
                        {
                            m_inputService.Value.GeneralInputEvent.AddListener(Listener);
                        });
                        
                        unregister = () => m_inputService.Value.GeneralInputEvent.RemoveListener(Listener);
                        break;
                    case PopupClosingStrategy.LightPress:
                        unregister = m_inputService.Value.RegisterButtonListener(PlayerInputActions.LightAttack, ClosePopupCallback, closeProcess);
                        break;
                    case PopupClosingStrategy.SpecialPress:
                        unregister = m_inputService.Value.RegisterButtonListener(PlayerInputActions.Special, ClosePopupCallback, closeProcess);
                        break;
                    case PopupClosingStrategy.DodgePress:
                        unregister = m_inputService.Value.RegisterButtonListener(PlayerInputActions.Dodge, ClosePopupCallback, closeProcess);
                        break;
                    case PopupClosingStrategy.JumpPress:
                        unregister = m_inputService.Value.RegisterButtonListener(PlayerInputActions.Jump, ClosePopupCallback, closeProcess);
                        break;
                }
                unregisters.Add(unregister);
                popup.gameObject.SetActive(true);
            }
        }

        private void ClosePopup(DialogPopup popup)
        {
            popup.gameObject.SetActive(false);
            m_inputService.Value.EnableInput();
        }

        public override void Init(GameObject servicePrefab = null)
        {
            m_canvasHolder = servicePrefab.GetComponent<PopupServiceCanvas>();
        }

        public override void StartService(MonoInjector injector)
        {
        }

        public override void Dispose()
        {

        }

        public void SetMainGameUI(MainGameUI mainGameUi)
        {
            m_mainGameUI = mainGameUi;
        }
        
        public bool TryGetElement<T>(out T element) where T : UnityEngine.Object
        {
            return m_mainGameUI.TryGetElement(typeof(T), out element);
        }
        
        public bool TryGetElement<T>(Type key, out T element) where T : UnityEngine.Object
        {
            return m_mainGameUI.TryGetElement(key, out element);
        }
        
        public void ActivateMainGameUI()
        {
            m_mainGameUI.ActivateMainGameUI();
        }
        
        public void ActivateMainGameUI(bool activateElements)
        {
            m_mainGameUI.ActivateMainGameUI(activateElements);
        }
        
        public void DeactivateMainGameUI()
        {
            m_mainGameUI.DeactivateMainGameUI();
        }
        
        public void ActivateTutorials(){
            m_mainGameUI.ActivateTutorials();
        }
        public void ActivateMetronomeSelection(){
            m_mainGameUI.ActivateMetronomeSelectionPrompts();
            m_mainGameUI.ActivateMetronome();
        }
        
        public void DeactivateMetronomeSelection(){
            m_mainGameUI.DeactivateMetronomeSelectionPrompts();
        }

        public void ActivateClickerSelection(){
            m_mainGameUI.ActivateClickerSelectionPrompts();
        }
        
        public void DeactivateClickerSelection(){
            m_mainGameUI.DeactivateClickerSelectionPrompts();
        }

        
        public void ActivateMetronome(){
            m_mainGameUI.ActivateMetronome();
        }
        public void ActivateHealthBar(){
            m_mainGameUI.ActivateHealthBar();
        }
        public void ActivateStyleBar(){
            m_mainGameUI.ActivateStyleBar();
        }
        public void DeactivateTutorials(){
            m_mainGameUI.DeactivateTutorials();
        }
        public void DeactivateMetronome(){
            m_mainGameUI.DeactivateMetronome();
        }
        public void DeactivateHealthBar(){
            m_mainGameUI.DeactivateHealthBar();
        }
        public void DeactivateStyleBar(){
            m_mainGameUI.DeactivateStyleBar();
        }

        public void UpdateUITime(float timeInGame)
        {
            m_mainGameUI.UpdateTimer((int) timeInGame);
        }

        public void SetTimerEnabled(bool value)
        {
            m_mainGameUI.SetTimerEnabled(value);
        }
    }
}