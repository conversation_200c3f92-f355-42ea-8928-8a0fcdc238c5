namespace RibCageGames.UI
{
    using UnityEngine;
    using System.Collections.Generic;

    public class PopupServiceCanvas : MonoBehaviour
    {
        [SerializeField] private Canvas m_canvas;
        [SerializeField] private DialogPopup m_dialogPopup;
        [SerializeField] private List<RectTransform> m_blackBars;
        [SerializeField] private float m_blackBarHeight;
        
        public Canvas Canvas => m_canvas;
        public DialogPopup DialogPopup => m_dialogPopup;
        public List<RectTransform> BlackBars => m_blackBars;
        public float BlackBarHeight => m_blackBarHeight;
    }
}