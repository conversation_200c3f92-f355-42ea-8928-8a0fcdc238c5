{"name": "RibCageGames.UI", "rootNamespace": "", "references": ["RibCageGames.Base", "RibCageGames.Editor", "RibCageGames.Animation", "RibCageGames.MonoUtils", "RibCageGames.Input", "Unity.TextMeshPro", "UniTask", "RibCageGames.Sound", "RibCageGames.Music"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}