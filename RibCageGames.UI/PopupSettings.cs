using System;
using RibCageGames.Input;
using TMPro;
using UnityEngine.Events;
using UnityEngine.Serialization;

namespace RibCageGames.UI
{
    
    using System.Collections.Generic;
    using UnityEngine;

    [CreateAssetMenu(fileName = "PopupSettings", menuName = "RibCageGames/Settings/UI/PopupSettings")]
    public class PopupSettings : ScriptableObject
    {
        [SerializeField] private bool m_pauseGameTime;
        [SerializeField] private bool m_blockInput;
        [SerializeField] private bool m_inputPassThrough;
        [SerializeField] private Sprite m_backgroundImageSprite;
        [SerializeField] [TextArea] private string m_popupText;
        [SerializeField] private string m_titleTextLeft;
        [SerializeField] private string m_titleTextRight;
        [SerializeField] private TMP_SpriteAsset m_textSpriteAsset;
        [SerializeField] private float m_minimalDuration;
        [SerializeField] private float m_maximalDuration;
        [SerializeField] private List<PopupClosingStrategy> m_closingStrategies;
        [SerializeField] private List<PlayerInputActions> m_excludedInputs;
        [SerializeField] private string m_continueText;
        [SerializeField] private bool m_requireOnBeat;
        [SerializeField] private UnityEvent m_onStart;
        [SerializeField] private UnityEvent m_onComplete;
        [FormerlySerializedAs("m_followingDialog")] [SerializeField] private PopupSettings followingPopup;
        [SerializeField] private bool m_usesClosePredicate;

        // Runtime predicate for conditional closing
        [System.NonSerialized] private Func<bool> m_closingPredicate;

        public bool PauseGameTime => m_pauseGameTime;
        public bool BlockInput => m_blockInput;
        public Sprite BackgroundImageSprite => m_backgroundImageSprite;
        public string PopupText => m_popupText;
        public string TitleTextLeft => m_titleTextLeft;
        public string TitleTextRight => m_titleTextRight;
        public TMP_SpriteAsset TextSpriteAsset => m_textSpriteAsset;
        public float MinimalDuration => m_minimalDuration;
        public float MaximalDuration => m_maximalDuration;
        public List<PopupClosingStrategy> ClosingStrategies => m_closingStrategies;
        public List<PlayerInputActions> ExcludedInputs => m_excludedInputs;
        public bool UsesClosePredicate => m_usesClosePredicate;
        public UnityEvent OnStart => m_onStart;
        public UnityEvent OnComplete => m_onComplete;
        public bool RequireOnBeat => m_requireOnBeat;
        public string ContinueText => m_continueText;
        public bool InputPassThrough => m_inputPassThrough;
        public PopupSettings FollowingPopup => followingPopup;

        /// <summary>
        /// Gets the closing predicate. If null, popup can close normally.
        /// If not null, popup can only close when predicate returns true.
        /// </summary>
        public Func<bool> ClosingPredicate => m_closingPredicate;

        /// <summary>
        /// Sets a predicate that must return true before the popup can be closed.
        /// Useful for ensuring certain interactions have occurred before allowing closure.
        /// </summary>
        /// <param name="predicate">Function that returns true when popup is allowed to close</param>
        public void SetClosingPredicate(Func<bool> predicate)
        {
            m_closingPredicate = predicate;
        }

        /// <summary>
        /// Clears the closing predicate, allowing normal popup closing behavior.
        /// </summary>
        public void ClearClosingPredicate()
        {
            m_closingPredicate = null;
        }
    }

    public enum PopupClosingStrategy
    {
        Duration,
        AnyPress,
        LightPress,
        HeavyPress,
        SpecialPress,
        DodgePress,
        JumpPress,
    }
}