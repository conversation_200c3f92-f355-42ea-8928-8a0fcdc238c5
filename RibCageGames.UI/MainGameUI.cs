using System;
using System.Collections.Generic;
using TMPro;

namespace RibCageGames.UI
{
    using UnityEngine;
    using Base;

    public class MainGameUI : MonoBehaviour
    {
        [SerializeField] private Canvas m_mainUICanvas;
        [SerializeField] private GameObject m_tutorials;
        [SerializeField] private GameObject m_healthBar;
        [SerializeField] private GameObject m_styleBar;
        [SerializeField] private GameObject m_metronome;
        [SerializeField] private GameObject m_metronomeSelectionPrompts;
        [SerializeField] private GameObject m_clickerSelectionPrompts;
        [SerializeField] private TextMeshProUGUI m_timeText;
        [SerializeField] private int m_demoTime;
        [SerializeField] private List<Object> m_genericElements;
        
        private static Dictionary<Type, Object> m_uiElementDict;
        
        public bool m_timerBlockEnabled;

        public void SetTimerBlockEnabled(bool value)
        {
            m_timerBlockEnabled = value;
        }
        
        public void SetTimerEnabled(bool value)
        {
            //m_timeText.gameObject.SetActive(value);
            m_timerBlockEnabled = value;
        }
        
        public Canvas MainUICanvas => m_mainUICanvas;

        private void Awake()
        {
            DeactivateMainGameUI();
            ServiceLocator.EditorGet<PopupService>().SetMainGameUI(this);

            m_uiElementDict = new Dictionary<Type, Object>();
            foreach (Object element in m_genericElements)
            {
                m_uiElementDict.Add(element.GetType(), element);
            }
        }

        public bool TryGetElement<T>(Type key, out T element) where T : Object
        {
            bool found = m_uiElementDict.TryGetValue(key, out Object value);
            if (found)
            {
                element = (T)value;
            }
            else
            {
                element = null;
            }

            return found;
        }

        public void UpdateTimer(int timeInGame)
        {
            if(!m_timerBlockEnabled) { return ;}
            if (timeInGame > m_demoTime)
            {
                //m_endDemoScreen.SetActive(m_timerBlockEnabled);
                return;
            }
            int secondsLeft = m_demoTime - timeInGame;
            int minutesInGame = secondsLeft / 60;
        
            secondsLeft %= 60;
            
            string minutes = minutesInGame < 10 ? ("0" + minutesInGame) : (minutesInGame.ToString());
            string seconds = secondsLeft < 10 ? ("0" + secondsLeft) : (secondsLeft.ToString());
            
            m_timeText.text = $"{minutes}:{seconds}";
        }

        public void ActivateMainGameUI()
        {
            MainUICanvas.enabled = true;
        }

        public void ActivateMainGameUI(bool activateElements)
        {
            MainUICanvas.enabled = true;
            if (activateElements)
            {
                ActivateTutorials();
                ActivateMetronome();
                ActivateHealthBar();
                ActivateStyleBar();
            }
            else
            {
                DeactivateTutorials();
                DeactivateMetronome();
                DeactivateHealthBar();
                DeactivateStyleBar();
            }
        }
        
        public void ActivateHealthBar()
        {
            m_healthBar.SetActive(true);
        }
        
        public void ActivateMetronome()
        {
            m_metronome.SetActive(true);
        }
        
        public void ActivateStyleBar()
        {
            m_styleBar.SetActive(true);
        }
        
        public void ActivateTutorials()
        {
            m_tutorials.SetActive(true);
        }

        public void DeactivateMainGameUI()
        {
            MainUICanvas.enabled = false;
            HideEndGameScreen();
        }
        
        public void DeactivateHealthBar()
        {
            m_healthBar.SetActive(false);
        }
        
        public void DeactivateMetronome()
        {
            m_metronome.SetActive(false);
        }
        
        public void DeactivateStyleBar()
        {
            m_styleBar.SetActive(false);
        }
        
        public void DeactivateTutorials()
        {
            m_tutorials.SetActive(false);
        }

        private void HideEndGameScreen()
        {
            //m_endDemoScreen.SetActive(false);
        }

        public void ActivateMetronomeSelectionPrompts()
        {
            m_metronomeSelectionPrompts.SetActive(true);
        }
        
        public void DeactivateMetronomeSelectionPrompts()
        {
            m_metronomeSelectionPrompts.SetActive(false);
        }
        
        public void ActivateClickerSelectionPrompts()
        {
            m_clickerSelectionPrompts.SetActive(true);
        }
        
        public void DeactivateClickerSelectionPrompts()
        {
            m_clickerSelectionPrompts.SetActive(false);
        }
    }
}