namespace RibCageGames.UI
{
    using UnityEngine;

    [CreateAssetMenu(fileName = "DialogPopupSettings", menuName = "RibCageGames/Settings/UI/DialogPopupSettings")]
    public class DialogPopupSettings : PopupSettings
    {
        [SerializeField] private Sprite m_floatingImageSpriteLeft;
        [SerializeField] private Sprite m_floatingImageSpriteRight;

        public Sprite FloatingImageSpriteLeft => m_floatingImageSpriteLeft;
        public Sprite FloatingImageSpriteRight => m_floatingImageSpriteRight;
    }
}