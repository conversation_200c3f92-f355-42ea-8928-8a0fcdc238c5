using RibCageGames.Music.fmod;

namespace RibCageGames.Sound
{
    using System.Collections.Generic;
    using RibCageGames.Base;
    using UnityEngine;
    using System;
    using System.Linq;
    using UnityEngine.Audio;

    /// <summary>
    /// A service to handle Audio sources for better audio control
    /// </summary>
    [CreateAssetMenu(fileName = "SoundService", menuName = "RibCageGames/Services/SoundService")]
    public class SoundService : BaseService
    {
        [SerializeField] private int m_startingPoolSize;
        [SerializeField] private SerializableDictionary<MixerGroups, AudioMixerParameterControl> m_mixerParameters;
        [SerializeField] private AudioMixer m_masterMixer;
        
        private HashSet<AudioSource> m_audioSources;
        private HashSet<AudioSource> m_availableAudioSources;
        
        public SerializableDictionary<MixerGroups, AudioMixerParameterControl> MixerParameters => m_mixerParameters;

        private GameObject m_runtimeServicePrefab { get; set; }

        public void PlayClip(SoundEffect sound)
        {
            PlayClipWithCallback(sound);
        }

        public Action PlayClipWithCallback(ISoundSource emitter)
        {
            if (emitter != null)
            {
                emitter.Play();
                return emitter.Stop;
            }

            return null;
        }

        private void ResetSource(AudioSource source)
        {
            source.Stop();
            source.clip = null;
            source.volume = 1f;
            m_availableAudioSources.Add(source);
        }
        
        private void FadeOutSource(AudioSource source, Action cancel, float duration)
        {
            MonoProcess.New().WaitFor(elapsed =>
                {
                    source.volume = 1f - (elapsed / duration);
                    return elapsed >= duration;
                }, duration)
                .Do(cancel);
        }

        private AudioSource GetAvailableSource()
        {
            AudioSource source;
            if (m_availableAudioSources.Count > 0)
            {
                source = m_availableAudioSources.First();
                m_availableAudioSources.Remove(source);
            }
            else
            {
                
                source = m_runtimeServicePrefab.AddComponent<AudioSource>();
                source.spatialize = false;
                source.spatialBlend = 0f;
                m_audioSources.Add(source);
            }
            source.Stop();
            source.clip = null;
            source.volume = 1f;
            
            return source;
        }

        public override void Init(GameObject servicePrefab = null)
        {
            m_audioSources = new HashSet<AudioSource>();
            m_availableAudioSources = new HashSet<AudioSource>();
            m_runtimeServicePrefab = servicePrefab;
            

            foreach (AudioSource source in m_runtimeServicePrefab.GetComponents<AudioSource>())
            {
                source.spatialize = false;
                source.spatialBlend = 0f;
                m_audioSources.Add(source);
            }
            
            if (m_audioSources.Count < m_startingPoolSize)
            {
                for (int i = 0; i < m_startingPoolSize - m_audioSources.Count; i++)
                {
                    m_audioSources.Add(m_runtimeServicePrefab.AddComponent<AudioSource>());
                }
            }

            foreach (AudioMixerParameterControl mixerParameter in m_mixerParameters.Values)
            {
                mixerParameter.Init(m_masterMixer);
            }
        }

        public override void StartService(MonoInjector injector)
        {
            foreach (AudioSource source in m_audioSources)
            {
                m_availableAudioSources.Add(source);
            }
            
            foreach (AudioMixerParameterControl mixerParameter in m_mixerParameters.Values)
            {
                mixerParameter.SetDefault();
            }

            SetSpeakerMode(AudioSpeakerMode.Stereo); //Save and load this
        }

        public void PrintAudioSourceLog()
        {
            string log = "Audio sources include:\n";

            foreach (AudioSource source in m_runtimeServicePrefab.GetComponents<AudioSource>())
            {
                log += $"source with {source.clip} and volume: {source.volume} \n";
            }
            
            Debug.LogError(log);
        }

        public override void Dispose()
        {
            m_audioSources.Clear();
            m_availableAudioSources.Clear();
        }

        public void SetSpeakerMode(AudioSpeakerMode speakerMode)
        {
            AudioSettings.speakerMode = speakerMode;   
        }

        public void ChangeMasterVolume(float value)
        {
            ChangeGroupVolume(MixerGroups.Master, value);
        }
        
        public void ChangeMusicVolume(float value)
        {
            ChangeGroupVolume(MixerGroups.Music, value);
        }
        
        public void ChangeAttackSFXVolume(float value)
        {
            ChangeGroupVolume(MixerGroups.AttackSFX, value);
        }
        
        public void ChangeFootstepsVolume(float value)
        {
            ChangeGroupVolume(MixerGroups.Footsteps, value);
        }

        public void ChangeGroupVolume(MixerGroups group, float value)
        {
            m_mixerParameters[group].SetValue(value);
        }

        private void OnValidate()
        {
            foreach (AudioMixerParameterControl mixerParameter in m_mixerParameters.Values)
            {
                mixerParameter.Validate();
            }
        }
    }

    public enum MixerGroups
    {
        Master = 0,
        Music = 10,
        AttackSFX = 20,
        Footsteps = 30,
    }

    [Serializable]
    public class AudioMixerParameterControl
    {
        [SerializeField] private string m_parameterId;
        [SerializeField][Range(-100f, 100f)] private float m_valueMinimum;
        [SerializeField][Range(-100f, 100f)] private float m_valueMaximum;
        [SerializeField][Range(-100f, 100f)] private float m_defaultValue;
        
        public float ValueMinimum => m_valueMinimum;
        public float ValueMaximum => m_valueMaximum;
        public float DefaultValue => m_defaultValue;
        
        private AudioMixer m_mixer;
        
        public void Init(AudioMixer mixer)
        {
            m_mixer = mixer;
        }

        public void SetValue(float value)
        {
            m_mixer.SetFloat(m_parameterId, Mathf.Lerp(m_valueMinimum, m_valueMaximum, value));
        }
        
        public void SetDefault()
        {
            m_mixer.SetFloat(m_parameterId, m_defaultValue);
        }

        public void Validate()
        {
            if (m_valueMaximum < m_valueMinimum)
            {
                m_valueMaximum = m_valueMinimum;
            }
        }
    }
}