namespace RibCageGames.Sound
{
    using Base;
    using System;
    using UnityEngine;

    [CreateAssetMenu(fileName = "SoundEffectControl", menuName = "RibCageGames/Settings/SoundEffectControl")]
    public class SoundEffectControl : ScriptableObject
    {
        [SerializeField] private SoundEffect m_sourceEffect;

        private SoundService m_soundService;
        private Action m_soundCancel;
        
        private SoundService SoundService
        {
            get
            {
                if (m_soundService == null)
                {
                    m_soundService = ServiceLocator.Get<SoundService>();
                }

                return m_soundService;
            }   
        }
        
        private void OnEnable()
        {
            m_soundService = ServiceLocator.Get<SoundService>();
        }

        public Action PlaySound()
        {
            m_soundCancel = SoundService.PlayClipWithCallback(m_sourceEffect);

            return m_soundCancel;
        }

        public void CancelSound()
        {
            m_soundCancel?.Invoke();
            m_soundCancel = null;
        }
    }
}
