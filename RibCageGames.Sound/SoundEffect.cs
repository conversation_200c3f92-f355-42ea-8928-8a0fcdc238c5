using System;
using FMOD.Studio;
using RibCageGames.Music.fmod;
using UnityEngine.Audio;

namespace RibCageGames.Sound
{
    using System.Collections;
    using System.Collections.Generic;
    using UnityEngine;

    [CreateAssetMenu(fileName = "SoundEffect", menuName = "RibCageGames/Settings/SoundEffect")]
    public class SoundEffect : ScriptableObject, ISoundSource
    {
        [SerializeField] private SelectionStrategy m_selectionStrategy = SelectionStrategy.CorrectedRandom;
        [SerializeField] private AudioClip[] m_clips;
        [SerializeField] private List<float> m_offsets; //TODO: these should be in a single serialized class

        [SerializeField] private List<fmodEventEmitter> m_eventEmitters;
        
        [SerializeField] private AudioMixerGroup m_mixerGroup;

        //TODO: make randonly selected collection for strategies, not sound effect specific
        [NonSerialized] private int m_lastSelected;
        [NonSerialized] private List<int> m_previouslySelected;
        
        public AudioMixerGroup MixerGroup => m_mixerGroup;
        
        public fmodEventEmitter Sound {
            get
            {
                try
                {
                    return m_eventEmitters[0];
                }
                catch (Exception e)
                {
                    Debug.LogError($"No emitters on {name}");
                }
                return null;
                int selection = 0;
                if (m_eventEmitters.Count == 1)
                {
                    return m_eventEmitters[0];
                }

                switch (m_selectionStrategy)
                {
                    case SelectionStrategy.RoundRobin:
                        m_lastSelected = selection = (m_lastSelected + 1) % m_clips.Length;
                        
                        return m_eventEmitters[selection];
                    case SelectionStrategy.CorrectedRandom:
                        m_lastSelected = selection = (m_lastSelected + Random.Range(1, m_clips.Length)) % m_clips.Length;
                        return m_eventEmitters[selection];
                    case SelectionStrategy.Random:
                        m_lastSelected = selection = Random.Range(0, m_clips.Length);
                        return m_eventEmitters[selection];
                    case SelectionStrategy.MultiCorrectedRandom:
                        //selection = (m_previouslySelected[0] + Random.Range(1, m_clips.Length)) % m_clips.Length;
                        //for (int i = 1; i < m_previouslySelected.Count; i++)
                        //{
                        //    selection = (m_previouslySelected[0] + Random.Range(1, m_clips.Length)) % m_clips.Length;
                        //}
                        //m_lastSelected = selection = (m_lastSelected + Random.Range(1, m_clips.Length)) % m_clips.Length;
                        return m_eventEmitters[selection];
                    default:
                        return m_eventEmitters[0];
                }
            }
        }

        //private void OnValidate()
        //{
        //    for (int i = m_offsets.Count; i < m_clips.Length; i++)
        //    {
        //        m_offsets.Add(0f);
        //    }
        //}
        public void Play()
        {
        }

        public void Stop()
        {
        }
    }

    public enum SelectionStrategy
    {
        RoundRobin,
        Random,
        CorrectedRandom,
        MultiCorrectedRandom,
    }
}