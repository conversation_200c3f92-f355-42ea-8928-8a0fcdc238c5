using System;
using UnityEngine;
using Object = UnityEngine.Object;

namespace RibCageGames.Editor.EditorUtillities.SerializeInterface
{

//Taken from https://github.com/adammyhre/Unity-Serialized-Interface
    [Serializable]
    public class InterfaceReference<TInterface, TObject> where TObject : Object where TInterface : class
    {
        [SerializeField, HideInInspector] TObject underlyingValue;

        public TInterface Value
        {
            get => (underlyingValue switch {
                null => null,
                TInterface @interface => @interface,
                _ => throw new InvalidOperationException(
                    $"{underlyingValue} needs to implement interface {nameof(TInterface)}.")
            })!;
            set => underlyingValue = (value switch {
                null => null,
                TObject newValue => newValue,
                _ => throw new ArgumentException($"{value} needs to be of type {typeof(TObject)}.", string.Empty)
            })!;
        }

        public TObject UnderlyingValue
        {
            get => underlyingValue;
            set => underlyingValue = value;
        }
        
        public static bool operator ==(InterfaceReference<TInterface, TObject>? obj1, InterfaceReference<TInterface, TObject>? obj2)
        {
            if (obj1 is null || obj1.UnderlyingValue == null)
            {
                return obj2 is null || obj2.UnderlyingValue == null;
            }
            return obj1.Equals(obj2);
        }
        public static bool operator !=(InterfaceReference<TInterface, TObject>? obj1, InterfaceReference<TInterface, TObject>? obj2)
        {
            return !(obj1 == obj2);
        }

        public InterfaceReference() { }

        public InterfaceReference(TObject target) => underlyingValue = target;

        public InterfaceReference(TInterface @interface) => underlyingValue = @interface as TObject;

        public static implicit operator TInterface(InterfaceReference<TInterface, TObject> obj) => obj.Value;
    }

    [Serializable]
    public class InterfaceReference<TInterface> : InterfaceReference<TInterface, Object> where TInterface : class
    {
    }
}