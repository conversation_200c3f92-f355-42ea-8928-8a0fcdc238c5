namespace RibCageGames.Editor
{
    using System;
    using UnityEngine;

    [AttributeUsage(AttributeTargets.Field | AttributeTargets.Property |
                    AttributeTargets.Class | AttributeTargets.Struct, Inherited = true)]
    public class ConditionalShowAttribute : ConditionalHideAttribute
    {
        public ConditionalShowAttribute(string conditionalSourceField) : base(conditionalSourceField, true) { }
    }
}