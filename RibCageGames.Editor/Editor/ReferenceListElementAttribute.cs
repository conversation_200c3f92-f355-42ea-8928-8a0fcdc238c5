namespace RibCageGames.Editor
{
    using UnityEngine;
    
    public class ReferenceListElementAttribute : PropertyAttribute
    {
        public string EnumVarName;
        public string ReferenceVarName;

        public ReferenceListElementAttribute(string enumVariable, string referenceVariable) {
            EnumVarName = enumVariable;
            ReferenceVarName = referenceVariable;
        }
    }
    
    public class ReferenceListAttribute : PropertyAttribute
    {
        public string ListVarName;
        public string EnumVarName;
        public string ReferenceVarName;

        public ReferenceListAttribute(string listVarName, string enumVariable, string referenceVariable)
        {
            ListVarName = listVarName;
            EnumVarName = enumVariable;
            ReferenceVarName = referenceVariable;
            //this.height = 8f;
        }
    }
}