namespace RibCageGames.Editor
{
    using System;
    using System.Linq.Expressions;
    using UnityEngine;

    public static class ExpressionUtils
    {
        public static MemberExpression GetMemberExpression<T>(Expression<Func<T>> exp)
        {
            MemberExpression body = exp.Body as MemberExpression;
            try
            {
                if (body == null)
                {
                    UnaryExpression ue = ((UnaryExpression) exp.Body);
                    body = ue.Operand as MemberExpression;
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }

            return body;
        }

        public static string GetBeautifiedExpression<T>(Expression<Func<T>> exp)
        {
            string expression = exp.ToString();
            // Beautify expression for display...
            expression = expression.Substring(expression.LastIndexOf('.'));
            if (expression.EndsWith(")")) expression = expression.Substring(0, expression.Length - 1);

            return expression;
        }
    }
}