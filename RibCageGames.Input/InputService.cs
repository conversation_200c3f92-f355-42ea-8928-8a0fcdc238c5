namespace RibCageGames.Input
{
    using Base;
    using System;
    using System.Collections.Generic;
    using UnityEngine;
    using UnityEngine.Events;
    using UnityEngine.InputSystem;
    using UnityEngine.InputSystem.Processors;

    [CreateAssetMenu(fileName = "InputService", menuName = "RibCageGames/Services/InputService")]
    public class InputService : BaseService
    {
        private bool m_inputEnabled = true;
        
        public bool InputEnabled => m_inputEnabled;
        
        [SerializeField] private SerializableDictionary<ActionMaps, InputEventMap> m_inputMaps;
        
        private Dictionary<PlayerInputActions, InputEvent> m_runtimeInputEvents = new Dictionary<PlayerInputActions, InputEvent>();
        private UnityEvent<PlayerInputActions, PlayerInputTypes> m_generalInputEvent = new UnityEvent<PlayerInputActions, PlayerInputTypes>();
        
        public Dictionary<PlayerInputActions, InputEvent> InGameInputs => m_runtimeInputEvents;
        public UnityEvent<PlayerInputActions, PlayerInputTypes> GeneralInputEvent => m_generalInputEvent;
        
        private Action<InputVoidEvent, bool, PlayerInputActions, PlayerInputTypes> m_voidInputResolver;
        

        public override void Init(GameObject servicePrefab = null)
        {
            //var actions = servicePrefab.GetComponent<InputListener>().PlayerInput.actions;
            
            //m_runtimeInputEvents = new Dictionary<PlayerInputActions, InputEvent>();
            foreach (InputEventMap map in m_inputMaps.Values)
            {
                if (map.InputActionAsset == null)
                {
                    continue;
                }

                foreach (InputAction inputAction in map.InputActionAsset)
                {
                    if (Enum.TryParse(inputAction.name, out PlayerInputActions result))
                    {
                        switch (result)
                        {
                            case PlayerInputActions.Movement:
                            case PlayerInputActions.Camera:
                                m_runtimeInputEvents.TryAdd(result, new InputVector2Event());
                                InputVector2Event vector2Action = (InputVector2Event) m_runtimeInputEvents[result];
                                //InputVector2Event vector2Action = (InputVector2Event) map.InputEvents[result];
                                inputAction.performed += (x) =>
                                {
                                    vector2Action.CurrentValue = x.ReadValue<Vector2>();
                                    InvokeVector2Input(vector2Action.Performed, vector2Action.CurrentValue);
                                };
                                inputAction.canceled += (x) =>
                                {
                                    vector2Action.CurrentValue = x.ReadValue<Vector2>();
                                    InvokeVector2Input(vector2Action.Canceled, vector2Action.CurrentValue);
                                };
                                break;
                            case PlayerInputActions.Settings:
                                m_runtimeInputEvents.TryAdd(result, new InputVoidEvent());
                                InputVoidEvent settingsvoidAction = (InputVoidEvent) m_runtimeInputEvents[result];
                                inputAction.performed += (x) => 
                                    InvokeVoidInput(settingsvoidAction, true, result, PlayerInputTypes.Started);
                                inputAction.canceled += (x) =>
                                    InvokeVoidInput(settingsvoidAction, false, result, PlayerInputTypes.Ended);
                                break;
                            default:
                                m_runtimeInputEvents.TryAdd(result, new InputVoidEvent());
                                InputVoidEvent voidAction = (InputVoidEvent) m_runtimeInputEvents[result];
                                inputAction.performed += (x) => 
                                    m_voidInputResolver(voidAction, true, result, PlayerInputTypes.Started);
                                inputAction.canceled += (x) =>
                                    m_voidInputResolver(voidAction, false, result, PlayerInputTypes.Ended);
                                break;
                        }
                    }
                    else
                    {
                        Debug.LogError($"Input enum does not contain {inputAction.name}");
                    }
                }
            }
        }

        protected virtual void InvokeVector2Input(Vector2InputEvent inputEvent, Vector2 value)
        {
            if (!m_inputEnabled) { return; }
            inputEvent?.Invoke(value);
        }
        
        protected virtual void InvokeVoidInput(InputVoidEvent inputEvent, bool performed, PlayerInputActions action, PlayerInputTypes type)
        {
            InvokeVoidInput(inputEvent, performed, action, type, false);
        }
        
        protected virtual void InvokeVoidInput(InputVoidEvent inputEvent, bool performed, PlayerInputActions action, PlayerInputTypes type , bool force = false)
        {
            if (!m_inputEnabled && !force && action != PlayerInputActions.Settings) { return; }

            inputEvent.CurrentValue = performed;
            (performed? inputEvent.Performed : inputEvent.Canceled)?.Invoke();
            inputEvent.ContextualEvent?.Invoke(action, type);
            
            m_generalInputEvent?.Invoke(action, type);
        }

        public void EnableInput()
        {
            m_inputEnabled = true;
            m_voidInputResolver = InvokeVoidInput;
        }
        
        public void DisableInput()
        {
            DisableInput(null);
        }
        
        public void DisableInput(HashSet<PlayerInputActions> unlockingInputs, bool inputPassthrough = true, HashSet<PlayerInputActions> excludedInputs = null)
        {
            m_inputEnabled = false;
            
            ((InputVector2Event) m_runtimeInputEvents[PlayerInputActions.Movement]).Canceled.Invoke(Vector2.zero);
            ((InputVector2Event) m_runtimeInputEvents[PlayerInputActions.Camera]).Canceled.Invoke(Vector2.zero);

            if (unlockingInputs != null && unlockingInputs.Count > 0)
            {
                m_voidInputResolver =
                    (inputEvent, performed, action, type) =>
                    {
                        if (excludedInputs != null && excludedInputs.Contains(action) && performed && type == PlayerInputTypes.Started)
                        {
                            InvokeVoidInput(inputEvent, performed, action, type, true);
                        }
                        else if (unlockingInputs.Contains(action) && performed && type == PlayerInputTypes.Started)
                        {
                            EnableInput();
                            if (inputPassthrough)
                            {
                                MonoProcess.New().Do(() => InvokeVoidInput(inputEvent, performed, action, type));
                            }
                        }
                    };
            }
        }

        public void EnableMouse()
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }
        
        public void DisableMouse()
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }

        public override void StartService(MonoInjector injector)
        {
            EnableInput();
            DisableMouse();
        }

        public override void Dispose()
        {
            
        }
        
        [ContextMenu("SimulateLightAttack")]
        public void SimulateLightAttackInput()
        {
            SimulateInput(PlayerInputActions.LightAttack);
        }
        
        //[ContextMenu("SimulateHeavyAttack")]
        //public void SimulateHeavyAttackInput()
        //{
            //SimulateInput(PlayerInputActions.HeavyAttack);
        //}
        
        [ContextMenu("SimulateDodge")]
        public void SimulateDodgeInput()
        {
            SimulateInput(PlayerInputActions.Dodge);
        }
        
        [ContextMenu("SimulateSpecial")]
        public void SimulateSpecialInput()
        {
            SimulateInput(PlayerInputActions.Special);
        }
        
        [ContextMenu("SimulateJump")]
        public void SimulateJumpInput()
        {
            SimulateInput(PlayerInputActions.Jump);
        }
        
        [ContextMenu("SimulateSettingsInput")]
        public void SimulateSettingsInput()
        {
            SimulateInput(PlayerInputActions.Settings);
        }

        public void SimulateInput(PlayerInputActions input)
        {
            return;
            InputVoidEvent voidAction = (InputVoidEvent) ServiceLocator.Get<InputService>().InGameInputs[input];
            InvokeVoidInput(voidAction, true, input, PlayerInputTypes.Started);
            //Debug.Log($"input {input} InGameInputs {m_runtimeInputEvents != null}");
            //Debug.Log($"InGameInputs[input] {m_runtimeInputEvents[input] != null}");
            //((InputVoidEvent) ServiceLocator.Get<InputService>().InGameInputs[input]).Performed?.Invoke();
            //((InputVoidEvent) ServiceLocator.Get<InputService>().InGameInputs[input]).ContextualEvent?.Invoke(input, PlayerInputTypes.Started);
        }
        
        public void ChangeCameraInputScale(float value)
        {
            ChangeVector2InputScale(PlayerInputActions.Camera, value);
        }
        
        public void ChangeVector2InputScale(PlayerInputActions actionType, float value)
        {
            InputAction action = m_inputMaps[ActionMaps.InGame].InputActionAsset
                .FindAction(actionType == PlayerInputActions.Camera ? "Camera" : "Movement");
            
            action.ApplyParameterOverride((ScaleVector2Processor p) => p.x, value);
            action.ApplyParameterOverride((ScaleVector2Processor p) => p.y, value);
        }
        
        public Action RegisterButtonListener(PlayerInputActions button, Action callbackAction, MonoProcess delayProcess = null)
        {
            if (delayProcess == null)
            {
                delayProcess = MonoProcess.NextFrame;
            }
            
            void Listener(PlayerInputActions input, PlayerInputTypes type)
            {
                if (input == button)
                {
                    callbackAction();
                }
            }

            delayProcess.Do(() =>
            {
                GeneralInputEvent.AddListener(Listener);
            });

            return () => GeneralInputEvent.RemoveListener(Listener);
        }
        
        private void OnValidate()
        {
            foreach (InputEventMap map in m_inputMaps.Values)
            {
                if (map == null || map.InputActionAsset == null)
                {
                    continue;
                }

                foreach (InputAction inputAction in map.InputActionAsset)
                {
                    if (Enum.TryParse(inputAction.name, out PlayerInputActions result))
                    {
                        if (!map.InputEvents.ContainsKey(result))
                        {
                            switch (result)
                            {
                                case PlayerInputActions.Movement:
                                case PlayerInputActions.Camera:
                                    //TODO: These are serialized as the base class. Should be removed.
                                    map.InputEvents[result] = new InputVector2Event()
                                    {
                                        Name = result.ToString()
                                    };
                                    Debug.LogError($"Added vector2 event for {result}");
                                    break;
                                default:
                                    map.InputEvents[result] = new InputVoidEvent()
                                    {
                                        Name = result.ToString()
                                    };
                                    Debug.LogError($"Added void event for {result}");
                                    break;
                            }
                        }
                    }
                    else
                    {
                        Debug.LogError($"Input enum does not contain {inputAction.name}");
                    }
                }

                map.InputEvents.ReSerialize();
            }
        }
    }

    [Serializable]
    public class InputEvent
    {
        [SerializeField] private string m_name;
        
        [SerializeField] private UnityEvent<PlayerInputActions, PlayerInputTypes> m_contextualEvent = new UnityEvent<PlayerInputActions, PlayerInputTypes>();
        
        public string Name
        {
            get => m_name;
            set => m_name = value;
        }
        
        public UnityEvent<PlayerInputActions, PlayerInputTypes> ContextualEvent
        {
            get => m_contextualEvent;
            set => m_contextualEvent = value;
        }
    }
    
    [Serializable]
    public class InputVoidEvent : InputEvent
    {
        [SerializeField] private UnityEvent m_actionPerformed = new UnityEvent();
        [SerializeField] private UnityEvent m_actionCanceled = new UnityEvent();
        
        public UnityEvent Performed
        {
            get => m_actionPerformed;
            set => m_actionPerformed = value;
        }

        public UnityEvent Canceled
        {
            get => m_actionCanceled;
            set => m_actionCanceled = value;
        }

        public bool CurrentValue { get; internal set; }
    }
    
    [Serializable]
    public class InputVector2Event : InputEvent
    {
        [SerializeField] private Vector2InputEvent m_actionPerformed = new Vector2InputEvent();
        [SerializeField] private Vector2InputEvent m_actionCanceled = new Vector2InputEvent();
        
        public Vector2InputEvent Performed
        {
            get => m_actionPerformed;
            set => m_actionPerformed = value;
        }

        public Vector2InputEvent Canceled => m_actionCanceled;
        
        public Vector2 CurrentValue { get; internal set; }
    }
    
    [Serializable]
    public class Vector2InputEvent : UnityEvent<Vector2>{}
    
    [Serializable]
    public class InputEventMap
    {
        [SerializeField] private InputActionAsset m_inputActionAsset;
        [SerializeField] private SerializableDictionary<PlayerInputActions, InputEvent> m_inputEvents;
        
        public InputActionAsset InputActionAsset => m_inputActionAsset;
        public SerializableDictionary<PlayerInputActions, InputEvent> InputEvents => m_inputEvents;
    }
    
    public enum ActionMaps
    {
        InGame = 0,
    }
    
    public enum PlayerInputActions
    {
        Movement = 0,
        Jump = 1,
        LightAttack = 2,
        //HeavyAttack = 3,
        Dodge = 4,
        Default = 5,
        Special = 6,
        LockOn = 7,
        LockOnSwitch = 8,
        Camera = 9,
        Settings = 10,
    }
    
    public enum PlayerInputTypes
    {
        Started = 0,
        Ended = 1,
        Held = 2,
    }

    public static class InputExtensions
    {
        public static MonoProcess HoldInput(this MonoProcess process, InputVoidEvent input, Action onComplete, float timeToHold = 1.5f, Action<float> visualUpdate = null)
        {
            bool released = false;
            input.Canceled.AddSingleUseListener(() => released = true);
            process.WaitFor((t) =>
            {
                visualUpdate?.Invoke(t);
                return released;
            }, timeToHold)
                .Do(() =>
            {
                if (!released)
                {
                    onComplete.Invoke();
                }
            });
            return process;
        }
    }
}