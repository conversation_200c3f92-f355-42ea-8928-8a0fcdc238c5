namespace RibCageGames.Input
{
    using Base;
    using System;
    using System.Collections.Generic;
    using UnityEngine;
    using UnityEngine.Events;
    using UnityEngine.InputSystem;
    using UnityEngine.InputSystem.Processors;
    using System.Threading.Tasks;
    using Cysharp.Threading.Tasks;

    [CreateAssetMenu(fileName = "InputService", menuName = "RibCageGames/Services/InputService")]
    public class InputService : BaseService
    {
        private bool m_inputEnabled = true;
        
        public bool InputEnabled => m_inputEnabled;
        
        private InputActionAsset m_mainInputActions;
        
        private Dictionary<PlayerInputActions, InputEvent> m_runtimeInputEvents = new();
        private UnityEvent<PlayerInputActions, PlayerInputTypes> m_generalInputEvent = new();
        
        public Dictionary<PlayerInputActions, InputEvent> InGameInputs => m_runtimeInputEvents;
        public UnityEvent<PlayerInputActions, PlayerInputTypes> GeneralInputEvent => m_generalInputEvent;
        public bool InputRegistered => m_inputRegistered;
        
        private Action<InputVoidEvent, bool, PlayerInputActions, PlayerInputTypes> m_voidInputResolver;
        
        #region Multiplayer
        private List<Dictionary<PlayerInputActions, InputEvent>> m_runtimeMultiplayerInputEvents = new();
        
        public Dictionary<PlayerInputActions, InputEvent> GetInputEventsForPlayer(int playerIndex)
        {
            return m_runtimeMultiplayerInputEvents[playerIndex];
        }
        #endregion
        
        [NonSerialized] private bool m_inputRegistered = false;
        [NonSerialized] private bool m_adittionalnputRegistered = false;

        public override void Init(GameObject servicePrefab = null)
        {
            m_inputRegistered = false;
        }

        public async UniTask RegisterMainInput(PlayerInput playerInput)
        {
            Debug.LogError($"RegisterMainInput started");
            
            await Task.WhenAny(
                UniTask.WaitWhile(() => string.IsNullOrEmpty(playerInput.currentControlScheme)).AsTask(),
                UniTask.WaitForSeconds(0.5f).AsTask()
                );
            
            //await UniTask.WaitWhile(() => string.IsNullOrEmpty(playerInput.currentControlScheme));
            //await UniTask.WaitForSeconds(0.5f);
         
            Debug.LogError($"RegisterMainInput started done waiting");
            
            m_inputRegistered = true;
            m_mainInputActions = playerInput.actions;
            string currentControlScheme = playerInput.currentControlScheme;

            // Filter actions based on current control scheme
            foreach (InputAction inputAction in playerInput.actions)
            {
                if (IsActionInControlScheme(inputAction, currentControlScheme))
                {
                    Debug.LogError($"InputService: Registering action '{inputAction.name}' (in control scheme '{currentControlScheme}') to m_runtimeInputEvents");
                    RegisterInputAction(inputAction, m_runtimeInputEvents);
                }
                else
                {
                    Debug.Log($"InputService: Skipped action '{inputAction.name}' (not in control scheme '{currentControlScheme}')");
                }
            }
        }
        
        public async UniTask RegisterAdditionalInput(PlayerInput playerInput)
        {
            await UniTask.WaitWhile(() => string.IsNullOrEmpty(playerInput.currentControlScheme));
            m_adittionalnputRegistered = true;
            foreach (InputAction actionEvent in playerInput.actions)
            {
                RegisterInputAction(actionEvent, m_runtimeInputEvents);
            }
        }
        
        public async UniTask RegisterMultiplayerInput(PlayerInput playerInput, Action<Dictionary<PlayerInputActions, InputEvent>> inputRegisteredCallback)
        {
            await UniTask.WaitWhile(() => string.IsNullOrEmpty(playerInput.currentControlScheme));
            
            Dictionary<PlayerInputActions, InputEvent> playerInputEvents = new();
            //m_adittionalnputRegistered = true;
            foreach (InputAction actionEvent in playerInput.actions)
            {
                RegisterInputAction(actionEvent, playerInputEvents);
            }
            
            m_runtimeMultiplayerInputEvents.Add(playerInputEvents);
            inputRegisteredCallback?.Invoke(playerInputEvents);
        }

        private bool IsActionInControlScheme(InputAction inputAction, string controlScheme)
        {
            if (inputAction == null || string.IsNullOrEmpty(controlScheme))
            {
                return false;
            }

            // Check if any of the action's bindings belong to the current control scheme
            foreach (var binding in inputAction.bindings)
            {
                // Check if this binding belongs to the current control scheme
                if (IsBindingInControlScheme(binding, controlScheme))
                {
                    return true;
                }
            }

            return false;
        }

        private bool IsBindingInControlScheme(InputBinding binding, string controlScheme)
        {
            // If the binding has no groups specified, it's considered universal (belongs to all schemes)
            if (string.IsNullOrEmpty(binding.groups))
            {
                return true;
            }

            // Split the groups string and check if our control scheme is in there
            string[] groups = binding.groups.Split(';');
            foreach (string group in groups)
            {
                if (group.Trim().Equals(controlScheme, System.StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        private void RegisterInputAction(InputAction inputAction, Dictionary<PlayerInputActions, InputEvent> runtimeInputEvents)
        {
            if (Enum.TryParse(inputAction.name, out PlayerInputActions result))
            {
                Debug.Log($"Input service Register action: {inputAction.name}");
                switch (result)
                {
                    case PlayerInputActions.Movement:
                    case PlayerInputActions.Camera:
                    case PlayerInputActions.CameraZoom:
                    case PlayerInputActions.PanFreeCamera:
                    case PlayerInputActions.RotateFreeCamera:
                    //case PlayerInputActions.ZoomFreeCamera:
                        runtimeInputEvents.TryAdd(result, new InputVector2Event());
                        InputVector2Event vector2Action = (InputVector2Event) runtimeInputEvents[result];
                        //InputVector2Event vector2Action = (InputVector2Event) map.InputEvents[result];
                        inputAction.performed += (x) =>
                        {
                            vector2Action.CurrentValue = x.ReadValue<Vector2>();
                            InvokeVector2Input(vector2Action.Performed, vector2Action.CurrentValue);
                        };
                        inputAction.canceled += (x) =>
                        {
                            vector2Action.CurrentValue = x.ReadValue<Vector2>();
                            InvokeVector2Input(vector2Action.Canceled, vector2Action.CurrentValue);
                        };
                        break;
                    case PlayerInputActions.Settings:
                        runtimeInputEvents.TryAdd(result, new InputVoidEvent());
                        InputVoidEvent settingsvoidAction = (InputVoidEvent) runtimeInputEvents[result];
                        inputAction.performed += (x) => 
                            InvokeVoidInput(settingsvoidAction, true, result, PlayerInputTypes.Started);
                        inputAction.canceled += (x) =>
                            InvokeVoidInput(settingsvoidAction, false, result, PlayerInputTypes.Ended);
                        break;
                    default:
                        runtimeInputEvents.TryAdd(result, new InputVoidEvent());
                        InputVoidEvent voidAction = (InputVoidEvent) runtimeInputEvents[result];
                        inputAction.performed += (x) => 
                            m_voidInputResolver(voidAction, true, result, PlayerInputTypes.Started);
                        inputAction.canceled += (x) =>
                            m_voidInputResolver(voidAction, false, result, PlayerInputTypes.Ended);
                        break;
                }
            }
            else
            {
                Debug.LogError($"Input enum does not contain {inputAction.name}");
            }
        }

        protected virtual void InvokeVector2Input(Vector2InputEvent inputEvent, Vector2 value)
        {
            if (!m_inputEnabled) { return; }
            inputEvent?.Invoke(value);
        }
        
        protected virtual void InvokeVoidInput(InputVoidEvent inputEvent, bool performed, PlayerInputActions action, PlayerInputTypes type)
        {
            InvokeVoidInput(inputEvent, performed, action, type, false);
        }
        
        protected virtual void InvokeVoidInput(InputVoidEvent inputEvent, bool performed, PlayerInputActions action, PlayerInputTypes type , bool force = false)
        {
            if (!m_inputEnabled && !force && action != PlayerInputActions.Settings) { return; }

            inputEvent.CurrentValue = performed;
            (performed? inputEvent.Performed : inputEvent.Canceled)?.Invoke();
            inputEvent.ContextualEvent?.Invoke(action, type);
            
            m_generalInputEvent?.Invoke(action, type);
        }

        [ContextMenu("EnableInput")]
        public void EnableInput()
        {
            m_inputEnabled = true;
            m_voidInputResolver = InvokeVoidInput;
        }
        
        public void DisableInput()
        {
            DisableInput(null);
        }
        
        public void DisableInput(HashSet<PlayerInputActions> unlockingInputs, bool inputPassthrough = true, HashSet<PlayerInputActions> excludedInputs = null)
        {
            m_inputEnabled = false;
            
            ((InputVector2Event) m_runtimeInputEvents[PlayerInputActions.Movement]).Canceled.Invoke(Vector2.zero);
            ((InputVector2Event) m_runtimeInputEvents[PlayerInputActions.Camera]).Canceled.Invoke(Vector2.zero);

            if (unlockingInputs != null && unlockingInputs.Count > 0)
            {
                m_voidInputResolver =
                    (inputEvent, performed, action, type) =>
                    {
                        if (excludedInputs != null && excludedInputs.Contains(action) && performed && type == PlayerInputTypes.Started)
                        {
                            InvokeVoidInput(inputEvent, performed, action, type, true);
                        }
                        else if (unlockingInputs.Contains(action) && performed && type == PlayerInputTypes.Started)
                        {
                            EnableInput();
                            if (inputPassthrough)
                            {
                                MonoProcess.New().Do(() => InvokeVoidInput(inputEvent, performed, action, type));
                            }
                        }
                    };
            }
        }

        public void EnableMouse()
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }
        
        public void DisableMouse()
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }

        public override void StartService(MonoInjector injector)
        {
            EnableInput();
            DisableMouse();
        }

        public override void Dispose()
        {
            m_inputRegistered = false;
            m_adittionalnputRegistered = false;
        }
        
        [ContextMenu("SimulateLightAttack")]
        public void SimulateLightAttackInput()
        {
            SimulateInput(PlayerInputActions.LightAttack);
        }
        
        [ContextMenu("SimulateDodge")]
        public void SimulateDodgeInput()
        {
            SimulateInput(PlayerInputActions.Dodge);
        }
        
        [ContextMenu("SimulateSpecial")]
        public void SimulateSpecialInput()
        {
            SimulateInput(PlayerInputActions.Special);
        }
        
        [ContextMenu("SimulateJump")]
        public void SimulateJumpInput()
        {
            SimulateInput(PlayerInputActions.Jump);
        }
        
        [ContextMenu("SimulateSettingsInput")]
        public void SimulateSettingsInput()
        {
            SimulateInput(PlayerInputActions.Settings);
        }

        public void SimulateInput(PlayerInputActions input)
        {
            InputVoidEvent voidAction = (InputVoidEvent) InGameInputs[input];
            InvokeVoidInput(voidAction, true, input, PlayerInputTypes.Started);
        }
        
        public void ChangeCameraInputScale(float value)
        {
            ChangeVector2InputScale(PlayerInputActions.Camera, value);
        }
        
        public void ChangeVector2InputScale(PlayerInputActions actionType, float value)
        {
            InputAction action = m_mainInputActions
                .FindAction(actionType == PlayerInputActions.Camera ? "Camera" : "Movement");
            
            action.ApplyParameterOverride((ScaleVector2Processor p) => p.x, value);
            action.ApplyParameterOverride((ScaleVector2Processor p) => p.y, value);
        }
        
        public Action RegisterButtonListener(PlayerInputActions button, Action callbackAction, MonoProcess delayProcess = null)
        {
            if (delayProcess == null)
            {
                delayProcess = MonoProcess.NextFrame;
            }
            
            void Listener(PlayerInputActions input, PlayerInputTypes type)
            {
                if (input == button)
                {
                    callbackAction();
                }
            }

            delayProcess.Do(() =>
            {
                GeneralInputEvent.AddListener(Listener);
            });

            return () => GeneralInputEvent.RemoveListener(Listener);
        }
        
        public Action RegisterButtonListener(PlayerInputActions button, Action<PlayerInputTypes> callbackAction, MonoProcess delayProcess = null)
        {
            if (delayProcess == null)
            {
                delayProcess = MonoProcess.NextFrame;
            }
            
            void Listener(PlayerInputActions input, PlayerInputTypes type)
            {
                if (input == button)
                {
                    callbackAction(type);
                }
            }

            delayProcess.Do(() =>
            {
                GeneralInputEvent.AddListener(Listener);
            });

            return () => GeneralInputEvent.RemoveListener(Listener);
        }
        
        public async Task WaitForInputRegistration()
        {
            await UniTask.WaitUntil(() => m_inputRegistered);
        }
        
        public async Task WaitForSecondaryInputRegistration()
        {
            await UniTask.WaitUntil(() => m_adittionalnputRegistered);
        }
    }

    [Serializable]
    public class InputEvent
    {
        [SerializeField] private string m_name;
        
        [SerializeField] private UnityEvent<PlayerInputActions, PlayerInputTypes> m_contextualEvent = new UnityEvent<PlayerInputActions, PlayerInputTypes>();
        
        public string Name
        {
            get => m_name;
            set => m_name = value;
        }
        
        public UnityEvent<PlayerInputActions, PlayerInputTypes> ContextualEvent
        {
            get => m_contextualEvent;
            set => m_contextualEvent = value;
        }
    }
    
    [Serializable]
    public class InputVoidEvent : InputEvent
    {
        [SerializeField] private UnityEvent m_actionPerformed = new UnityEvent();
        [SerializeField] private UnityEvent m_actionCanceled = new UnityEvent();
        
        public UnityEvent Performed
        {
            get => m_actionPerformed;
            set => m_actionPerformed = value;
        }

        public UnityEvent Canceled
        {
            get => m_actionCanceled;
            set => m_actionCanceled = value;
        }

        public bool CurrentValue { get; internal set; }
    }
    
    [Serializable]
    public class InputVector2Event : InputEvent
    {
        [SerializeField] private Vector2InputEvent m_actionPerformed = new Vector2InputEvent();
        [SerializeField] private Vector2InputEvent m_actionCanceled = new Vector2InputEvent();
        
        public Vector2InputEvent Performed
        {
            get => m_actionPerformed;
            set => m_actionPerformed = value;
        }

        public Vector2InputEvent Canceled => m_actionCanceled;
        
        public Vector2 CurrentValue { get; internal set; }
    }
    
    [Serializable]
    public class Vector2InputEvent : UnityEvent<Vector2>{}
    
    [Serializable]
    public class InputEventMap
    {
        [SerializeField] private InputActionAsset m_inputActionAsset;
        [SerializeField] private SerializableDictionary<PlayerInputActions, InputEvent> m_inputEvents;
        
        public InputActionAsset InputActionAsset
        {
            get => m_inputActionAsset;
            set => m_inputActionAsset = value;
        }

        public SerializableDictionary<PlayerInputActions, InputEvent> InputEvents => m_inputEvents;
    }
    
    public enum ActionMaps
    {
        InGame = 0,
    }
    
    public enum PlayerInputActions
    {
        Movement = 0,
        Jump = 1,
        LightAttack = 2,
        //HeavyAttack = 3,
        Dodge = 4,
        Default = 5,
        Special = 6,
        LockOn = 7,
        LockOnSwitch = 8,
        Camera = 9,
        Settings = 10,
        CameraZoom = 11,
        
        //Secondary scheme
        PanFreeCamera = 101,
        RotateFreeCamera = 102,
        //ZoomFreeCamera = 103,
        SnapToFollow = 104,
        CameraRecord = 105,
        CameraPlayback = 106,
    }
    
    public enum PlayerInputTypes
    {
        Started = 0,
        Ended = 1,
        Held = 2,
    }

    public static class InputExtensions
    {
        public static MonoProcess HoldInput(this MonoProcess process, InputVoidEvent input, Action onComplete, float timeToHold = 1.5f, Action<float> visualUpdate = null)
        {
            bool released = false;
            input.Canceled.AddSingleUseListener(() => released = true);
            process.WaitFor((t) =>
            {
                visualUpdate?.Invoke(t);
                return released;
            }, timeToHold)
                .Do(() =>
            {
                if (!released)
                {
                    onComplete.Invoke();
                }
            });
            return process;
        }
    }
}