using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.InputSystem;

[CreateAssetMenu(fileName = "HapticFeedbackInstanceSetting", menuName = "RibCageGames/Settings/HapticFeedbackInstanceSetting")]
public class HapticFeedbackInstanceSetting : MonoScriptableObject
{
    [SerializeField] private float lowFrequencyDuration = 0.1f;
    [SerializeField] private float lowFrequencyAmplitude = 1f;
    [SerializeField] private float highFrequencyDuration = 0.1f;
    [SerializeField] private float highFrequencyAmplitude = 1f;

    public async void TriggerHapticsOnFirstGamepad()
    {
        Gamepad gamepad = Gamepad.all.FirstOrDefault();
        if (gamepad != null)
        {
            gamepad.SetMotorSpeeds(lowFrequencyAmplitude, highFrequencyAmplitude);
            await UniTask.WaitForSeconds(lowFrequencyDuration + highFrequencyDuration);
            gamepad.SetMotorSpeeds(0f, 0f);
        }
        else
        {
            //Debug.LogWarning("No gamepad found to trigger haptics on.");
        }
    }

    public override void Initialize()
    {
    }
}


