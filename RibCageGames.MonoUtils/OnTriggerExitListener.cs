using System;
using UnityEngine;
using UnityEngine.Events;

public class OnTriggerExitListener : MonoBehaviour
{
    [SerializeField] private string m_activatingTag;
    [SerializeField] private OnTriggerExitEvent triggerExit = new OnTriggerExitEvent();
    
    public OnTriggerExitEvent TriggerExit => triggerExit;

    private void OnTriggerExit(Collider other)
    {
        if (string.IsNullOrEmpty(m_activatingTag) || other.CompareTag(m_activatingTag))
        {
            triggerExit?.Invoke(other);
        }
    }
}

[Serializable]
public class OnTriggerExitEvent : UnityEvent<Collider> {}
