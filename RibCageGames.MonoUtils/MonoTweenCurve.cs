using System;
using System.Threading;

namespace RibCageGames.MonoUtils
{
    using System.Collections;
    using System.Collections.Generic;
    using DG.Tweening;
    using UnityEngine;

    [CreateAssetMenu(fileName = "MonoTweenCurve", menuName = "RibCageGames/Utilities/MonoTweenCurve")]
    public class MonoTweenCurve : MonoTweenerSettings
    {
        [SerializeField] private int m_resolution = 10;
        
        [Toolt<PERSON>("Curve anchors. First anchor will always be targetTransform initial position, last will always be target value.")]
        [SerializeField] private Vector3[] m_anchors;
        
        [Tooltip("Curve handles. Used for calculating the curve, auto-set to correct number needed")]
        [SerializeField] private List<Vector3> m_handles;
                    
        protected override Tweener TweenerMove(Transform targetTransform, Vector3 value, float duration,
            Vector3 overrideForward = new Vector3(), Transform otherEntity = null,
            Action<Vector3> outputAction = null)
        {
            List<Vector3> waypoints = new List<Vector3>();

            Vector3 relativeModifier = Relative? targetTransform.position : Vector3.zero;
            
            for (int i = 0; i < m_handles.Count + m_anchors.Length + 1; i++)
            {
                if (i % 3 == 0) //anchor
                {
                    if (i == m_handles.Count + m_anchors.Length - 2) //last anchor, value
                    {
                        waypoints.Add(value);
                    }
                    else
                    {
                        waypoints.Add(m_anchors[i / 3].TransformByForward(overrideForward));
                        waypoints[i] += relativeModifier; //Add local modifier
                    }
                }
                else //handle
                {
                    waypoints.Add(m_handles[i - (i/3) - 1].TransformByForward(overrideForward));
                    waypoints[i] += relativeModifier; //Add local modifier
                }
            }
            
            return targetTransform.DOPath(waypoints.ToArray(),
            duration,
            PathType.CubicBezier, PathMode.Ignore, m_resolution,
            Color.blue);
        }

        private void OnValidate()
        {
            TweenType = TweenerType.Move;
            int delta = m_handles.Count - (2 + (m_anchors.Length * 2));
            if (delta > 0)
            {
                for (int i = 0; i < delta; i++)
                {
                    m_handles.RemoveAt(m_handles.Count - 1);
                }
            }
            else
            {
                for (int i = 0; i < - delta; i++)
                {
                    if (m_handles.Count < 1)
                    {
                        m_handles.Add(new Vector3());
                    }
                    else
                    {
                        m_handles.Add(m_handles[m_handles.Count - 1]);
                    }
                }
            }
        }
    }
}