namespace RibCageGames.MonoUtils
{
    using DG.Tweening;
    using UnityEngine;
    using UnityEngine.Events;

    public class MonoTweener : MonoBehaviour
    {
        //TODO make work via scriptable object settings

        [SerializeField] private bool m_runOnStart;

        [SerializeField] private MonoTweenerSettings m_settings;
        [SerializeField] private UnityEvent m_onComplete;
        private Tweener m_tween;
        private Vector3 m_target;


        private void Start()
        {
            if (m_runOnStart)
            {
                Run();
            }
        }
        //public void SetX(float x) {
        //    m_target.x = x;
        //}
        //
        //public void SetY(float y) {
        //    m_target.y = y;
        //}
        //
        //public void SetZ(float z) {
        //    m_target.z = z;
        //}
        //
        //public void DoMove(float duration) {
        //    m_tween = transform.DOMove(m_target, duration);
        //    if (m_onComplete != null) {
        //        m_tween.onComplete += m_onComplete.Invoke;
        //    }
        //}
        //
        //public void DOScale(float duration) {
        //    m_tween = transform.DOScale(m_target, duration);
        //    if (m_onComplete != null) {
        //        m_tween.onComplete += m_onComplete.Invoke;
        //    }
        //}
//
        //public void DORotate(float duration) {
        //    m_tween = transform.DORotate(m_target, duration);
        //if (m_onComplete != null) {
        //    m_tween.onComplete += m_onComplete.Invoke;
        //}
        //}
//
        //public void SetFrom() {
        //    m_tween.From();
        //}
//
        //public void SetDelay(float delay) {
        //    m_tween.SetDelay(delay);
        //}
        //
        //public void SetEase(int easeIndex) {
        //    m_tween.SetEase((Ease) easeIndex);
        //}

        public void Run()
        {
            Run(null);
        }

        public void Run(MonoTweenerSettings overrideSettings = null)
        {
            (overrideSettings ?? m_settings).Run(transform, m_onComplete.Invoke);

            //MonoTweenerSettings settings = overrideSettings ?? m_settings;
            //switch (settings.TweenType)
            //{
            //    case TweenerType.Move:
            //        m_tween = transform.DOMove(
            //            settings.Relative?
            //                transform.position + settings.TargetValue
            //                : settings.TargetValue,
            //            settings.Duration);
            //        break;
            //    case TweenerType.Rotate:
            //        m_tween = transform.DORotate(
            //            settings.Relative?
            //                transform.rotation.eulerAngles + settings.TargetValue
            //                : settings.TargetValue,
            //            settings.Duration);
            //        break;
            //    case TweenerType.Scale:
            //        m_tween = transform.DOScale(
            //            settings.Relative?
            //                transform.localScale + settings.TargetValue
            //                : settings.TargetValue,
            //            settings.Duration);
            //        break;
            //}
            //
            //m_tween.SetDelay(settings.Delay);
            //m_tween.SetEase(settings.Ease);
            //if (settings.From) { m_tween.From(); }
            //if (m_onComplete != null) {
            //    m_tween.onComplete += m_onComplete.Invoke;
            //}
        }
    }
}