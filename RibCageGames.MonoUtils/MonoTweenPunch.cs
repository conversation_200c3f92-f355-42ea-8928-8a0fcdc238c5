using System;

namespace RibCageGames.MonoUtils
{
    using System.Collections;
    using System.Collections.Generic;
    using DG.Tweening;
    using UnityEngine;

    [CreateAssetMenu(fileName = "MonoTweenerPunch", menuName = "RibCageGames/Utilities/MonoTweenerPunch")]
    public class MonoTweenPunch : MonoTweenerSettings
    {
        protected override Tweener TweenerMove(Transform targetTransform, Vector3 value, float duration,
            Vector3 overrideForward = new Vector3(), Transform otherEntity = null,
            Action<Vector3> outputAction = null)
        {
            return targetTransform.DOPunchPosition(value, duration);
        }

        protected override Tweener TweenerRotate(Transform targetTransform, Vector3 value, float duration,
            Transform otherEntity = null)
        {
            return targetTransform.DOPunchRotation(value, duration);
        }

        protected override Tweener TweenerScale(Transform targetTransform, Vector3 value, float duration,
            Transform otherEntity = null)
        {
            return targetTransform.DOPunchScale(value, duration);
        }
    }
}