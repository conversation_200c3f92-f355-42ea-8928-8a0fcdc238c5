using System;

namespace RibCageGames.MonoUtils
{
    using DG.Tweening;
    using UnityEngine;

    [CreateAssetMenu(fileName = "MonoTweenCast", menuName = "RibCageGames/Utilities/MonoTweenCast")]
    public class MonoTweenCast : MonoTweenerSettings
    {

        [SerializeField] private float m_maxDistance = 50f;
        [SerializeField] private LayerMask m_raycastLayers;
        [SerializeField] private Vector3 m_hitPointOffset;
        
        protected override Tweener TweenerMove(Transform targetTransform, Vector3 value, float duration,
            Vector3 overrideForward = new Vector3(), Transform otherEntity = null, Action<Vector3> outputAction = null)
        {
            RaycastHit hit;
            
            // Does the ray intersect any objects excluding the player layer
            if (Physics.Raycast(targetTransform.position - value.normalized, value, out hit, m_maxDistance + 1,
                m_raycastLayers))
            {
                //Debug.Log($"Did Hit at {hit.point} distance: {hit.distance}");
                if (m_incremental)
                {
                    Vector3 initialPosition = targetTransform.position;
                    Vector3 savedValue = initialPosition;

                    //Debug.Log($"final pos {hit.point + m_hitPointOffset}");
                    
                    Tweener tweener = DOVirtual.Vector3(initialPosition, hit.point + m_hitPointOffset, duration, newPos =>
                    {
                        targetTransform.position += newPos - savedValue;
                        savedValue = newPos;
                    });

                    return tweener;
                }
                else
                {
                    return targetTransform.DOMove(hit.point + m_hitPointOffset, duration, true);
                }
            }
            else
            {
                return targetTransform.DOMove(targetTransform.position + value * m_maxDistance + m_hitPointOffset, duration, true);
            }
        }
        
        protected override Tweener TweenerRotate(Transform targetTransform, Vector3 value, float duration,
            Transform otherEntity = null)
        {
            throw new System.NotImplementedException();
        }

        protected override Tweener TweenerScale(Transform targetTransform, Vector3 value, float duration,
            Transform otherEntity = null)
        {
            throw new System.NotImplementedException();
        }
    }
}