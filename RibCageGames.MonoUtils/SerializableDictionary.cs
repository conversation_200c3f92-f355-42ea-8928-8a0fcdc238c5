namespace RibCageGames.MonoUtils
{
    using System;
    using System.Collections.Generic;
    using UnityEngine;
    
    [Serializable]
    public class SerializableDictionary<TKey, TValue> : Dictionary<TKey, TValue>, ISerializationCallbackReceiver
    {
        private TKey m_defaultValue;
        [SerializeField] private List<SerializableKeyPair<TKey, TValue>> m_keyValuePairs;

        public SerializableDictionary(TKey defaultValue)
        {
            m_defaultValue = defaultValue;
        }
        
        public void OnBeforeSerialize()
        {
            m_keyValuePairs.Clear();
            foreach (KeyValuePair<TKey, TValue> pair in this)
            {
                m_keyValuePairs.Add(m_keyValuePairs.Exists(keyValuePair => keyValuePair.Key.Equals(pair.Key))
                    ? new SerializableKeyPair<TKey, TValue>(m_defaultValue, pair.Value)
                    : new SerializableKeyPair<TKey, TValue>(pair.Key, pair.Value));
            }
        }

        public void OnAfterDeserialize()
        {
            Clear();
            foreach (SerializableKeyPair<TKey, TValue> pair in m_keyValuePairs)
            {
                Add(this.ContainsKey(pair.Key) ? m_defaultValue : pair.Key, pair.Value);
            }
        }

        [Serializable]
        public class SerializableKeyPair<TKey, TValue>
        {
            public TKey Key;
            public TValue Value;

            public SerializableKeyPair(TKey key, TValue value)
            {
                Key = key;
                Value = value;
            }
        }
    }
}