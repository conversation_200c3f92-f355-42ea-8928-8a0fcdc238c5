namespace RibCageGames.MonoUtils
{
    using Base;
    using UnityEngine;
    using UnityEngine.Events;
    using System;
    using System.Collections.Generic;

    public class DelayedEventSequence : MonoBehaviour
    {
        [SerializeField] private List<DelayedEventStep> m_events;

        public void Activate()
        {
            MonoProcess process = MonoProcess.New();
            
            foreach (DelayedEventStep step in m_events)
            {
                process.WaitForSeconds(step.delay)
                    .Do(step.action.Invoke);
            }
        }
    }
    
    [Serializable]
    public class DelayedEventStep
    {
        [SerializeField] private float m_delay;
        [SerializeField] private UnityEvent m_action;
        
        internal float delay => m_delay;
        internal UnityEvent action => m_action;
    }
}