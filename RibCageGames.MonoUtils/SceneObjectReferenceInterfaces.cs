namespace RibCageGames.MonoUtils
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Linq;
    using UnityEngine;

    public interface ISceneReferenceCollection<T>
    {
        List<T> GetItems();
        T GetItem(int index);
        int GetIndex(T item);
    }

    [Serializable]
    public class SceneReferenceCollection<T> : ISceneReferenceCollection<T>
    {
        [SerializeField] private List<T> m_referenceList = new List<T>();

        public List<T> GetItems() => m_referenceList;

        public T GetItem(int index)
        {
            return m_referenceList[index];
        }

        public int GetIndex(T item)
        {
            return m_referenceList.IndexOf(item);
        }

        public T this[int index]
        {
            get { return m_referenceList[index]; }
            set
            {
                if (index > m_referenceList.Count)
                {
                    m_referenceList.Add(value);
                }
                else
                {
                    m_referenceList[index] = value;
                }
            }
        }
    }

    [Serializable]
    public class ColliderReferenceCollection : SceneReferenceCollection<Collider>
    {
    }

    [Serializable]
    public class EffectParentReferenceCollection : SceneReferenceCollection<GameObject>
    {
    }
}