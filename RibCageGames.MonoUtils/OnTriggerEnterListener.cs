using System;
using UnityEngine;
using UnityEngine.Events;

public class OnTriggerEnterListener : MonoBehaviour
{
    [SerializeField] private string m_activatingTag;
    [SerializeField] private OnTriggerEnterEvent triggerEnter = new();

    public OnTriggerEnterEvent TriggerEnter => triggerEnter;

    private void OnTriggerEnter(Collider other)
    {
        if (enabled && (string.IsNullOrEmpty(m_activatingTag) || other.CompareTag(m_activatingTag)))
        {
            triggerEnter?.Invoke(other);
        }
    }
}

[Serializable]
public class OnTriggerEnterEvent : UnityEvent<Collider> {}
