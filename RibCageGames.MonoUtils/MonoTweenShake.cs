using System;

namespace RibCageGames.MonoUtils
{
    using DG.Tweening;
    using UnityEngine;

    [CreateAssetMenu(fileName = "MonoTweenShake", menuName = "RibCageGames/Utilities/MonoTweenShake")]
    public class MonoTweenShake : MonoTweenerSettings
    {
        protected override Tweener TweenerMove(Transform targetTransform, Vector3 value, float duration,
            Vector3 overrideForward = new Vector3(), Transform otherEntity = null,
            Action<Vector3> outputAction = null)
        {
            return targetTransform.DOShakePosition(duration, value);
        }

        protected override Tweener TweenerRotate(Transform targetTransform, Vector3 value, float duration,
            Transform otherEntity = null)
        {
            return targetTransform.DOShakeRotation(duration, value);
        }

        protected override Tweener TweenerScale(Transform targetTransform, Vector3 value, float duration,
            Transform otherEntity = null)
        {
            return targetTransform.DOShakeScale(duration, value);
        }
    }
}