using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

namespace RibCageGames.MonoUtils
{
    public class ContinuousOverlapHandler : MonoBehaviour
    {
        [SerializeField] private bool m_overlapping = false;
        [SerializeField] private string m_activatingTag;
        [SerializeField] private OnTriggerOverlapEvent onTriggerOverlap = new();
        [SerializeField] private Collider m_collider;
        [SerializeField] private bool debug;
        [SerializeField] private List<string> m_colliderOverlapList = new();
        private HashSet<Collider> m_overlappingColliders = new();
        public OnTriggerOverlapEvent TriggerOverlap => onTriggerOverlap;
        public bool Overlapping => m_overlapping;
        public Collider Collider => m_collider;
        
        private void OnTriggerEnter(Collider other)
        {
            if(debug) Debug.LogError($"debug log {other.name}");
            if (string.IsNullOrEmpty(m_activatingTag) || other.CompareTag(m_activatingTag))
            {
                m_overlappingColliders.Add(other);
                m_overlapping = m_overlappingColliders.Count > 0;
                onTriggerOverlap?.Invoke(m_overlapping, other);
                if (debug)
                {
                    Debug.LogError($"debug log {other.name} number {m_overlappingColliders.Count}");
                }

                m_colliderOverlapList.Add(other.name);
                
            }
        }

        private void OnTriggerExit(Collider other)
        {
            if(debug) Debug.LogError($"Exit {other.name}");
            if (string.IsNullOrEmpty(m_activatingTag) || other.CompareTag(m_activatingTag))
            {
                m_overlappingColliders.Remove(other);
                m_overlapping = m_overlappingColliders.Count > 0;
                onTriggerOverlap?.Invoke(m_overlapping, other);
                if(debug) Debug.LogError($"Exit {other.name} number {m_overlappingColliders.Count}");
                
                m_colliderOverlapList.Remove(other.name);
            }
        }

        public (bool, Vector3) CheckOverlap(LayerMask mask)
        {
            bool hit = false;
            RaycastHit hitData = default;
            if (Collider is SphereCollider)
            {
                hit = Physics.SphereCast(
                    new Ray(transform.position + Vector3.up * 0.5f,
                        Vector3.down),
                    ((SphereCollider) Collider).radius,
                    out hitData,
                    0.5f,
                    mask);   
            }
            else if(Collider is CapsuleCollider capsule)
            {
                //CapsuleCollider capsule = (CapsuleCollider) Collider;
                hit = Physics.SphereCast(
                    new Ray(transform.position + capsule.center,
                        Vector3.down),
                    ((CapsuleCollider) Collider).radius,
                    out hitData,
                    capsule.height,
                    mask);
            }

            m_overlappingColliders.Clear();
            m_overlappingColliders.Add(hitData.collider);
            m_overlapping = m_overlappingColliders.Count > 0;
            
            if(debug) Debug.LogError($"Manual check {(hit ? (hitData.transform.name) : string.Empty)} number {m_overlappingColliders.Count}");
            
            m_colliderOverlapList.Clear();
            if(hit) m_colliderOverlapList.Add(hitData.collider.name);
            
            return (hit, hit ? hitData.normal : Vector3.zero);
        }
        
        public void ResetOverlap()
        {
            if(debug) Debug.LogError($"Reset ContinuousOverlapCheck");
            m_overlappingColliders.Clear();
            m_overlapping = false;
            onTriggerOverlap?.Invoke(m_overlapping, null);
            
            m_colliderOverlapList.Clear();
        }

        public void SetDebug()
        {
            debug = true;
        }
    }

    [Serializable]
    public class OnTriggerOverlapEvent : UnityEvent<bool, Collider> {}
}