namespace RibCageGames.MonoUtils
{
    using RibCageGames.Base;
    using UnityEngine;
    using UnityEngine.Events;

    public class DelayedEventTrigger : MonoBehaviour
    {
        [SerializeField] private float m_delay;
        [SerializeField] private UnityEvent m_event;

        public void Activate()
        {
            MonoProcess.WaitForSecondsProcess(m_delay)
                .Do(m_event.Invoke);
        }
    }
}
