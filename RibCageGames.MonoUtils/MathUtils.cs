using System;
using System.Collections.Generic;
using UnityEngine;

namespace RibCageGames.MonoUtils
{
    public static class MathUtils
    {
        public static Vector3 TransformByForward(this Vector3 vector, Vector3 overrideForward)
        {
            if (overrideForward == Vector3.zero) { return vector; }

            overrideForward = overrideForward.normalized;
            Vector3 right = new Vector3(overrideForward.z, overrideForward.y, - overrideForward.x);
            Vector3 up = Vector3.Cross(overrideForward, right);
            return 
                vector.z * overrideForward
                + vector.x * right
                + vector.y * up;
        }
    
        public static float CastToRange(this float value,
            float originalRangeMin, float originalRangeMax,
            float newRangeMin, float newRangeMax)
        {
            float relativet = Mathf.Clamp01((value - originalRangeMin) / (originalRangeMax - originalRangeMin));
            return  newRangeMin + relativet * (newRangeMax - newRangeMin);
        }
    
        public static float magnitudeXZ(this Vector3 vector)
        {
            return (float) Math.Sqrt((double) vector.x * (double) vector.x + (double) vector.z * (double) vector.z);
        }
        
        public static Vector3 ComponentClamp(this Vector3 vector, Vector3 clampingVactor)
        {
            return new Vector3(
                Mathf.Clamp(vector.x, Mathf.Min(clampingVactor.x, 0f), Mathf.Max(clampingVactor.x, 0f)),
                Mathf.Clamp(vector.y, Mathf.Min(clampingVactor.y, 0f), Mathf.Max(clampingVactor.y, 0f)),
                Mathf.Clamp(vector.z, Mathf.Min(clampingVactor.z, 0f), Mathf.Max(clampingVactor.z, 0f)));
        }
        
        public static Vector3 PreserveAngleClamp(this Vector3 vector, Vector3 clampingVactor)
        {
            float minMult = Mathf.Min( 
                Mathf.Clamp01(clampingVactor.x / vector.x),
                Mathf.Clamp01(clampingVactor.y / vector.y),
                    Mathf.Clamp01(clampingVactor.z / vector.z));
            
            return vector * minMult;
        }
    
        public static float sqrMagnitudeXZ(this Vector3 vector)
        {
            return (float) ((double) vector.x * (double) vector.x + (double) vector.z * (double) vector.z);
        }
    
        public static Vector3 SquareInterpolation(Vector3 a, Vector3 b, Vector3 c, float t)
        {
            return Vector3.Lerp(Vector3.Lerp(a, b, t), Vector3.Lerp(b, c, t), t);
        }
        
        private static Vector3 NonLinearInterpolate(List<Vector3> points, float t)
        {
            int pointCount = points.Count;

            for (int i = 1; i < pointCount; i++)
            {
                for (int j = 0; j < i - 1; j++)
                {
                    points[j] = Vector3.Lerp(points[j], points[j + 1], t);
                }
            }

            return points[0];
        }

        public static bool BoxCapsuleIntersect(BoxCollider box, CapsuleCollider capsule)
        {
            Vector3 p1 = capsule.transform.TransformPoint(capsule.center + Vector3.up * ((capsule.height / 2f) - capsule.radius));
            Vector3 p2 = capsule.transform.TransformPoint(capsule.center - Vector3.up * ((capsule.height / 2f) - capsule.radius));
            Vector3 c1 = box.center;
            return BoxCapsuleIntersect(p1, p2, capsule.radius,
                c1, box.size, box.transform,
                out Vector3 bx, out Vector3 bx2, out Vector3 rx, out Vector3 rx2);
        }

        public static bool BoxCapsuleIntersect(Vector3 ce1, Vector3 ce2, float cr,
                Vector3 bc, Vector3 bs, Transform bt,
                out Vector3 bx, out Vector3 bx2, out Vector3 rx, out Vector3 rx2)
            //,out Vector3 f1, out Vector3 f2, out Vector3 f3)
        {
            Vector3 bcg = bt.TransformPoint(bc);
            Vector3 cc = (ce1 + ce2) / 2f;
            Vector3 br = bt.right;
            Vector3 bu = bt.up;
            Vector3 bf = bt.forward;
            Vector3 bsc = bt.localScale;

            bx = bc;
            bx2 = cc;
            rx = bc;
        
            rx2 = GetClosestPointOnLine(ce1, ce2, rx);
        
            bool intersect = cr * cr > Vector3.Dot((rx - rx2), (rx - rx2));
            if (intersect) { return true; }
        
            Vector3 bcc = cc - bcg;
            float dotX = Vector3.Dot(br, bcc);
            float dotY = Vector3.Dot(bu, bcc);
            float dotZ = Vector3.Dot(bf, bcc);
        
            float dotXsign = Mathf.Sign(dotX);
            float dotYsign = Mathf.Sign(dotY);
            float dotZsign = Mathf.Sign(dotZ);

            bool xTraversed = false;
            bool yTraversed = false;
            bool zTraversed = false;
        
            dotX *= dotX;
            dotY *= dotY;
            dotZ *= dotZ;
        
            Vector3 xAxis = dotXsign * br * bsc.x * bs.x / 2f;
            Vector3 yAxis = dotYsign * bu * bsc.y * bs.y / 2f;
            Vector3 zAxis = dotZsign * bf * bsc.z * bs.z / 2f;
        
            Vector3 mainAxis;
            Vector3 secondaryAxis;
            Vector3 thirdAxis;

            if (dotX > dotY && dotX > dotZ) //X
            {
                mainAxis = xAxis;
                xTraversed = true;
            }
            else if (dotY > dotX && dotY > dotZ) //Y
            {
                mainAxis = yAxis;
                yTraversed = true;
            }
            else //Z
            {
                mainAxis = zAxis;
                zTraversed = true;
            }

            bx = bcg;
            bx2 = bcg + mainAxis;
        
            //First axis
            GetClosestPointsOnLines(bx, bx2, ce1, ce2, out rx, out rx2);
            rx2 = GetClosestPointOnLine(ce1, ce2, rx);
            rx = GetClosestPointOnLine(bx, bx2, rx2);

            //rx = bx;
            //rx2 = bx2;
        
            //return cr * cr > Vector3.Dot((rx - rx2), (rx - rx2));
            intersect = cr * cr > Vector3.Dot((rx - rx2), (rx - rx2));
            if (intersect) { return true; }
        
            dotX = xTraversed ? 0f : Vector3.Dot(xAxis, (rx2 - rx));
            dotY = yTraversed ? 0f : Vector3.Dot(yAxis, (rx2 - rx));
            dotZ = zTraversed ? 0f : Vector3.Dot(zAxis, (rx2 - rx));
        
            dotXsign = Mathf.Sign(dotX);
            dotYsign = Mathf.Sign(dotY);
            dotZsign = Mathf.Sign(dotZ);
        
            dotX *= dotX;
            dotY *= dotY;
            dotZ *= dotZ;

            if (xTraversed)
            {
                yTraversed = dotY > dotZ;
                zTraversed = !yTraversed;
                secondaryAxis = yTraversed ? dotYsign * yAxis : dotZsign * zAxis;
                thirdAxis = yTraversed ? zAxis : yAxis;
            }
            else if(yTraversed)
            {
                xTraversed = dotX > dotZ;
                zTraversed = !xTraversed;
                secondaryAxis = xTraversed ? dotXsign * xAxis : dotZsign * zAxis;
                thirdAxis = xTraversed ? zAxis : xAxis;
            }
            else //zTraveresed
            {
                xTraversed = dotX > dotY;
                yTraversed = !xTraversed;
                secondaryAxis = xTraversed ? dotXsign * xAxis : dotYsign * yAxis;
                thirdAxis = xTraversed ?  yAxis : xAxis;
            }
            
            bx = rx;
            bx2 = bx + secondaryAxis;
        
            //Second axis
            GetClosestPointsOnLines(bx, bx2, ce1, ce2, out rx, out rx2);
            rx2 = GetClosestPointOnLine(ce1, ce2, rx);
        
            intersect = cr * cr > Vector3.Dot((rx - rx2), (rx - rx2));
            if (intersect) { return true; }
        
            bx = bx2;
            bx2 = bx + thirdAxis;
        
            //Third axis
            GetClosestPointsOnLines(bx, bx2, ce1, ce2, out rx, out rx2);
            rx2 = GetClosestPointOnLine(ce1, ce2, rx);
        
            return cr * cr > Vector3.Dot((rx - rx2), (rx - rx2));
        }
    
        public static bool CapsuleCapsuleIntersect(CapsuleCollider capsule1, CapsuleCollider capsule2)
        {
            Vector3 p1 = capsule1.transform.TransformPoint(capsule1.center + Vector3.up * ((capsule1.height / 2f) - capsule1.radius));
            Vector3 p2 = capsule1.transform.TransformPoint(capsule1.center - Vector3.up * ((capsule1.height / 2f) - capsule1.radius));
        
            Vector3 p3 = capsule2.transform.TransformPoint(capsule2.center + Vector3.up * ((capsule2.height / 2f) - capsule2.radius));
            Vector3 p4 = capsule2.transform.TransformPoint(capsule2.center - Vector3.up * ((capsule2.height / 2f) - capsule2.radius));
            
            Vector3 lossyScale1 = capsule1.transform.lossyScale;
            Vector3 lossyScale2 = capsule2.transform.lossyScale;
            return CapsuleCapsuleIntersect(p1, p2, capsule1.radius * Mathf.Max(lossyScale1.x, lossyScale1.z),
                p3, p4, capsule2.radius * Mathf.Max(lossyScale2.x, lossyScale2.z));
        }

        public static bool CapsuleCapsuleIntersect(Vector3 ce1, Vector3 ce2, float cr1,
            Vector3 ce3, Vector3 ce4, float cr2)
        {
            GetClosestPointsOnLines(ce1, ce2, ce3, ce4,
                out Vector3 cx1, out Vector3 cx2);
            Vector3 normal = cx1 - cx2;
            float len = normal.sqrMagnitude;
            float depth = (cr1 + cr2) * (cr1 + cr2) - len;
            return depth > 0;
        }

        // Find the closest point on a line to a given point
        public static Vector3 GetClosestPointOnLine(Vector3 linePoint1, Vector3 linePoint2, Vector3 point)
        {
            Vector3 lineVector = linePoint2 - linePoint1;
            float lineLength = lineVector.magnitude;

            // If the line is very short (close to a point), return one of the line endpoints
            if (lineLength < 0.0001f)
            {
                return linePoint1;
            }

            // Calculate the normalized direction vector of the line
            Vector3 lineDirection = lineVector / lineLength;

            // Calculate the vector from the start of the line to the given point
            Vector3 pointVector = point - linePoint1;

            // Project the point vector onto the line direction to find the closest point on the line
            float dotProduct = Vector3.Dot(pointVector, lineDirection);
            dotProduct = Mathf.Clamp(dotProduct, 0f, lineLength); // Ensure the closest point is within the line segment

            // Calculate the closest point on the line
            Vector3 closestPoint = linePoint1 + lineDirection * dotProduct;

            return closestPoint;
        }
    
        // Find the two closest points on two finite lines
        public static void GetClosestPointsOnLines(Vector3 linePoint1, Vector3 linePoint2, Vector3 linePoint3, Vector3 linePoint4, out Vector3 result1, out Vector3 result2)
        {
            Vector3 d1 = linePoint2 - linePoint1;
            Vector3 d2 = linePoint4 - linePoint3;

            Vector3 n = Vector3.Cross(d1, d2);
            Vector3 n1 = Vector3.Cross(d1, n);
            Vector3 n2 = Vector3.Cross(d2, n);

            Vector3 x1 = linePoint1 +
                         ((Vector3.Dot((linePoint3 - linePoint1), n2)) /
                          (Vector3.Dot(d1, n2))) * d1;
        
            Vector3 x2 = linePoint3 +
                         ((Vector3.Dot((linePoint1 - linePoint3), n1)) /
                          (Vector3.Dot(d2, n1))) * d2;

            result1 = GetClosestPointOnLine(linePoint1, linePoint2, x1);
            result2 = GetClosestPointOnLine(linePoint3, linePoint4, x2);

            if (float.IsNaN(result1.x) || float.IsNaN(result1.y) || float.IsNaN(result1.z))
            {
                //Lines parrallel test edges of lines to find the closest pair
                n = (linePoint1 + linePoint2 + linePoint3 + linePoint4) / 4f;
                result1 = GetClosestPointOnLine(linePoint1, linePoint2, n);
                result2 = GetClosestPointOnLine(linePoint3, linePoint4, n);
                //TODO: merge with get closest earlier
            }
        }
        
        public static float ClampAngle(float angle, float from, float to)
        {
            angle -= ((int) angle / 360) * 360f;
        
            return Mathf.Clamp(angle, from, to);
        }
        
        public static Vector3 ProjectOnContactPlane (Vector3 vector, Vector3 normal) {
            return vector - normal * Vector3.Dot(vector, normal);
        }
        
        //public static Vector3 ProjectOnContactPlane (Vector3 vector, Vector3 normal)
        //{
        //    return Vector3.ProjectOnPlane(vector, normal);
        //}
    }
}
