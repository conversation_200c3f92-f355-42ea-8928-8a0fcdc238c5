namespace RibCageGames.MonoUtils
{
    using System;
    using DG.Tweening;
    using UnityEngine;

    [CreateAssetMenu(fileName = "MonoTweenerSettings", menuName = "RibCageGames/Utilities/MonoTweenerSettings")]
    public class MonoTweenerSettings : ScriptableObject
    {
        [SerializeField] protected TweenerType m_tweenType;
        [SerializeField] protected Vector3 m_targetValue = Vector3.zero;
        [SerializeField] protected float m_duration = 1f;
        [SerializeField] protected float m_delay;
        [SerializeField] protected Ease m_ease;
        [SerializeField] protected bool m_from = false;
        [SerializeField] protected bool m_relative = false;
        [SerializeField] protected bool m_incremental = false;
        [SerializeField] protected bool m_effectsGrounded = true;
        
        protected TweenerType TweenType
        {
            get { return m_tweenType; }
            set { m_tweenType = value; }
        }

        public Vector3 TargetValue => m_targetValue;
        public float Delay => m_delay;
        public float Duration => m_duration;
        public Ease Ease => m_ease;
        public bool From => m_from;
        public bool Relative => m_relative;
        public bool EffectsGrounded => m_effectsGrounded;

        public Sequence Run(Transform targetTransform, Action onComplete = null)
        {
            return RunWithValue(targetTransform, m_targetValue, onComplete);
        }
        
        //TODO: Possibly take a transform or position as a reference too to allow "snap-to-pos" tweens 
        public Sequence RunDirected(Transform targetTransform, Vector3 forward, Action onComplete = null, Transform otherEntity = null, Func<Transform> getDynamicTarget = null, Action<Vector3> outputAction = null)
        {
            //TODO: use cancellation token
            forward = forward.normalized;
            Vector3 right = new Vector3(forward.z, forward.y, -forward.x);
            Vector3 up = Vector3.Cross(forward, right);
            return RunWithValue(targetTransform,
                m_targetValue.TransformByForward(forward),
                onComplete,
                forward,
                otherEntity,
                getDynamicTarget,
                //TODO: make out functions for position and rotation
                outputAction);
        }

        public virtual Sequence RunWithValue(Transform targetTransform, Vector3 value,
            Action onComplete = null, Vector3 overrideForward = new Vector3(), Transform otherEntity = null,
            Func<Transform> getDynamicTarget = null, Action<Vector3> outputAction = null)
        {
            Sequence sequence = DOTween.Sequence(targetTransform);
            Tweener tween = null;
            switch (TweenType)
            {
                case TweenerType.Move:
                    tween = TweenerMove(targetTransform, m_relative
                            ? targetTransform.position
                              + value
                            : value,
                        m_duration,
                        overrideForward,
                        otherEntity);
                    break;
                case TweenerType.Rotate:
                    tween = TweenerRotate(targetTransform,
                        m_relative
                            ? targetTransform.rotation.eulerAngles
                              + value
                            : value,
                        m_duration,
                        otherEntity);
                    break;
                case TweenerType.Scale:
                    tween = TweenerScale(targetTransform,
                        m_relative
                            ? targetTransform.localScale
                              + value
                            : value,
                        m_duration,
                        otherEntity);
                    break;
            }

            tween?.SetDelay(m_delay);
            tween?.SetEase(m_ease);
            if (m_from)
            {
                tween.From();
            }

            if (tween != null && onComplete != null)
            {
                tween.onComplete += onComplete.Invoke;
            }

            sequence.Insert(0f, tween);
            return sequence;
        }

        protected virtual Tweener TweenerMove(Transform targetTransform, Vector3 value, float duration,
            Vector3 overrideForward = new Vector3(), Transform otherEntity = null,
            Action<Vector3> outputAction = null)
        {
            if (m_incremental)
            {
                Vector3 initialPosition = targetTransform.position;
                Vector3 savedValue = initialPosition;

                Tweener tweener = DOVirtual.Vector3(initialPosition, value, duration, newPos =>
                {
                    targetTransform.position += newPos - savedValue;
                    savedValue = newPos;
                });

                return tweener;
            }
            else
            {
                return targetTransform.DOMove(value, duration);
            }
        }

        protected virtual Tweener TweenerRotate(Transform targetTransform, Vector3 value, float duration,
            Transform otherEntity = null)
        {
            return targetTransform.DORotate(value, duration);
        }

        protected virtual Tweener TweenerScale(Transform targetTransform, Vector3 value, float duration,
            Transform otherEntity = null)
        {
            return targetTransform.DOScale(value, duration);
        }
    }

    public enum TweenerType
    {
        Move = 1,
        Rotate = 2,
        Scale = 3,
    }
}