using System;
using System.Threading;

namespace RibCageGames.MonoUtils
{
    using System.Collections;
    using System.Collections.Generic;
    using DG.Tweening;
    using UnityEngine;

    [CreateAssetMenu(fileName = "MonoTweenClamp", menuName = "RibCageGames/Utilities/MonoTweenClamp")]
    public class MonoTweenerClamp : MonoTweenerSettings
    {
        [SerializeField] private Vector3 m_maxOffset;
        [SerializeField] private Vector3 m_minOffset;

        protected override Tweener TweenerMove(Transform targetTransform, Vector3 value, float duration,
            Vector3 overrideForward = new Vector3(), Transform otherEntity = null,
            Action<Vector3> outputAction = null)
        {
            if (otherEntity == null)
            {
                return DOVirtual.Vector3(Vector3.zero, Vector3.zero, 0f, _ => { });
            }
            Vector3 basePosition = otherEntity.position;
            Vector3 delta = targetTransform.position - basePosition;
            
            Vector3 forward = delta.normalized;
            forward.y = 0f;
            Vector3 up = Vector3.up;
            
            forward *= Mathf.Clamp(forward.magnitude, m_minOffset.z, m_maxOffset.z);
            up = Vector3.up * Mathf.Clamp(delta.y, m_minOffset.y, m_maxOffset.y);

            Vector3 final = basePosition + forward + up;
                        
            if (m_incremental)
            {
                Vector3 initialPosition = targetTransform.position;
                Vector3 savedValue = initialPosition;
                
                Tweener tweener = DOVirtual.Vector3(initialPosition, final, duration, newPos =>
                {
                    targetTransform.position += newPos - savedValue;
                    savedValue = newPos;
                });

                return tweener;
            }
            else
            {
                return targetTransform.DOMove(final, duration);
            }
        }

        protected override Tweener TweenerRotate(Transform targetTransform, Vector3 value, float duration,
            Transform otherEntity = null)
        {
            throw new System.NotImplementedException();
        }

        protected override Tweener TweenerScale(Transform targetTransform, Vector3 value, float duration,
            Transform otherEntity = null)
        {
            throw new System.NotImplementedException();
        }
    }
}