namespace RibCageGames.MonoUtils
{
    using System;
    using System.Collections.Generic;
    using DG.Tweening;
    using UnityEngine;

    [CreateAssetMenu(fileName = "MonoTweenGroup", menuName = "RibCageGames/Utilities/MonoTweenGroup")]
    public class MonoTweenGroup : MonoTweenerSettings
    {
        [SerializeField] private List<MonoTweenerSettings> tweens;
        [SerializeField] private bool m_runSequential = true;

        public override Sequence RunWithValue(Transform targetTransform, Vector3 value,
            Action onComplete = null, Vector3 overrideForward = new Vector3(), Transform otherEntity = null,
            Func<Transform> getDynamicTarget = null, Action<Vector3> outputAction = null)
        {
            Sequence tweenSequence = DOTween.Sequence(targetTransform);
            
            Vector3 forward = overrideForward.normalized;
            Vector3 right = new Vector3(forward.z, forward.y, -forward.x);
            Vector3 up = Vector3.Cross(forward, right);
            float tweenTime = 0f;

            if (m_runSequential)
            {
                foreach (MonoTweenerSettings tween in tweens)
                {
                    tweenSequence.Insert(tweenTime,
                        tween.RunWithValue(targetTransform,
                            tween.TargetValue.TransformByForward(forward),
                            onComplete,
                            forward,
                            otherEntity));
                    tweenTime += tween.Duration;
                }
            }
            else
            {
                foreach (MonoTweenerSettings tween in tweens)
                {
                    tweenSequence.Join(tween.RunWithValue(targetTransform,
                            tween.TargetValue.TransformByForward(forward),
                            onComplete,
                            forward,
                            otherEntity));
                    tweenTime += tween.Duration;
                }
            }

            return tweenSequence;
        }
    }
}
