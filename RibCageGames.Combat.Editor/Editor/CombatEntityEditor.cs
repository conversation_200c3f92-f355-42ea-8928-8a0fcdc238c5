namespace RibCageGames.Combat.Editor
{
#if UNITY_EDITOR
    using System.Collections;
    using System.Collections.Generic;
    using RibCageGames.Combat;
    using UnityEditor;
    using UnityEngine;

//TODO:
//- Get list of animation states for names of ActionData
//- Make a dropdown showing all ActionData to show either other datas going to or from each one
    [CustomEditor(typeof(CombatEntity))]
    public class CombatEntityEditor : Editor
    {

        private SerializedProperty m_combatActionsprop;

        private void OnEnable()
        {
            m_combatActionsprop = serializedObject.FindProperty("m_combatActions");
        }

        public override void OnInspectorGUI()
        {

            serializedObject.UpdateIfRequiredOrScript();

            //m_sameLengthHouses = EditorGUILayout.Toggle("Same Length Houses", m_sameLengthHouses);

            //All houses, vertical
            //EditorGUILayout.BeginVertical();



            //Single house
            //EditorGUILayout.; 


            //EditorGUILayout.EndVertical();

            base.OnInspectorGUI();
            serializedObject.ApplyModifiedProperties();
        }
    }
    #endif
}