namespace RibCageGames.Combat.Editor
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Linq;
    using UnityEditor;
    using UnityEngine;
    using RibCageGames.Editor;
    using RibCageGames.MonoUtils;


    [CustomPropertyDrawer(typeof(ComponentReferenceAccessorAttribute), true)]
    public class ComponentReferenceAccessorPropertyDrawer : PropertyDrawer
    {
        private CombatEntity m_combatEntity = null;
        private List<string> m_componentNames = null;
        private string m_currentComponentName;
        private float m_propertyHeight = 16;

        private ComponentReferenceAccessorAttribute TargetAttribute => (ComponentReferenceAccessorAttribute) attribute;

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            return m_propertyHeight;
        }

        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            SerializedProperty collectionProp = property.serializedObject.FindProperty(TargetAttribute.CollectionPath);
            SerializedProperty selectedProp = property.FindPropertyRelative(TargetAttribute.VariableName);
            SerializedProperty timeProp = null;
            SerializedProperty effectEndProp = null;
            SerializedProperty tweenerProp = null;
            SerializedProperty severityProp = null;
            SerializedProperty damageModProp = null;
            SerializedProperty faceSourceProp = null;
            
            SerializedProperty beatSyncProp = null; //TODO: remove is temporary
            
            Type collectionType = TargetAttribute.ReferenceType;

            m_propertyHeight = EditorGUIUtility.singleLineHeight;
            m_combatEntity = (CombatEntity) collectionProp.objectReferenceValue;
            property.serializedObject.ApplyModifiedProperties();

            Rect parameterNameDropdownPosition =
                new Rect(position.x, position.y, position.width, EditorGUIUtility.singleLineHeight);

            if (m_combatEntity != null)
            {

                List<string> componentNames = null;

                if (collectionType == typeof(Collider))
                {
                    componentNames = ((ISceneReferenceCollection<Collider>) m_combatEntity).GetItems()
                        .Where(component => component != null)
                        .Select(component => component.gameObject.name).ToList();
                    timeProp = property.FindPropertyRelative("m_hitBoxStartTime");
                    tweenerProp = property.FindPropertyRelative("m_hitMoveEffect");
                    severityProp = property.FindPropertyRelative("m_hitSeverity");
                    damageModProp = property.FindPropertyRelative("m_damageMultiplier");
                    faceSourceProp = property.FindPropertyRelative("m_faceDamageSource");
                    beatSyncProp = property.FindPropertyRelative("beatSync");
                }
                else if (collectionType == typeof(GameObject))
                {
                    componentNames = ((ISceneReferenceCollection<GameObject>) m_combatEntity).GetItems()
                        .Where(component => component != null)
                        .Select(component => component.gameObject.name).ToList();
                    timeProp = property.FindPropertyRelative("m_effectStartTime");
                    beatSyncProp = property.FindPropertyRelative("beatSync");
                    effectEndProp = property.FindPropertyRelative("m_stopOnMoveEnd");
                }

                if (componentNames != null && componentNames.Count > 0)
                {
                    m_componentNames = componentNames;
                }
                else if (m_componentNames == null)
                {
                    m_componentNames = new List<string>();
                }

                for (int i = 0; i < m_componentNames.Count; i++)
                {
                    if (i == selectedProp.intValue)
                    {
                        m_currentComponentName = m_componentNames[i];
                    }
                }

                if (string.IsNullOrEmpty(m_currentComponentName) && m_componentNames.Count > 0)
                {
                    m_currentComponentName = m_componentNames[0];
                }

                if (m_currentComponentName == null)
                {
                    m_propertyHeight += EditorGUIUtility.singleLineHeight + 2;

                    EditorGUI.indentLevel--;

                    EditorGUI.EndProperty();
                    return;
                }

                if (GUI.Button(parameterNameDropdownPosition,
                    string.IsNullOrEmpty(m_currentComponentName)
                        ? "Parameter not present in animator"
                        : m_currentComponentName,
                    EditorStyles.popup))
                {
                    GenericMenu menu = new GenericMenu();
                    foreach (string componentName in m_componentNames)
                    {
                        menu.AddItem(new GUIContent(componentName),
                            componentName == m_currentComponentName,
                            enumValue =>
                            {
                                m_currentComponentName = componentName;
                                selectedProp.intValue = m_componentNames.IndexOf(m_currentComponentName);

                                property.serializedObject.ApplyModifiedProperties();
                            }, componentName);
                    }

                    menu.ShowAsContext();
                }


                if (timeProp != null)
                {
                    EditorGUI.PropertyField(
                        new Rect(position.x, position.y + m_propertyHeight + 2, position.width,
                            EditorGUIUtility.singleLineHeight),
                        timeProp);
                }
                
                if (effectEndProp != null)
                {
                    EditorGUI.PropertyField(
                        new Rect(position.x, position.y + 2 * m_propertyHeight + 4, position.width,
                            EditorGUIUtility.singleLineHeight),
                        effectEndProp);
                        m_propertyHeight += EditorGUIUtility.singleLineHeight - 2;
                }

                if (tweenerProp != null)
                {
                    EditorGUI.PropertyField(
                        new Rect(position.x, position.y + 2 * m_propertyHeight + 4, position.width,
                            EditorGUIUtility.singleLineHeight),
                        tweenerProp);
                    m_propertyHeight += EditorGUIUtility.singleLineHeight + 2;
                }
                
                //TODO: figure out property heights which are a fluster cuck
                if (severityProp != null)
                {
                    EditorGUI.PropertyField(
                        new Rect(position.x, position.y + 2 * m_propertyHeight - 16, position.width,
                            EditorGUIUtility.singleLineHeight),
                        severityProp);
                    m_propertyHeight += EditorGUIUtility.singleLineHeight + 2;
                }
                
                if (damageModProp != null)
                {
                    EditorGUI.PropertyField(
                        new Rect(position.x, position.y + 2 * m_propertyHeight - 36, position.width,
                            EditorGUIUtility.singleLineHeight),
                        damageModProp);
                    m_propertyHeight += EditorGUIUtility.singleLineHeight + 2;
                }
                
                if (faceSourceProp != null)
                {
                    EditorGUI.PropertyField(
                        new Rect(position.x, position.y + 2 * m_propertyHeight - 56, position.width,
                            EditorGUIUtility.singleLineHeight),
                        faceSourceProp);
                    m_propertyHeight += EditorGUIUtility.singleLineHeight + 2;
                }
                
                if (beatSyncProp != null)
                {
                    if (effectEndProp != null)
                    {
                        EditorGUI.PropertyField(
                            new Rect(position.x, position.y + m_propertyHeight + 24, position.width,
                                EditorGUIUtility.singleLineHeight),
                            beatSyncProp, true);
                        m_propertyHeight += beatSyncProp.isExpanded
                            ? (4 * (EditorGUIUtility.singleLineHeight + 2) + 6)
                            : EditorGUIUtility.singleLineHeight + 2;
                    }
                    else
                    {
                        EditorGUI.PropertyField(
                            new Rect(position.x, position.y + 2 * m_propertyHeight - 80, position.width,
                                EditorGUIUtility.singleLineHeight),
                            beatSyncProp, true);
                        m_propertyHeight += beatSyncProp.isExpanded
                            ? 4 * (EditorGUIUtility.singleLineHeight + 2)
                            : EditorGUIUtility.singleLineHeight + 2;
                    }
                }

                m_propertyHeight += EditorGUIUtility.singleLineHeight + 2;

                EditorGUI.indentLevel--;

                EditorGUI.EndProperty();
                //property.serializedObject.ApplyModifiedProperties();

            }
        }
    }
}