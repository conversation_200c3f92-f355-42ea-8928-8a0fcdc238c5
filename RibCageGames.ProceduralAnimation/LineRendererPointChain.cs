using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

public class LineRendererPointChain : ProceduralAnimationPointChain
{
    [SerializeField] private List<LineRendererBone> m_bones;
    [SerializeField] private Transform m_transformTarget;
    [SerializeField] private bool m_matchTransform;
    [SerializeField] private LineRenderer m_lineRenderer;
    [SerializeField] private bool m_positionReset;
    
    [SerializeField] private bool m_lazyMatch;
    [SerializeField] private float m_movementThreshold;
    [SerializeField] private float m_restThreshold;
    [SerializeField] private float m_matchingSpeed;

    public Vector3 EndPoint => m_bones[m_bones.Count - 1].EndPosition;
    
    public bool Stable;
    public bool Anchor;

    public Vector3 BasePosition => m_bones[0].BasePosition;

    protected override void Start()
    {
        base.Start();
        RecalculateBones();
        //m_lineRenderer.useWorldSpace = false;
        Target = m_transformTarget.position;

    }

    private bool matchingPosition = false;
    
    //TODO make target transition trigger clearer and in a method
    protected override void ControlledUpdate(float deltaTime)
    {
        m_bones[0].BasePosition = transform.position;
        UpdatePointPositions(m_bones);
        
        if (m_matchTransform)
        {
            Target = m_transformTarget.position;
        }
        else
        {
            m_transformTarget.position = m_bones[m_bones.Count - 1].EndPosition;
        }
    }
    
    protected void ControlledUpdateOld(float deltaTime)
    {
        m_bones[0].BasePosition = transform.position;
        if (m_lazyMatch)
        {
            float targetDistance = (Target - m_transformTarget.position).magnitude;
            if (!matchingPosition && targetDistance > m_movementThreshold)
            {
                //Start matching
                Stable = false;
                if (!Anchor)
                {
                    matchingPosition = true;
                }
            }
            else if (matchingPosition && targetDistance < m_restThreshold)
            {
                //Stop matching
                Stable = true;
                matchingPosition = false;
            }

            if (matchingPosition)
            {
                Vector3 currentTarget = Target;
                
                Target = currentTarget + (m_transformTarget.position - currentTarget).normalized * deltaTime * m_matchingSpeed;
                m_transformTarget.position = Target;
            }
        }
        else
        {
            Target = m_transformTarget.position;    
        }
        
        UpdatePointPositions(m_bones);
    }
    
    public override void RecalculateBones()
    {
        base.RecalculateBones(m_bones);
    }

    [ContextMenu("UpdateChain")]
    public void UpdateChain()
    {
        UpdatePointPositions(m_bones);   
    }

    [ContextMenu("SetChainData")]
    private void SetChainData()
    {
        for (int i = 0; i < m_bones.Count; i++)
        {
            m_bones[i].BasePositionDirect = m_lineRenderer.useWorldSpace ? 
                m_lineRenderer.GetPosition(i) : 
                transform.TransformPoint(m_lineRenderer.GetPosition(i));
            m_bones[i].EndPositionDirect = m_lineRenderer.useWorldSpace ?
                m_lineRenderer.GetPosition(i + 1) :
                transform.TransformPoint(m_lineRenderer.GetPosition(i + 1));
        }

        m_lineRenderer.useWorldSpace = true;
        for (int i = 0; i < m_bones.Count; i++)
        {
            m_bones[i].BasePosition = m_bones[i].BasePositionDirect;
            m_bones[i].EndPosition = m_bones[i].EndPositionDirect;
        }

        //for (int i = 0; i < m_bones.Count; i++)
        //{
        //    m_bones[i].EndPosition =
        //        m_bones[i].BasePosition
        //        + m_bones[i].BaseRotation * Vector3.forward * m_bones[i].Length;
        //    
        //    m_bones[i + 1].BasePosition = m_bones[i].EndPosition;
        //}
    }

    private void OnDrawGizmos()
    {
        //return;
        foreach (LineRendererBone bone in m_bones)
        {
            //Gizmos.color = Color.blue;
            //Gizmos.DrawLine(bone.BasePosition, bone.BasePosition + bone.DebugProjectedTarget);
            
            Gizmos.color = Color.red;
            Gizmos.DrawLine(bone.BasePosition, bone.BasePosition + bone.NegDebugRotation * Vector3.forward * 0.2f);
            
            Gizmos.color = Color.yellow;
            Gizmos.DrawLine(bone.BasePosition, bone.BasePosition + bone.DebugNatVector * 0.3f);
            
            Gizmos.color = Color.cyan;
            Gizmos.DrawLine(bone.BasePosition, bone.BasePosition + bone.DebugProjectedTarget);
            
            Gizmos.color = bone.PosValid ? Color.green : Color.magenta;
            Gizmos.DrawLine(bone.BasePosition, bone.BasePosition + bone.PosDebugRotation * Vector3.forward * 0.1f);
            
            Gizmos.color = Color.blue;
            Gizmos.DrawLine(bone.BasePosition, bone.BasePosition + bone.DebugRotAxis * 0.1f);
            
            //Gizmos.color = bone.PosValid ? Color.green : Color.red;
            //Gizmos.DrawLine(bone.BasePosition, bone.BasePosition + bone.PosDebugRotation * Vector3.forward * 0.15f);
            
            //Gizmos.color = bone.NegValid ? Color.green : Color.red;
            //Gizmos.DrawLine(bone.BasePosition, bone.BasePosition + bone.NegDebugRotation * Vector3.forward * 0.15f);
        }
    }

    [SerializeField] private bool m_editorMatch;
    private void OnValidate()
    {
        if (m_editorMatch)
        {
            m_editorMatch = false;
            //m_bones[0].BasePosition = transform.position;
            RecalculateBones();
            Target = m_transformTarget.position;
            UpdatePointPositions(m_bones);    
        }
        return;
        UpdatePointPositions(m_bones);
        for (var i = 0; i < m_bones.Count; i++)
        {
            LineRendererBone bone = m_bones[i];
            bone.lineRenderer = m_lineRenderer;
            bone.pointIndex = i;
        }

        m_bones[0].BasePosition = transform.position;
        
        for (int i = 0; i < m_bones.Count; i++)
        {
            m_bones[i].BasePosition = m_bones[i].BasePositionDirect;
            //m_bones[i].EndPosition = m_bones[i].EndPositionDirect;
        }

        if (m_positionReset)
        {
            m_positionReset = false;

            RecalculateBones();
        }
    }
    
    [Serializable]
    public class LineRendererBone : Bone
    {
        [SerializeField] private LineRenderer m_lineRenderer;
        [SerializeField] private int m_pointIndex;
        
        internal LineRenderer lineRenderer
        {
            get => m_lineRenderer;
            set => m_lineRenderer = value;
        }

        internal int pointIndex
        {
            get => m_pointIndex;
            set => m_pointIndex = value;
        }

        public override Vector3 BasePosition
        {
            get => m_lineRenderer.GetPosition(m_pointIndex);
            set
            {
                m_lineRenderer.SetPosition(m_pointIndex, value);
                m_basePosition = value;
            }
        }

        public override Vector3 EndPosition
        {
            get => m_lineRenderer.GetPosition(m_pointIndex + 1);
            set => m_lineRenderer.SetPosition(m_pointIndex + 1, value);
        }
        
        public Vector3 BasePositionDirect
        {
            get => m_basePosition;
            set => m_basePosition = value;
        }

        public Vector3 EndPositionDirect
        {
            get => m_endPosition;
            set => m_endPosition = value;
        }
    }
}

