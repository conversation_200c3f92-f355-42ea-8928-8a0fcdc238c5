using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

#if UNITY_EDITOR
[CanEditMultipleObjects]
#endif

public class PAIKBase : MonoBehaviour
{

    [SerializeField] private List<Bone> m_bones;

    [SerializeField] private float m_maxLength;

    [SerializeField] private Transform m_target;

    [SerializeField] private bool m_activate;
    
    [SerializeField] private bool m_positionReset;
    


    private void Update()
    {
        //if (m_activate)
        //{
            //m_activate = false;
            UpdateBones();
        //}
    }

    // Update is called once per frame
    private void UpdateBones()
    {
        float remainingLength = m_maxLength;
        for (int i = 0; i < m_bones.Count; i++)
        {
            Bone current = m_bones[i];
            remainingLength -= current.Length;

            if (m_bones.Count == i + 1) //last bone
            {
                current.BoneTransform.LookAt(m_target.position);
                current.BoneEdge.position = current.BoneTransform.position
                                            + (Quaternion.Euler(current.BoneTransform.eulerAngles)
                                               * (current.Length * Vector3.forward));
                break;
            }

            Vector3 targetAxis = m_target.position - current.BoneTransform.position;
            float targetDistance = Mathf.Min(targetAxis.magnitude, remainingLength + current.Length);

            float leftoverLength = 0f;

            
            if (current.Length > targetDistance)
            {
                leftoverLength = remainingLength - ((remainingLength - current.Length + targetDistance) *
                                                    (remainingLength / (remainingLength + targetDistance)) *
                                                    (1f - current.LeftoverRatio));
            }
            else
            {
                leftoverLength = remainingLength - ((remainingLength + current.Length - targetDistance) *
                                                    (remainingLength / (remainingLength + current.Length)) *
                                                    (1f - current.LeftoverRatio));
            }
            
            leftoverLength = Mathf.Clamp(leftoverLength, Mathf.Abs(current.Length - targetDistance),
                current.Length + targetDistance);

            float cosA = ((leftoverLength * leftoverLength)
                          - (current.Length * current.Length)
                          - (targetDistance * targetDistance)) /
                         (-2 * current.Length * targetDistance);

            cosA = Mathf.Clamp01(cosA);

            float angle = Mathf.Rad2Deg * Mathf.Acos(cosA);
            
            current.BoneTransform.localRotation = Quaternion.identity;
            
            current.BoneTransform.LookAt(m_target.position);
            
            Vector3 rotAxis = current.RotationAxis.normalized;
            
            Quaternion rot = current.BoneTransform.localRotation;
            
            //rot = Quaternion.Euler(Vector3.Project(current.BoneTransform.localRotation.eulerAngles, rotAxis));
            
            
            Quaternion natRot = Quaternion.Euler(rotAxis * current.NaturalAngle);

            Quaternion posRot = rot * Quaternion.Euler(rotAxis * angle);
            Quaternion negRot = rot * Quaternion.Euler(rotAxis * (- angle));
                        
            Vector3 natHeading = natRot * Vector3.forward;
            float posHeading = ((posRot * Vector3.forward) - natHeading).sqrMagnitude;
            float negHeading = ((negRot * Vector3.forward) - natHeading).sqrMagnitude;

            rot = posHeading < negHeading ? posRot : negRot;
            
            
            
            current.BoneTransform.localRotation = rot;
            
            //Position next bone start
            current.BoneEdge.position =
                current.BoneTransform.position
                + current.BoneTransform.forward * current.Length;

        }
    }
    
    private float ClampAngle(float angle, float from, float to)
    {
        //if (angle < 0f) {angle = 360 + angle;}

        angle -= ((int) angle / 360) * 360f;
        
        return Mathf.Clamp(angle, from, to);
    }

    [ContextMenu("TestClampAngle")]
    public void TestClampAngle()
    {
        Debug.Log($"{370f} clamped: {ClampAngle(370f, 0f, 360f)}");
        Debug.Log($"{-370f} clamped: {ClampAngle(-370f, 0f, 360f)}");
        Debug.Log($"{170f} clamped: {ClampAngle(170f, 0f, 360f)}");
        Debug.Log($"{-170f} clamped: {ClampAngle(-170f, 0f, 360f)}");
        Debug.Log($"{350f} clamped: {ClampAngle(350f, 0f, 360f)}");
        Debug.Log($"{-350f} clamped: {ClampAngle(350f, 0f, 360f)}");
        
    }

    private void OnValidate()
    {
        for (int i = 0; i < m_bones.Count - 1; i++)
        {
            if (m_bones[i].m_boneEdge == null)
            {
                m_bones[i].m_boneEdge = m_bones[i + 1].BoneTransform;
            }
            else if(m_bones[i + 1].BoneTransform == null)
            {
                m_bones[i + 1].m_boneTransform = m_bones[i].m_boneEdge;
            }
        }

        if (m_activate)
        {
            m_activate = false;
            UpdateBones();
        }
        
        if (m_positionReset)
        {
            m_positionReset = false;

            RecalculateBones();
        }
    }
    
    [ContextMenu("ResetChain")]
    private void ResetChain()
    {
        for (int i = 0; i < m_bones.Count; i++)
        {
            m_bones[i].BoneTransform.rotation = Quaternion.identity;
            
            m_bones[i].BoneEdge.position =
                m_bones[i].BoneTransform.position
                + m_bones[i].BoneTransform.forward * m_bones[i].Length;
        }
    }

    private void RecalculateBones()
    {
        for (int i = 0; i < m_bones.Count - 1; i++)
        {
            m_bones[i].m_boneEdge = m_bones[i + 1].BoneTransform;
        }

        m_maxLength = 0f;
            
        foreach (Bone t in m_bones)
        {
            t.m_length = (t.m_boneEdge.position - t.BoneTransform.position).magnitude;
            m_maxLength += t.m_length;
        }

        float remainingLength = m_maxLength;
        for (int i = 0; i < m_bones.Count; i++)
        {
            Bone t = m_bones[i];

            if (i == m_bones.Count - 1)
            {
                t.m_leftoverRatio = 0f;
            }
            else if (i == m_bones.Count - 2)
            {
                t.m_leftoverRatio = 1f;
            }
            else
            {
                t.m_leftoverRatio = (t.Length / remainingLength);
                remainingLength -= t.Length;
            }
        }
    }

    [Serializable]
    public class Bone
    {
        [SerializeField] internal Transform m_boneTransform;
        [SerializeField] internal Transform m_boneEdge;
        [SerializeField] internal float m_length;
        [HideInInspector][SerializeField] internal float m_leftoverRatio;
        [SerializeField] private Vector3 m_rotationAxis;
        [SerializeField] private float m_minimalAngle = -90f;
        [SerializeField] private float m_maximalAngle = 90f;
        [SerializeField] private float m_naturalAngle = 0f;
        
        public float NaturalAngle => m_naturalAngle;
        public Transform BoneTransform => m_boneTransform;
        public Transform BoneEdge => m_boneEdge;
        public float Length => m_length;
        public Vector3 RotationAxis => m_rotationAxis;
        public float LeftoverRatio => m_leftoverRatio;
        
        public float MinimalAngle => m_minimalAngle;
        public float MaximalAngle => m_maximalAngle;
    }
}
