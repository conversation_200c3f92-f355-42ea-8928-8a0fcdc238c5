using System;
using UnityEngine;

[Serializable]
public class Bone
{
    [SerializeField] internal Vector3 m_basePosition;
    [SerializeField] internal Vector3 m_endPosition;
    [SerializeField] internal float m_length;
    [HideInInspector][SerializeField] internal float m_leftoverRatio;
    [SerializeField] private Vector3 m_rotationAxis;
    [SerializeField] private float m_minimalAngle = -90f;
    [SerializeField] private float m_maximalAngle = 90f;
    [SerializeField] private float m_naturalAngle = 0f;
    [SerializeField] private bool m_allowAngleFlipping;
    
    public float NaturalAngle => m_naturalAngle;
    
    public virtual Vector3 BasePosition
    {
        get => m_basePosition;
        set => m_basePosition = value;
    }

    public virtual Vector3 EndPosition
    {
        get => m_endPosition;
        set => m_endPosition = value;
    }
    
    public virtual Quaternion BaseRotation
    {
        get;
        set;
    }
    
    public virtual bool AllowFlipping
    {
        get => m_allowAngleFlipping;
        set => m_allowAngleFlipping = value;
    }
    
    public virtual Quaternion NatDebugRotation
    {
        get;
        set;
    }
    
    public virtual Quaternion PosDebugRotation
    {
        get;
        set;
    }
    
    public virtual Quaternion NegDebugRotation
    {
        get;
        set;
    }
    
    public virtual Vector3 DebugRotAxis
    {
        get;
        set;
    }
    
    public virtual Vector3 DebugNatVector
    {
        get;
        set;
    }
    
    public virtual Vector3 DebugProjectedTarget
    {
        get;
        set;
    }
    
    public virtual bool PosValid
    {
        get;
        set;
    }
    
    public virtual bool NegValid
    {
        get;
        set;
    }

    public virtual Quaternion EndRotation
    {
        get;
        set;
    }

    public float Length => m_length;
    public Vector3 RotationAxis => m_rotationAxis;
    public float LeftoverRatio => m_leftoverRatio;
        
    public float MinimalAngle => m_minimalAngle;
    public float MaximalAngle => m_maximalAngle;
}