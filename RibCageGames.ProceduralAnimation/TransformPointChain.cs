using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

public class TransformPointChain : ProceduralAnimationPointChain
{
    [SerializeField] private List<TransformBone> m_bones;
    [SerializeField] private Transform m_transformTarget;
    [SerializeField] private bool m_positionReset;
    
    [SerializeField] private bool m_lazyMatch;
    [SerializeField] private float m_movementThreshold;
    [SerializeField] private float m_restThreshold;
    [SerializeField][Range(0f,1f)] private float m_matchingSpeed;

    protected override void Start()
    {
        base.Start();
        RecalculateBones();
        Target = m_transformTarget.position;
    }


    private bool matchingPosition = false;
    protected override void ControlledUpdate(float deltaTime)
    {
        if (m_lazyMatch)
        {
            float targetDistance = (Target - m_transformTarget.position).magnitude;
            if (!matchingPosition && targetDistance > m_movementThreshold)
            {
                //Start matching
                matchingPosition = true;
            }
            else if (matchingPosition && targetDistance < m_restThreshold)
            {
                //Stop matching
                matchingPosition = false;
            }

            if (matchingPosition)
            {
                Target = Vector3.Lerp(Target, m_transformTarget.position, m_matchingSpeed);
            }
        }
        else
        {
            Target = m_transformTarget.position;    
        }
        
        UpdatePointPositions(m_bones);
    }
    
    public override void RecalculateBones()
    {
        base.RecalculateBones(m_bones);
        for (int i = 0; i < m_bones.Count - 1; i++)
        {
            m_bones[i].m_boneEdge = m_bones[i + 1].BoneTransform;
        }
    }

    [ContextMenu("ResetChain")]
    private void ResetChain()
    {
        for (int i = 0; i < m_bones.Count; i++)
        {
            m_bones[i].BoneTransform.rotation = Quaternion.identity;
            
            m_bones[i].BoneEdge.position =
                m_bones[i].BoneTransform.position
                + m_bones[i].BoneTransform.forward * m_bones[i].Length;
        }
    }
    
    private void OnValidate()
    {
        for (int i = 0; i < m_bones.Count - 1; i++)
        {
            if (m_bones[i].m_boneEdge == null)
            {
                m_bones[i].m_boneEdge = m_bones[i + 1].BoneTransform;
            }
            else if(m_bones[i + 1].BoneTransform == null)
            {
                m_bones[i + 1].m_boneTransform = m_bones[i].m_boneEdge;
            }
        }
        
        if (m_positionReset)
        {
            m_positionReset = false;

            RecalculateBones();
        }
    }
    
    [Serializable]
    public class TransformBone : Bone
    {
        [SerializeField] internal Transform m_boneTransform;
        [SerializeField] internal Transform m_boneEdge;
        
        public override Vector3 BasePosition
        {
            get
            {
                return m_boneTransform.position;
                //return m_basePosition;
            }
            set
            {
                m_boneTransform.position = value;
                //m_basePosition = value;
            }
        }

        public override Vector3 EndPosition
        {
            get
            {
                return m_boneEdge.position;
                //return m_endPosition;
            }
            set
            {
                m_boneEdge.position = value;
                //m_endPosition = value;
            }
        }
        
        public override Quaternion BaseRotation
        {
            get => m_boneTransform.rotation;
            set => m_boneTransform.rotation = value;
        }

        public override Quaternion EndRotation
        {
            get => m_boneEdge.rotation;
            set => m_boneEdge.rotation = value;
        }

        public Transform BoneTransform => m_boneTransform;
        public Transform BoneEdge => m_boneEdge;
    }
}
