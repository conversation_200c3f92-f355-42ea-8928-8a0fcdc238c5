using System;
using System.Collections;
using System.Collections.Generic;
using RibCageGames.Base;
using UnityEngine;

public abstract class ProceduralAnimationPointChain : MonoBehaviour
{
    [SerializeField] protected PointChainStrategy m_strategy;

    private MonoService m_monoService;
    
    protected Vector3 m_target;
    public Vector3 Target
    {
        get => m_target;
        set => m_target = value;
    }
    
    protected float m_maxLength;
    public float MaxLength
    {
        get => m_maxLength;
        set => m_maxLength = value;
    }

    protected virtual void Start()
    {
        m_monoService = ServiceLocator.Get<MonoService>();
        m_monoService.OnUpdate.AddListener(ControlledUpdate);
    }

    protected abstract void ControlledUpdate(float deltaTime);

    public abstract void RecalculateBones();
    
    public virtual void RecalculateBones<T> (List<T> bones) where T : Bone
    {
        m_maxLength = 0f;
            
        foreach (Bone t in bones)
        {
            t.m_length = (t.EndPosition - t.BasePosition).magnitude;
            m_maxLength += t.m_length;
        }

        float remainingLength = m_maxLength;
        for (int i = 0; i < bones.Count; i++)
        {
            Bone t = bones[i];

            if (i == bones.Count - 1)
            {
                t.m_leftoverRatio = 0f;
            }
            else if (i == bones.Count - 2)
            {
                t.m_leftoverRatio = 1f;
            }
            else
            {
                t.m_leftoverRatio = (t.Length / remainingLength);
                remainingLength -= t.Length;
            }
        }
    }

    public void UpdatePointPositions<T> (List<T> bones) where T : Bone
    {
        m_strategy.CalculateBones(bones, m_maxLength, m_target, transform.rotation);
    }
}
