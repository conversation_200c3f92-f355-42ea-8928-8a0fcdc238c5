using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(fileName = "PAIKStrategy", menuName = "RibCageGames/ProceduralAnimation/PAIKStrategy")]
public class PAIKStrategy : PointChainStrategy
{
    public override void CalculateBones<T>(List<T> bones, float maxLength, Vector3 target, Quaternion baseRotation)
    {
        float remainingLength = maxLength;
        Quaternion lastBoneRotation = baseRotation;
        for (int i = 0; i < bones.Count; i++)
        {
            Bone current = bones[i];
            remainingLength -= current.Length;
            
            Vector3 rotatedAxis = baseRotation * current.RotationAxis;
            current.DebugRotAxis = rotatedAxis;

            if (i == bones.Count - 1) //last bone
            {
                Vector3 finalTargetVector = target - current.BasePosition;
                finalTargetVector -= Vector3.Project(finalTargetVector, rotatedAxis);
                
                float finalTargetAngle = Vector3.SignedAngle(lastBoneRotation * Vector3.forward ,finalTargetVector, rotatedAxis);
                
                finalTargetAngle = Mathf.Clamp(finalTargetAngle, current.MinimalAngle, current.MaximalAngle);
                
                Quaternion lookRotation = lastBoneRotation * Quaternion.AngleAxis(finalTargetAngle, current.RotationAxis);
                
                current.BaseRotation = lookRotation;
                current.EndPosition = current.BasePosition
                                      + (lookRotation
                                         * (current.Length * Vector3.forward));
                break;
            }

            Vector3 targetAxis = target - current.BasePosition;
            float targetDistance = Mathf.Min(targetAxis.magnitude, remainingLength + current.Length);

            float leftoverLength = 0f;

                
            //leftoverLength = remainingLength - ((remainingLength - Mathf.Abs(current.Length + targetDistance)) *
            //                                    (remainingLength / (remainingLength + Mathf.Min(current.Length, targetDistance))) *
            //                                    (1f - current.LeftoverRatio));
            if (current.Length > targetDistance)
            {
                leftoverLength = remainingLength - ((remainingLength - current.Length + targetDistance) *
                                                    (remainingLength / (remainingLength + targetDistance)) *
                                                    (1f - current.LeftoverRatio));
            }
            else
            {
                leftoverLength = remainingLength - ((remainingLength + current.Length - targetDistance) *
                                                    (remainingLength / (remainingLength + current.Length)) *
                                                    (1f - current.LeftoverRatio));
            }
            
            leftoverLength = Mathf.Clamp(leftoverLength, Mathf.Abs(current.Length - targetDistance),
                current.Length + targetDistance);

            float cosA = ((leftoverLength * leftoverLength)
                          - (current.Length * current.Length)
                          - (targetDistance * targetDistance)) /
                         (-2f * current.Length * targetDistance);

            cosA = Mathf.Clamp01(cosA);

            float angle = Mathf.Rad2Deg * Mathf.Acos(cosA);
            
            Vector3 targetVector = target - current.BasePosition;
            targetVector -= Vector3.Project(targetVector, rotatedAxis);
                
            current.DebugProjectedTarget = targetVector;
            current.NegDebugRotation = lastBoneRotation;
            current.DebugNatVector = lastBoneRotation * Vector3.forward;

            float targetAngle = Vector3.SignedAngle(lastBoneRotation * Vector3.forward ,targetVector, rotatedAxis);
            current.NatDebugRotation = lastBoneRotation * Quaternion.AngleAxis(targetAngle, current.RotationAxis);
            float resultAngle = targetAngle + Mathf.Sign(current.NaturalAngle - targetAngle) * angle;
            current.PosDebugRotation = lastBoneRotation * Quaternion.AngleAxis(resultAngle, current.RotationAxis);
            resultAngle = Mathf.Clamp(resultAngle, current.MinimalAngle, current.MaximalAngle);
            Quaternion rot = lastBoneRotation * Quaternion.AngleAxis(resultAngle, current.RotationAxis);
            current.BaseRotation = rot;
            
            lastBoneRotation = rot;
            
            current.EndPosition =
                current.BasePosition
                + rot * Vector3.forward * current.Length;
        }
    }
}
