using System.Runtime.InteropServices.WindowsRuntime;
using RibCageGames.Base;

namespace RibCageGames.Animation
{
    using System;
    using UnityEngine;

    [Serializable]
    public class AnimatorParameterControlGroup
    {

    }

    [Serializable]
    public class AnimatorParameterControl : IConcreteCloneable<AnimatorParameterControl>
    {

        [SerializeField] private Animator m_animator;
        [SerializeField] private int m_parameterHash;
        [SerializeField] private AnimatorControllerParameterType m_parameterType;
        [SerializeField] private bool m_resetParameter = false;
        [SerializeField] private bool m_updateResetValue = false;//Not always have this true
        [SerializeField] private float m_floatValue;
        [SerializeField] private int m_intValue;
        [SerializeField] private bool m_boolValue;
        
        public bool ResetParameter => m_resetParameter;

        private Action<Animator> m_parameterTrigger = null;
        private Action<Animator> m_parameterReset = null;

        //Runtime initialization, assumes everything else is already set up
        public void Initialize(Animator animator)
        {
            m_animator = animator;
            switch (m_parameterType)
            {
                case AnimatorControllerParameterType.Float:
                    float initialFloatValue;
                    SetValue(m_floatValue, false);
                    initialFloatValue = m_animator.GetFloat(m_parameterHash);
                    m_parameterReset = anim => { anim?.SetFloat(m_parameterHash, initialFloatValue); };
                    break;
                case AnimatorControllerParameterType.Int:
                    int initialIntValue;
                    SetValue(m_intValue, false);
                    initialIntValue = m_animator.GetInteger(m_parameterHash);
                    m_parameterReset = anim => { anim?.SetInteger(m_parameterHash, initialIntValue); };
                    break;
                case AnimatorControllerParameterType.Bool:
                    bool initialBoolValue;
                    SetValue(m_boolValue, false);
                    initialBoolValue = m_animator.GetBool(m_parameterHash);
                    m_parameterReset = anim =>
                    {
                        anim?.SetBool(m_parameterHash, initialBoolValue);
                    };
                    break;
                case AnimatorControllerParameterType.Trigger:
                    m_parameterTrigger = anim =>
                    {
                        anim?.SetTrigger(m_parameterHash);
                    };

                    m_parameterReset = anim => { anim?.ResetTrigger(m_parameterHash); };
                    break;
            }
        }

        public void SetValue(bool value, bool invoke = true)
        {
            m_parameterTrigger = anim =>
            {
                if (m_updateResetValue)
                {
                    bool updatedInitialBoolValue = anim.GetBool(m_parameterHash);
                    m_parameterReset = resetAnim => { resetAnim?.SetBool(m_parameterHash, updatedInitialBoolValue); };
                }
                anim?.SetBool(m_parameterHash, value);
            };

            if (invoke)
            {
                ApplyParameter();
            }
        }
        
        public void SetValue(float value, bool invoke = true)
        {
            m_parameterTrigger = anim =>
            {
                if (m_updateResetValue)
                {
                    float updatedInitialFloatValue = anim.GetFloat(m_parameterHash);
                    m_parameterReset = resetAnim => { resetAnim?.SetFloat(m_parameterHash, updatedInitialFloatValue); };
                }

                anim?.SetFloat(m_parameterHash, value);
            };

            if (invoke)
            {
                ApplyParameter();
            }
        }
        
        public void SetValue(int value, bool invoke = true)
        {
            m_parameterTrigger = anim =>
            {
                if (m_updateResetValue)
                {
                    int updatedInitialIntValue = anim.GetInteger(m_parameterHash);
                    m_parameterReset = resetAnim => { resetAnim?.SetInteger(m_parameterHash, updatedInitialIntValue); };
                }
                        
                anim?.SetInteger(m_parameterHash, value);
            };

            if (invoke)
            {
                ApplyParameter();
            }
        }

        public void ApplyParameter(Animator overrideAnimator = null)
        {
            m_parameterTrigger.Invoke(overrideAnimator ?? m_animator);
        }

        public void ResetParameterControl(Animator overrideAnimator)
        {
            m_parameterReset.Invoke(overrideAnimator ?? m_animator);
        }

        public AnimatorParameterControl Copy()
        {
            AnimatorParameterControl copy = new AnimatorParameterControl();
                
            copy.m_animator = this.m_animator;
            copy.m_parameterHash = this.m_parameterHash;
            copy.m_parameterType = this.m_parameterType;
            copy.m_updateResetValue = this.m_updateResetValue ; 
            copy.m_floatValue = this.m_floatValue;
            copy.m_intValue = this.m_intValue;
            copy.m_boolValue = this.m_boolValue;
            copy.m_resetParameter = this.m_resetParameter;

            return copy;
        }

        public object Clone()
        {
            return Copy();
        }
    }
}