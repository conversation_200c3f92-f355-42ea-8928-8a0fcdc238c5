namespace RibCageGames.Animation
{
    using System.Collections;
    using System.Collections.Generic;
    using RibCageGames.Base;
    using UnityEngine;
    using UnityEngine.Events;
    using UnityEngine.Serialization;

    public class OnStartDelayedAnimationEvent : StateMachineBehaviour
    {

        [SerializeField] private AnimationEventType animationEventType = AnimationEventType.Ignored;
        [SerializeField] [Range(0f, 60f)] private float m_delay = 1f;

        public class OnAnimationDelayedStartHandler : UnityEvent<AnimationEventType>
        {
        }

        public OnAnimationDelayedStartHandler AnimationDelayedStart { get; } = new OnAnimationDelayedStartHandler();

        public override void OnStateEnter(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
        {
            base.OnStateExit(animator, stateInfo, layerIndex);
            if (animationEventType != AnimationEventType.Ignored)
            {
                MonoProcess.WaitForSecondsProcess(m_delay)
                    .Do(() => { AnimationDelayedStart.Invoke(animationEventType); });
            }
        }

        public enum AnimationEventType
        {
            Ignored,
            AttackFinished,
            TakeDamageFinished,
        }
    }
}