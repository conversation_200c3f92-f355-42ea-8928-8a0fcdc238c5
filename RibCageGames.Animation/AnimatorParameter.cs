namespace RibCageGames.Animation
{
    using System;
    using UnityEngine;

    public class AnimatorParameter
    {

        [SerializeField] private int m_parameterHash;

        public int Value => m_parameterHash;

        public string ParamterName
        {
            set { m_parameterHash = Animator.StringToHash(value); }
        }
    }

    [Serializable]
    public class TargetedAnimatorParameter : AnimatorParameter
    {
    }
}
