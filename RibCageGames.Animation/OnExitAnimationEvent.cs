namespace RibCageGames.Animation
{
    using System.Collections;
    using System.Collections.Generic;
    using UnityEngine;
    using UnityEngine.Events;

    public class OnExitAnimationEvent : StateMachineBehaviour
    {

        [SerializeField] private AnimationExitType m_animationExitType = AnimationExitType.Ignored;

        public class OnAnimationExitHandler : UnityEvent<AnimationExitType>
        {
        }

        public OnAnimationExitHandler OnAnimationExit { get; } = new OnAnimationExitHandler();

        public override void OnStateExit(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
        {
            base.OnStateExit(animator, stateInfo, layerIndex);
            if (m_animationExitType != AnimationExitType.Ignored)
            {
                OnAnimationExit.Invoke(m_animationExitType);
            }
        }

        public enum AnimationExitType
        {
            Ignored,
            AttackFinished,
            TakeDamageFinished,
        }
    }
}