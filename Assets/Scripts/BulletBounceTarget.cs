using System;
using RibCageGames.Base;
using RibCageGames.Combat;
using UnityEngine;

public class BulletBounceTarget : MonoBehaviour, DamageableRegistry.IDamageable
{
    [SerializeField] private BulletPool m_bulletPool;
    [SerializeField] private BoxCollider m_hurtCollider;
    [SerializeField] private Transform m_bounceTransform;
    
    [NonSerialized] protected DamageableRegistry m_damageableRegistry;
    
    private void Start()
    {
        m_damageableRegistry = ServiceLocator.Get<DamageableRegistry>();

        m_damageableRegistry.RegisterDamageable(new ColliderReference(m_hurtCollider), this);
    }

    public float TakeDamage(float damage, CombatEntity owner, ActionData.ActionHitBox hitData, out bool flinch)
    {
        flinch = false;
        return 0f;
    }

    public Transform ConnectedTransform { get; }
    public bool IsPlayer { get; }
    public Animator Animator { get; }

    public void BounceBullet(BulletController bullet, Vector3 hitPoint)
    {
        Vector3 newDirection = Vector3.Reflect(bullet.MovementDirection, m_bounceTransform.forward);
        bullet.transform.rotation = Quaternion.LookRotation(newDirection);   
        bullet.SetBulletDataPartial(newDirection, bullet.Speed, bullet.MaxDistance);
    }
}
