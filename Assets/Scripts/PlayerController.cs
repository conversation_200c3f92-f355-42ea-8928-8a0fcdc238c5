using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using RibCageGames.Animation;
using RibCageGames.Base;
using RibCageGames.Behaviour;
using RibCageGames.Combat;
using RibCageGames.Input;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Serialization;

public class PlayerController : CombatEntity
{
    [Header("References")] [SerializeField]
    private Camera m_mainCamera;

    [SerializeField] private Transform m_visualHolder;
    [SerializeField] private Transform m_bulletSpawn;
    [SerializeField] private ParticleSystem m_shellCaseParticleSystem;
    [SerializeField] private BulletPool m_bulletPool;

    [FormerlySerializedAs("m_legChain")] [SerializeField]
    private List<LineRendererPointChain> m_legChains;

    public override bool IsPlayer { get; }
    public Vector2 CurrentHorizontalSpeed => m_currentHorizontalSpeed;

    private Vector3 m_inputDirection;
    private Vector2 m_currentCameraRelativeMovement;
    private Vector2 m_currentHorizontalSpeed = Vector2.zero;
    [SerializeField] private float m_acceleration;
    [SerializeField] private float m_movementSpeed;
    [SerializeField] private float m_bulletSpeed;
    [SerializeField] private float m_bulletMaxDistance;
    [SerializeField] private float m_bulletKnockBackForce;
    [SerializeField] private float m_recoilKnockBackForce;
    [SerializeField] private float m_stepGroundOffset;
    [SerializeField] private AnimationCurve m_stepCurve;
    [SerializeField] private float m_stepOvershoot;
    [SerializeField] private float m_legHeightMaximum;
    
    [SerializeField] [AnimatorParameterControl("m_animator")]
    private AnimatorParameterControl m_idleAnimatorParameter;
    
    [SerializeField] [AnimatorParameterControl("m_animator")]
    private AnimatorParameterControl m_walkAnimatorParameter;
    
    [SerializeField] [AnimatorParameterControl("m_animator")]
    private AnimatorParameterControl m_shootAnimatorParameter;
    
    [SerializeField] [AnimatorParameterControl("m_animator")]
    private AnimatorParameterControl m_knockbackAnimatorParameter;
    
    [SerializeField] [AnimatorParameterControl("m_animator")]
    private AnimatorParameterControl m_stunAnimatorParameter;
    
    
    [SerializeField] private bool m_inputActive;

    private LegControllerGroup m_legGroup;
    
    private Vector2 CameraRelativeMovement(Vector3 movementDirection)
    {
        Vector3 cameraForward = m_mainCamera.transform.forward;
        Vector3 cameraRight = m_mainCamera.transform.right;

        cameraForward.y = 0f;
        cameraRight.y = 0f;

        Vector3 sumMovement = cameraForward * movementDirection.z
                              + cameraRight * movementDirection.x;

        return new Vector2(sumMovement.x, sumMovement.z).normalized;
    }

    protected override void Start()
    {
        base.Start();
        ActivateCombatEntity();
        m_legGroup = new LegControllerGroup(this, new List<LegController>
        {
            new(m_legChains[0], m_stepGroundOffset, m_stepCurve, m_legHeightMaximum),
            new(m_legChains[1], m_stepGroundOffset, m_stepCurve, m_legHeightMaximum),
        }, m_stepGroundOffset, m_stepOvershoot);

        m_idleAnimatorParameter.Initialize(m_animator);
        m_walkAnimatorParameter.Initialize(m_animator);
        m_shootAnimatorParameter.Initialize(m_animator);
        m_knockbackAnimatorParameter.Initialize(m_animator);
        m_stunAnimatorParameter.Initialize(m_animator);

        m_legGroup.StopRunCycle();
        
        m_idleAnimatorParameter.ApplyParameter();
    }

    public override void ActivateCombatEntity(bool resetHealth = true)
    {
        base.ActivateCombatEntity(resetHealth);
        m_monoService.OnUpdate.AddListener(CalculateMovement);

        m_damageableRegistry.RegisterDamageable(m_hurtBox, this);

        if (m_inputActive)
        {
            Dictionary<PlayerInputActions, InputEvent> inputActions = ServiceLocator.Get<InputService>().InGameInputs;

            foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
            {
                switch (action.Key)
                {
                    case PlayerInputActions.Movement:
                        ((InputVector2Event)action.Value).Performed.AddListener(OnMovement);
                        ((InputVector2Event)action.Value).Canceled.AddListener(OnMovement);
                        break;
                    case PlayerInputActions.LightAttack:
                        ((InputVoidEvent)action.Value).Performed.AddListener(Shoot);
                        break;
                }
            }
        }
        m_damageableRegistry.RegisterDamageable(m_hurtBox, this);
        //base.ActivatePoolable();
    }

    public override Transform GetCombatTarget(ActionData actionData)
    {
        return null;
    }

    public override void FaceDirection(Vector3 direction, float delta)
    {

    }

    private bool m_shooting = false;
    [SerializeField] private float m_shootingSpeed;
    [SerializeField] private float m_shootingDuration;

    private async void Shoot()
    {
        if (m_shooting)
        {
            return;
        }

        m_shellCaseParticleSystem.Emit(1);

        BulletController bullet = m_bulletPool.GetInstance();

        bullet.transform.position = m_bulletSpawn.position;
        bullet.transform.rotation = m_bulletSpawn.rotation;
        bullet.SetBulletData(m_hurtBox.Collider, m_bulletSpawn.forward, m_bulletSpeed, m_bulletMaxDistance, m_bulletKnockBackForce);
        bullet.ResetPoolable(true);
        bullet.ActivatePoolable();

        CompositeMovementStrategy.DeltaStrategy.OnCollisionDetected.AddListener(CollideDuringFlyback);
        
        m_shootAnimatorParameter.ApplyParameter();
        
        CancellationTokenSource flyBackToken = new CancellationTokenSource();
        m_shooting = true;

        m_legGroup.PlantLegs(flyBackToken.Token).Forget();
        
        float timeShooting = 0f;

        while (timeShooting < m_shootingDuration)
        {
            timeShooting += Time.deltaTime;
            ConcurrentMovement = -m_shootingSpeed * Animator.transform.forward;
            //await Task.Yield();
            await UniTask.NextFrame();
        }

        //await Task.Delay((int)(m_shootingDuration * 1000));
        m_shooting = false;
        //bullet.OnDisable.AddSingleUseListener(bulletDestroyed);

        CompositeMovementStrategy.DeltaStrategy.OnCollisionDetected.RemoveListener(CollideDuringFlyback);
        
        m_idleAnimatorParameter.ApplyParameter();
        
        await UniTask.WaitForSeconds(0.2f);
        
        flyBackToken.Cancel();
    }

    private void CollideDuringFlyback(Collider colliderHit)
    {
        if (m_damageableRegistry.TryGetValue(colliderHit, out DamageableRegistry.IDamageable damageable))
        {
            if (damageable is PlayerController player)
            {
                player.TakeDamage(0.3f, (player.transform.position - transform.position).normalized
                                        //Knockback force
                                        * m_recoilKnockBackForce);
            }
        }
    }

    public override float TakeDamage(float damage, CombatEntity owner, ActionData.ActionHitBox hitData, out bool flinch)
    {
        return base.TakeDamage(damage, owner, hitData, out flinch);
    }

    public async void TakeDamage(float flybackDuration, Vector3 direction)
    {
        CompositeMovementStrategy.DeltaStrategy.OnCollisionDetected.AddListener(CollideDuringDamageFlyback);
        CancellationTokenSource flyBackToken = new CancellationTokenSource();
        m_busy = true;
        float timeFlying = 0f;
        
        m_knockbackAnimatorParameter.ApplyParameter();
        
        m_legGroup.PlantLegs(flyBackToken.Token).Forget();
        
        while (timeFlying < flybackDuration)
        {
            timeFlying += Time.deltaTime;
            ConcurrentMovement = direction * Time.deltaTime;
            //await Task.Yield();
            await UniTask.NextFrame();
        }

        //await Task.Delay((int)(m_shootingDuration * 1000));
        //bullet.OnDisable.AddSingleUseListener(bulletDestroyed);

        await UniTask.WaitForSeconds(0.2f);
        flyBackToken.Cancel();
        m_busy = false;
        CompositeMovementStrategy.DeltaStrategy.OnCollisionDetected.RemoveListener(CollideDuringDamageFlyback);
        m_idleAnimatorParameter.ApplyParameter();
    }

    private async void CollideDuringDamageFlyback(Collider collider)
    {
        m_stunAnimatorParameter.ApplyParameter();
        
        await UniTask.WaitForSeconds(0.5f);
        
        m_idleAnimatorParameter.ApplyParameter();
    }

    //TODO: make leg anchor mechanic clearer
    private LineRendererPointChain m_lastAnchor;

    protected override void ControlledUpdate(float deltaTime)
    {
        base.ControlledUpdate(deltaTime);

        //LineRendererPointChain leg1 = m_legChains[0];
        //LineRendererPointChain leg2 = m_legChains[1];
//
        //if (leg1.Stable && !leg2.Stable && leg1 != m_lastAnchor)
        //{
        //    m_lastAnchor = leg1;
        //    leg1.Anchor = true;
        //    leg2.Anchor = false;
        //}
        //else if (leg2.Stable && !leg1.Stable && leg2 != m_lastAnchor)
        //{
        //    m_lastAnchor = leg2;
        //    leg2.Anchor = true;
        //    leg1.Anchor = false;
        //}
    }

    private void CalculateMovement(float deltaTime)
    {
        m_currentCameraRelativeMovement = CameraRelativeMovement(m_inputDirection);


        if (m_shooting)
        {
            m_currentHorizontalSpeed = Vector2.zero;
            ConcurrentMovement = new Vector3(m_currentHorizontalSpeed.x, 0f, m_currentHorizontalSpeed.y);
            return;
        }

        if (!m_busy)
        {
            m_currentHorizontalSpeed += m_currentCameraRelativeMovement * m_acceleration * deltaTime;
            m_currentHorizontalSpeed = Vector2.ClampMagnitude(m_currentHorizontalSpeed, m_movementSpeed);

            if (m_inputDirection == Vector3.zero && !m_shooting && !m_busy)
            {
                m_currentHorizontalSpeed = Vector2.zero;
            }

            Vector3 movement = new Vector3(m_currentHorizontalSpeed.x, 0f, m_currentHorizontalSpeed.y);
            ConcurrentMovement = movement;

            if (movement.sqrMagnitude > 0.1f)
            {
                m_visualHolder.forward = movement;

                if (!m_legGroup.Active)
                {
                    m_legGroup.StartRunCycle();
                }
                
                m_walkAnimatorParameter.ApplyParameter();
            }
            else
            {
                if (m_legGroup.Active)
                {
                    m_legGroup.StopRunCycle();
                }
                m_idleAnimatorParameter.ApplyParameter();
            }
        }
    }

    private void OnMovement(Vector2 value)
    {
        m_inputDirection = new Vector3(value.x, 0, value.y);
    }

    internal class LegControllerGroup
    {
        private List<LegController> m_legControllers;
        private PlayerController m_owner;
        private float m_stepGroundOffset;
        private float m_stepOvershoot;
        private int m_lastLegIndex;

        public bool Active { get; private set; } = false;

        private CancellationTokenSource m_runProcessSource;

        public LegControllerGroup(PlayerController owner, List<LegController> legs, float stepGroundOffset, float stepOvershoot)
        {
            m_legControllers = legs;
            m_owner = owner;
            m_stepGroundOffset = stepGroundOffset;
            m_stepOvershoot = stepOvershoot;
        }

        public async void StartRunCycle()
        {
            Active = true;
            m_runProcessSource?.Cancel();
            m_runProcessSource = new CancellationTokenSource();
            //foreach (LegController leg in m_legControllers)
            //{
            //    await SetStepCycle(leg, m_runProcessSource.Token);
            //await UniTask.WaitForSeconds(0.1f); //Offset between feet
            //}

            JointStepCycle(m_runProcessSource.Token).Forget();
        }

        private async UniTask JointStepCycle(CancellationToken token)
        {
            while (!token.IsCancellationRequested && Active)
            {
                int index = m_lastLegIndex % m_legControllers.Count + 1;
                for (int i = 0; i < m_legControllers.Count; i++)
                {
                    int selection = (i + index) % m_legControllers.Count;
                    LegController leg = m_legControllers[selection];
                    m_lastLegIndex = selection;
                    float directionMult = Mathf.Sign(Vector3.Dot(m_owner.ConcurrentMovement, m_owner.OwnForward));

                    Vector3 target = m_owner.transform.position + ((-m_stepGroundOffset) * Vector3.up)
                                                                + (directionMult * m_owner.OwnForward *
                                                                   m_owner.CurrentHorizontalSpeed.magnitude *
                                                                   m_stepOvershoot)
                                                                + leg.Offset;
                    
                    //Debug.LogError($"relative target {target - m_owner.transform.position} for {m_owner.gameObject.name} m_owner.CurrentHorizontalSpeed.magnitude is {m_owner.CurrentHorizontalSpeed.magnitude}");
                    
                    await leg.LerpTarget(target, 0.2f);
                    if (!Active)
                    {
                        break;
                    }
                }
            }
        }

        public async void StopRunCycle()
        {
            Active = false;
            //m_runProcessSource?.Cancel();

            await UniTask.NextFrame();

            m_runProcessSource?.Cancel();

            int index = m_lastLegIndex % m_legControllers.Count + 1;
            for (int i = 0; i < m_legControllers.Count; i++)
            {
                int selection = (i + index) % m_legControllers.Count;
                LegController leg = m_legControllers[selection];
                m_lastLegIndex = selection;
                if (leg.IsMoving)
                {
                    Vector3 target = m_owner.transform.position + ((-m_stepGroundOffset) * Vector3.up)
                                                                + (m_owner.OwnForward *
                                                                   m_owner.CurrentHorizontalSpeed.magnitude * 0.15f);

                    _ = leg.LerpTarget(target, 0.1f);
                }
            }
        }

        public async UniTask PlantLegs(CancellationToken token)
        {
            do
            {
                int index = m_lastLegIndex % m_legControllers.Count + 1;
                for (int i = 0; i < m_legControllers.Count; i++)
                {
                    int selection = (i + index) % m_legControllers.Count;
                    LegController leg = m_legControllers[selection];
                    m_lastLegIndex = selection;
                    //Vector3 target = leg.CurrentTarget;
                    //Vector3 calculatedTarget = m_owner.transform.position + ((-m_stepGroundOffset) * Vector3.up);
                    //target.y = calculatedTarget.y;

                    float directionMult = Mathf.Sign(Vector3.Dot(m_owner.ConcurrentMovement, m_owner.OwnForward));

                    Vector3 target = m_owner.transform.position + ((-m_stepGroundOffset) * Vector3.up)
                                                                + (directionMult * m_owner.OwnForward *
                                                                   m_owner.CurrentHorizontalSpeed.magnitude * 0.15f);

                    await leg.LerpTarget(target, 0.1f);
                }
            } while (!token.IsCancellationRequested);
        }
    }

    internal class LegController
    {
        private LineRendererPointChain m_legRenderer;
        private float m_verticalDistanceMultiplier;
        private float m_stepHeight;
        private float m_heightMaximum;
        private Vector3 m_offset;
        private AnimationCurve m_stepCurve;
        private CancellationTokenSource m_movementTokenSource;

        private float m_stepProcessValue;

        public Vector3 Offset => m_offset;
        public Vector3 CurrentTarget => m_legRenderer.Target;

        internal bool IsMoving => m_stepProcessValue < 1f;

        public LegController(LineRendererPointChain legRenderer, float stepHeight, AnimationCurve stepCurve, float heightMaximum)
        {
            m_legRenderer = legRenderer;
            //m_verticalDistanceMultiplier = verticalDistanceMultiplier;
            m_stepHeight = stepHeight;
            m_stepCurve = stepCurve;
            //m_offset = legRenderer.Target;
            m_heightMaximum = heightMaximum;
        }

        public async Task LerpTarget(Vector3 newTarget, float duration)
        {
            m_movementTokenSource?.Cancel();
            m_movementTokenSource = new CancellationTokenSource();
            CancellationToken token = m_movementTokenSource.Token;
            if ((newTarget - m_legRenderer.Target).magnitude > 0.5f || newTarget.y < 0f)
            {
                //Debug.LogError($"Set target to {newTarget} at {(newTarget - m_legRenderer.Target).magnitude}");
                //Debug.Break();
            }
            Vector3 originalTarget = m_legRenderer.Target;
            float activeTime = 0f;
            while (activeTime < duration && !token.IsCancellationRequested)
            {
                activeTime += Time.deltaTime;

                m_stepProcessValue = activeTime / duration;

                Vector3 calculatedTarget = m_legRenderer.transform.TransformVector(m_legRenderer.transform.localPosition)
                                           + Vector3.Lerp(originalTarget, newTarget, m_stepProcessValue);

                calculatedTarget.y = m_stepCurve.Evaluate(Mathf.Clamp01(m_stepProcessValue)) * m_stepHeight;
                //calculatedTarget.y = Mathf.Sin(Mathf.Clamp01(m_stepProcessValue) * Mathf.PI) * m_stepHeight;

                calculatedTarget.y = Mathf.Clamp(calculatedTarget.y, 0f, m_heightMaximum);
                
                m_legRenderer.Target = calculatedTarget;
                    //m_legRenderer.transform.TransformVector(m_legRenderer.transform.localPosition)
                    //+ Vector3.Lerp(originalTarget, newTarget, m_stepProcessValue)
                    //+ Vector3.up * Mathf.Sin(m_stepProcessValue * Mathf.PI) * m_stepHeight;
                //+ Vector3.up * stepDistance * m_verticalDistanceMultiplier * Mathf.Abs((activeTime / duration) - 0.5f);

                await Task.Yield();

                if (m_legRenderer.EndPoint.y > 0.4f)
                {
                    Debug.LogError($"m_legRenderer.Target {m_legRenderer.Target} for newTarget {newTarget}");
                    Debug.LogError($"A {m_legRenderer.transform.TransformVector(m_legRenderer.transform.localPosition)} B {Vector3.Lerp(originalTarget, newTarget, m_stepProcessValue)} C {Mathf.Sin(m_stepProcessValue * Mathf.PI) * m_stepHeight}");
                    //Debug.Break();
                }

                //if (cancellationToken.IsCancellationRequested)
                //{
                //    break;  
                //}  
            }

            m_legRenderer.Target = newTarget;

            //m_legRenderer.Target = newTarget;
        }
    }
}
