using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

public class HexTileGrid : MonoBehaviour
{
    [FormerlySerializedAs("tileSize")]
    [Header("Grid Settings")]
    [SerializeField] private float m_tileSize = 1f;
    [FormerlySerializedAs("tileSpacing")] [SerializeField] private float m_tileSpacing = 0.1f;
    [FormerlySerializedAs("pointyTop")] [SerializeField] private bool m_pointyTop = true; // true for pointy-top, false for flat-top

    [FormerlySerializedAs("tiles")]
    [Header("Tile Management")]
    [SerializeField] private List<HexTile> m_tiles = new();
    [FormerlySerializedAs("autoRegisterChildTiles")] [SerializeField] private bool m_autoRegisterChildTiles = true;

    [FormerlySerializedAs("showGridGizmos")]
    [Header("Debug")]
    [SerializeField] private bool m_showGridGizmos = true;
    [FormerlySerializedAs("gizmoColor")] [SerializeField] private Color m_gizmoColor = Color.cyan;
    [FormerlySerializedAs("gizmoGridRadius")] [SerializeField] private int m_gizmoGridRadius = 5;
    
    // Hexagonal math constants
    private static readonly float SQRT_3 = Mathf.Sqrt(3f);
    private static readonly float SQRT_3_OVER_2 = SQRT_3 / 2f;

    public float TileSize
    {
        get => m_tileSize;
        set
        {
            m_tileSize = Mathf.Max(0.01f, value);
            RefreshAllTilePositions();
        }
    }

    public float TileSpacing
    {
        get => m_tileSpacing;
        set
        {
            m_tileSpacing = Mathf.Max(0f, value);
            RefreshAllTilePositions();
        }
    }

    public bool PointyTop
    {
        get => m_pointyTop;
        set
        {
            m_pointyTop = value;
            RefreshAllTilePositions();
        }
    }

    public List<HexTile> MTiles => m_tiles;

    private void Awake()
    {
        if (m_autoRegisterChildTiles)
        {
            RegisterChildTiles();
        }
    }

    private void Start()
    {
        RefreshAllTilePositions();
    }

    private void OnValidate()
    {
        m_tileSize = Mathf.Max(0.01f, m_tileSize);
        m_tileSpacing = Mathf.Max(0f, m_tileSpacing);

        if (Application.isPlaying)
        {
            RefreshAllTilePositions();
        }
    }

    /// <summary>
    /// Converts hex cube coordinates to world position
    /// </summary>
    public Vector3 CoordinateToWorldPosition(HexCoordinate coordinate)
    {
        float effectiveSize = m_tileSize + m_tileSpacing;
        Vector3 position;

        if (!m_pointyTop)
        {
            // Pointy-top orientation
            float x = effectiveSize * (SQRT_3 * coordinate.Q + SQRT_3_OVER_2 * coordinate.R);
            float z = effectiveSize * (3f / 2f * coordinate.R);
            position = new Vector3(x, 0f, z);
        }
        else
        {
            // Flat-top orientation
            float x = effectiveSize * (3f / 2f * coordinate.Q);
            float z = effectiveSize * (SQRT_3_OVER_2 * coordinate.Q + SQRT_3 * coordinate.R);
            position = new Vector3(x, 0f, z);
        }

        return transform.TransformPoint(position);
    }

    /// <summary>
    /// Converts world position to nearest hex cube coordinates
    /// </summary>
    public HexCoordinate WorldPositionToCoordinate(Vector3 worldPosition)
    {
        Vector3 localPosition = transform.InverseTransformPoint(worldPosition);
        float effectiveSize = m_tileSize + m_tileSpacing;

        float q, r;

        if (m_pointyTop)
        {
            // Pointy-top orientation
            q = (SQRT_3 / 3f * localPosition.x - 1f / 3f * localPosition.z) / effectiveSize;
            r = (2f / 3f * localPosition.z) / effectiveSize;
        }
        else
        {
            // Flat-top orientation
            q = (2f / 3f * localPosition.x) / effectiveSize;
            r = (-1f / 3f * localPosition.x + SQRT_3 / 3f * localPosition.z) / effectiveSize;
        }

        return CubeRound(q, r);
    }

    /// <summary>
    /// Rounds fractional cube coordinates to the nearest integer cube coordinates
    /// </summary>
    private HexCoordinate CubeRound(float q, float r)
    {
        float s = -q - r;

        int rq = Mathf.RoundToInt(q);
        int rr = Mathf.RoundToInt(r);
        int rs = Mathf.RoundToInt(s);

        float q_diff = Mathf.Abs(rq - q);
        float r_diff = Mathf.Abs(rr - r);
        float s_diff = Mathf.Abs(rs - s);

        if (q_diff > r_diff && q_diff > s_diff)
        {
            rq = -rr - rs;
        }
        else if (r_diff > s_diff)
        {
            rr = -rq - rs;
        }

        return new HexCoordinate(rq, rr);
    }

    /// <summary>
    /// Registers a tile with this grid
    /// </summary>
    public void RegisterTile(HexTile tile)
    {
        if (tile != null && !m_tiles.Contains(tile))
        {
            m_tiles.Add(tile);
            tile.ParentGrid = this;
            if (tile.AutoSnapToGrid)
            {
                tile.Coordinate = WorldPositionToCoordinate(tile.transform.position);
                tile.SnapToGridPosition();
            }
        }
    }

    /// <summary>
    /// Unregisters a tile from this grid
    /// </summary>
    public void UnregisterTile(HexTile tile)
    {
        if (tile != null && m_tiles.Contains(tile))
        {
            m_tiles.Remove(tile);
            if (tile.ParentGrid == this)
            {
                tile.ParentGrid = null;
            }
        }
    }

    /// <summary>
    /// Automatically finds and registers all HexTile children
    /// </summary>
    public void RegisterChildTiles()
    {
        HexTile[] childTiles = GetComponentsInChildren<HexTile>();
        foreach (HexTile tile in childTiles)
        {
            RegisterTile(tile);
        }
    }

    /// <summary>
    /// Refreshes positions of all registered tiles
    /// </summary>
    public void RefreshAllTilePositions()
    {
        foreach (HexTile tile in m_tiles)
        {
            if (tile != null && tile.AutoSnapToGrid)
            {
                tile.SnapToGridPosition();
            }
        }
    }

    /// <summary>
    /// Gets the tile at the specified coordinate
    /// </summary>
    public HexTile GetTileAtCoordinate(HexCoordinate coordinate)
    {
        foreach (HexTile tile in m_tiles)
        {
            if (tile != null && tile.Coordinate == coordinate)
            {
                return tile;
            }
        }
        return null;
    }

    /// <summary>
    /// Checks if a coordinate is occupied by a tile
    /// </summary>
    public bool IsCoordinateOccupied(HexCoordinate coordinate)
    {
        return GetTileAtCoordinate(coordinate) != null;
    }

    /// <summary>
    /// Gets all neighbors of a coordinate within the specified distance
    /// </summary>
    public List<HexCoordinate> GetNeighbors(HexCoordinate center, int distance = 1)
    {
        List<HexCoordinate> neighbors = new List<HexCoordinate>();

        for (int q = -distance; q <= distance; q++)
        {
            int r1 = Mathf.Max(-distance, -q - distance);
            int r2 = Mathf.Min(distance, -q + distance);

            for (int r = r1; r <= r2; r++)
            {
                HexCoordinate coord = new HexCoordinate(center.Q + q, center.R + r);
                if (coord != center)
                {
                    neighbors.Add(coord);
                }
            }
        }

        return neighbors;
    }

    /// <summary>
    /// Calculates distance between two hex coordinates
    /// </summary>
    public static int Distance(HexCoordinate a, HexCoordinate b)
    {
        return (Mathf.Abs(a.Q - b.Q) + Mathf.Abs(a.Q + a.R - b.Q - b.R) + Mathf.Abs(a.R - b.R)) / 2;
    }

    private void OnDrawGizmos()
    {
        if (!m_showGridGizmos) return;

        Gizmos.color = m_gizmoColor;

        // Draw grid lines for visualization
        for (int q = -m_gizmoGridRadius; q <= m_gizmoGridRadius; q++)
        {
            for (int r = -m_gizmoGridRadius; r <= m_gizmoGridRadius; r++)
            {
                if (Mathf.Abs(q + r) <= m_gizmoGridRadius)
                {
                    HexCoordinate coord = new HexCoordinate(q, r);
                    Vector3 worldPos = CoordinateToWorldPosition(coord);

                    // Draw a small circle at each grid position
                    Gizmos.DrawWireSphere(worldPos, m_tileSize * 0.1f);

                    // Draw hex outline
                    DrawHexGizmo(worldPos, m_tileSize * 0.9f);
                }
            }
        }
    }

    private void DrawHexGizmo(Vector3 center, float size)
    {
        Vector3[] vertices = new Vector3[6];

        for (int i = 0; i < 6; i++)
        {
            float angle = (m_pointyTop ? 0f : 30f) + 60f * i;
            float rad = angle * Mathf.Deg2Rad;
            vertices[i] = center + new Vector3(
                size * Mathf.Cos(rad),
                0f,
                size * Mathf.Sin(rad)
            );
        }

        // Draw hex outline
        for (int i = 0; i < 6; i++)
        {
            Gizmos.DrawLine(vertices[i], vertices[(i + 1) % 6]);
        }
    }
}
