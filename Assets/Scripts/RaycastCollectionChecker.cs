using System;
using System.Collections.Generic;
using RibCageGames.Base;
using Unity.Collections;
using Unity.Jobs;
using UnityEngine;
using UnityEngine.Jobs;
using Random = UnityEngine.Random;

public class RayCastCollectionChecker : MonoBehaviour{
    #region Settings and Refs
    [SerializeField] private LayerMask m_collisionMask;
    [SerializeField] private ParticleSystem m_splatterParticleSystem;
    #endregion

    static readonly List<BulletController> m_activeProjectiles = new();

    private void Start()
    {
        ServiceLocator.Get<MonoService>().OnUpdate.AddListener(ControlledUpdate);
    }

    public static void AddBulletToActiveBullets(BulletController bullet)
    {
        m_activeProjectiles.Add(bullet);
        bullet.OnDisable.AddSingleUseListener(() =>
        {
            Debug.Log($"Remove via listener");    
            m_activeProjectiles.Remove(bullet);
        });
        Debug.Log($"Adding bullet count now: {m_activeProjectiles.Count}");
    }

    private void ControlledUpdate(float delta)
    {
        int subSteps = 5;
        float subStepTime = delta / subSteps;

        // Consider caching the TransformAccessArray if possible
        using TransformAccessArray bulletTransforms = new TransformAccessArray(m_activeProjectiles.Count);
        using NativeArray<Vector3> directions = new NativeArray<Vector3>(m_activeProjectiles.Count, Allocator.TempJob);
        using NativeArray<float> speeds = new NativeArray<float>(m_activeProjectiles.Count, Allocator.TempJob);
        for (int i = 0; i < m_activeProjectiles.Count; i++)
        {
            BulletController bullet = m_activeProjectiles[i];
            bulletTransforms.Add(bullet.transform);
            NativeArray<Vector3> nativeArray = directions;
            nativeArray[i] = bullet.MovementDirection;
            NativeArray<float> floats = speeds;
            floats[i] = bullet.Speed;
        }

        for (int step = 0; step < subSteps; step++)
        {
            var job = new BulletMoveJob
            {
                deltaTime = subStepTime,
                speeds = speeds,
                directions = directions,
            };

            JobHandle jobHandle = job.Schedule(bulletTransforms);
            jobHandle.Complete();

            HandleCollisions(subStepTime);
        }

        for (int i = m_activeProjectiles.Count - 1; i >= 0; i--)
        {
            //Remove bullets from collection
            if (m_activeProjectiles[i].HasTraveledMaxDistance())
            {
                m_activeProjectiles[i].OnDisable.Invoke();
            }
        }
    }

    void HandleCollisions(float delta) {
        Vector3[] origins = new Vector3[10];
        Vector3[] directions = new Vector3[10];
        float[] distances = new float[10];
        for (int i = 0; i < m_activeProjectiles.Count; i++) {
            BulletController bullet = m_activeProjectiles[i];
            origins[i] = bullet.transform.position;
            directions[i] = bullet.MovementDirection;
            distances[i] = bullet.Speed * delta;
        }
        
        RaycastBatchProcessor.PerformRaycasts(origins,
            directions,
            distances,
            m_collisionMask.value,
            false,
            false,
            false,
            OnRaycastResults);
    }

    private void OnRaycastResults(RaycastHit[] hits) {
        
        for (int i = hits.Length; i-- > 0;) {
            if (hits[i].collider != null) {
                
                //TODO: thing when bullet hits a thing
                if (m_activeProjectiles[i].OnHit(hits[i].collider, hits[i].point))
                {
                    m_splatterParticleSystem.transform.position = hits[i].point + hits[i].normal * 0.01f;
                    
                    ParticleSystem.MainModule mainModule = m_splatterParticleSystem.main;
                    Vector3 eulerRotation = Quaternion.LookRotation(-hits[i].normal).eulerAngles;

                    mainModule.startRotationX = new ParticleSystem.MinMaxCurve(eulerRotation.x * Mathf.Deg2Rad);
                    mainModule.startRotationY = new ParticleSystem.MinMaxCurve(- eulerRotation.y * Mathf.Deg2Rad);
                    mainModule.startRotationZ = new ParticleSystem.MinMaxCurve(Random.Range(0f, 360f) * Mathf.Deg2Rad);
                    
                    m_splatterParticleSystem.Emit(1);
                    m_activeProjectiles[i].OnDisable.Invoke();
                }
            }
        }
    }

    //[BurstCompile] TODO: figure out why burst doesn't compile
    struct BulletMoveJob : IJobParallelForTransform {
        public float deltaTime;
        public NativeArray<float> speeds;
        public NativeArray<Vector3> directions;

        public void Execute(int index, TransformAccess transform) {
            transform.position += directions[index] * speeds[index] * deltaTime;
        }
    }
}