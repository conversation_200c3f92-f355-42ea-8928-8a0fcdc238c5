using UnityEngine;
using UnityEngine.Serialization;

[System.Serializable]
public struct HexCoordinate
{
    [SerializeField] private int q;
    [SerializeField] private int r;

    public int Q => q;
    public int R => r;
    public int S => -q - r; // Cube coordinate constraint: q + r + s = 0

    public HexCoordinate(int q, int r)
    {
        this.q = q;
        this.r = r;
    }

    public static HexCoordinate Zero => new HexCoordinate(0, 0);

    public bool IsValid => Q + R + S == 0;

    public override string ToString()
    {
        return $"({Q}, {R}, {S})";
    }

    public override bool Equals(object obj)
    {
        return obj is HexCoordinate other && Q == other.Q && R == other.R;
    }

    public override int GetHashCode()
    {
        return (Q, R).GetHashCode();
    }

    public static bool operator ==(HexCoordinate a, HexCoordinate b)
    {
        return a.Q == b.Q && a.R == b.R;
    }

    public static bool operator !=(HexCoordinate a, HexCoordinate b)
    {
        return !(a == b);
    }
}

public class HexTile : MonoBehaviour
{
    [FormerlySerializedAs("coordinate")] [SerializeField] private HexCoordinate m_coordinate = HexCoordinate.Zero;
    [FormerlySerializedAs("autoSnapToGrid")] [SerializeField] private bool m_autoSnapToGrid = true;
    [SerializeField] private Vector3 m_graphicSizeScale = Vector3.one;

    private HexTileGrid parentGrid;
    private HexCoordinate lastValidCoordinate;

    public HexCoordinate Coordinate
    {
        get => m_coordinate;
        set
        {
            if (m_coordinate != value)
            {
                m_coordinate = value;
                ValidateCoordinate();
                if (m_autoSnapToGrid && parentGrid != null)
                {
                    SnapToGridPosition();
                }
            }
        }
    }

    public HexTileGrid ParentGrid
    {
        get => parentGrid;
        set => parentGrid = value;
    }

    public bool AutoSnapToGrid
    {
        get => m_autoSnapToGrid;
        set => m_autoSnapToGrid = value;
    }

    private void Awake()
    {
        lastValidCoordinate = m_coordinate;
        ValidateCoordinate();
    }

    private void Start()
    {
        // Try to find parent grid if not set
        if (parentGrid == null)
        {
            parentGrid = GetComponentInParent<HexTileGrid>();
        }

        if (m_autoSnapToGrid && parentGrid != null)
        {
            SnapToGridPosition();
        }
    }

    private void OnValidate()
    {
        ValidateCoordinate();
        if (m_autoSnapToGrid && parentGrid != null && Application.isPlaying)
        {
            SnapToGridPosition();
        }
    }

    private void ValidateCoordinate()
    {
        if (!m_coordinate.IsValid)
        {
            // Restore last valid coordinate if current is invalid
            m_coordinate = lastValidCoordinate;
        }
        else
        {
            lastValidCoordinate = m_coordinate;
        }
    }

    public void SetTileSize(Vector3 size)
    {
        m_graphicSizeScale = size;
    }

    public void SnapToGridPosition()
    {
        if (parentGrid != null)
        {
            transform.position = parentGrid.CoordinateToWorldPosition(m_coordinate);
            transform.localScale = m_graphicSizeScale * parentGrid.TileSize;
        }
    }

    public void SetCoordinateWithoutSnap(HexCoordinate newCoordinate)
    {
        m_coordinate = newCoordinate;
        ValidateCoordinate();
    }
}
