using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.MonoUtils;
using UnityEngine;
using UnityEngine.Events;

public class BulletController : MonoBehaviour, IPoolable
{
    private DamageableRegistry m_damageableRegistry;
    private Collider m_ownerCollider;
    private Vector3 m_movementDirection;
    private float m_maxDistance;
    private float m_speed;
    private bool m_active;
    private float m_distanceTraversed;
    private float m_knockbackForce;
    private Vector3 m_initialPosition;
    
    public Vector3 MovementDirection => m_movementDirection;
    public float MaxDistance => m_maxDistance;
    public float Speed => m_speed;
    public float DistanceTraversed => m_distanceTraversed;
    public bool TraversedMaxDistance => m_distanceTraversed > m_maxDistance;
    
    public bool HasTraveledMaxDistance() {
        float distanceTraveledSq = (m_initialPosition - transform.position).sqrMagnitude;
        return m_active && distanceTraveledSq >= m_maxDistance * m_maxDistance;
    }
    
    public void SetBulletData(Collider ownerCollider, Vector3 flightDirection, float speed, float maxDistance, float knockbackForce)
    {
        m_initialPosition = transform.position;
        m_ownerCollider = ownerCollider;
        m_movementDirection = flightDirection.normalized;
        m_speed = speed;
        m_maxDistance = maxDistance;
        m_active = true;
        m_knockbackForce = knockbackForce;
        gameObject.SetActive(true);
    }
    
    public void SetBulletDataPartial(Vector3 flightDirection, float speed, float maxDistance)
    {
        m_initialPosition = transform.position;
        m_movementDirection = flightDirection.normalized;
        m_speed = speed;
        m_maxDistance = maxDistance;
        m_active = true;
        gameObject.SetActive(true);
    }

    public bool OnHit(Collider colliderHit, Vector3 hitPoint)
    {
        if (m_damageableRegistry.TryGetValue(colliderHit, out DamageableRegistry.IDamageable damageable))
        {
            if (damageable is PlayerController player)
            {
                player.TakeDamage(0.3f, (player.transform.position - hitPoint).normalized * m_knockbackForce);
                return true;
            }
            else if (damageable is BulletBounceTarget bounceTarget)
            {
                bounceTarget.BounceBullet(this, hitPoint);
                return false;
            }
        }

        return true;//(?)
    }
    
    public void ActivatePoolable()
    {
        //m_monoService.OnUpdate.AddListenerUntil(MoveBullet, (_) => !m_active);
        RayCastCollectionChecker.AddBulletToActiveBullets(this);
    }

    public void DeactivatePoolable()
    {
        m_active = false;
        gameObject.SetActive(false);
    }

    public void ResetPoolable(bool resetMovement = true)
    {
    }

    public void Initialize()
    {
        ServiceLocator.Get<MonoService>();
        m_damageableRegistry = ServiceLocator.Get<DamageableRegistry>();
    }

    public UnityEvent OnDisable { get; } = new();
}
