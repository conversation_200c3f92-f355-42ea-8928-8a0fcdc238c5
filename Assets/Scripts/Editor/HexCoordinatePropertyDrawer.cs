using UnityEngine;
using UnityEditor;

[CustomPropertyDrawer(typeof(HexCoordinate))]
public class HexCoordinatePropertyDrawer : PropertyDrawer
{
    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        EditorGUI.BeginProperty(position, label, property);
        
        // Get the q and r properties
        SerializedProperty qProp = property.FindPropertyRelative("q");
        SerializedProperty rProp = property.FindPropertyRelative("r");
        
        // Calculate positions
        Rect labelRect = new Rect(position.x, position.y, EditorGUIUtility.labelWidth, position.height);
        Rect qRect = new Rect(position.x + EditorGUIUtility.labelWidth, position.y, 50, position.height);
        Rect rRect = new Rect(qRect.x + 55, position.y, 50, position.height);
        Rect sRect = new Rect(rRect.x + 55, position.y, 50, position.height);
        Rect infoRect = new Rect(sRect.x + 55, position.y, position.width - (sRect.x + 55 - position.x), position.height);
        
        // Draw label
        EditorGUI.LabelField(labelRect, label);
        
        // Store original values
        int originalQ = qProp.intValue;
        int originalR = rProp.intValue;
        int calculatedS = -originalQ - originalR;
        
        // Draw Q field
        EditorGUI.BeginChangeCheck();
        int newQ = EditorGUI.IntField(qRect, originalQ);
        bool qChanged = EditorGUI.EndChangeCheck();
        
        // Draw R field
        EditorGUI.BeginChangeCheck();
        int newR = EditorGUI.IntField(rRect, originalR);
        bool rChanged = EditorGUI.EndChangeCheck();
        
        // Calculate and display S (read-only)
        int newS = -newQ - newR;
        EditorGUI.BeginDisabledGroup(true);
        EditorGUI.IntField(sRect, newS);
        EditorGUI.EndDisabledGroup();
        
        // Update properties if changed
        if (qChanged || rChanged)
        {
            qProp.intValue = newQ;
            rProp.intValue = newR;
        }
        
        // Show coordinate info
        string coordText = $"({newQ}, {newR}, {newS})";
        bool isValid = (newQ + newR + newS) == 0;
        
        if (!isValid)
        {
            GUI.color = Color.red;
            coordText += " INVALID";
        }
        else
        {
            GUI.color = Color.green;
        }
        
        EditorGUI.LabelField(infoRect, coordText, EditorStyles.miniLabel);
        GUI.color = Color.white;
        
        EditorGUI.EndProperty();
    }
    
    public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
    {
        return EditorGUIUtility.singleLineHeight;
    }
}
