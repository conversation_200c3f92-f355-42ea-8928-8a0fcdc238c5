using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[CustomEditor(typeof(HexTileGrid))]
public class HexTileGridEditor : Editor
{
    private HexTileGrid grid;
    private bool showTileList = true;
    private bool showUtilities = true;
    
    private void OnEnable()
    {
        grid = (HexTileGrid)target;
    }
    
    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        
        // Draw default inspector
        DrawDefaultInspector();
        
        EditorGUILayout.Space();
        
        // Tile Management Section
        showTileList = EditorGUILayout.Foldout(showTileList, "Tile Management", true);
        if (showTileList)
        {
            EditorGUI.indentLevel++;
            
            EditorGUILayout.LabelField($"Registered Tiles: {grid.MTiles.Count}", EditorStyles.boldLabel);
            
            if (grid.MTiles.Count > 0)
            {
                EditorGUILayout.BeginVertical("box");
                for (int i = 0; i < grid.MTiles.Count; i++)
                {
                    HexTile tile = grid.MTiles[i];
                    if (tile != null)
                    {
                        EditorGUILayout.BeginHorizontal();
                        
                        // Tile reference
                        EditorGUI.BeginDisabledGroup(true);
                        EditorGUILayout.ObjectField(tile, typeof(HexTile), true);
                        EditorGUI.EndDisabledGroup();
                        
                        // Coordinate display
                        EditorGUILayout.LabelField($"{tile.Coordinate}", GUILayout.Width(80));
                        
                        // Remove button
                        if (GUILayout.Button("Remove", GUILayout.Width(60)))
                        {
                            grid.UnregisterTile(tile);
                            break;
                        }
                        
                        EditorGUILayout.EndHorizontal();
                    }
                }
                EditorGUILayout.EndVertical();
            }
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Refresh Child Tiles"))
            {
                grid.RegisterChildTiles();
                EditorUtility.SetDirty(grid);
            }
            
            if (GUILayout.Button("Refresh All Positions"))
            {
                grid.RefreshAllTilePositions();
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUI.indentLevel--;
        }
        
        EditorGUILayout.Space();
        
        // Utilities Section
        showUtilities = EditorGUILayout.Foldout(showUtilities, "Utilities", true);
        if (showUtilities)
        {
            EditorGUI.indentLevel++;
            
            EditorGUILayout.LabelField("Coordinate Converter", EditorStyles.boldLabel);
            
            // World to Coordinate converter
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("World Position:", GUILayout.Width(100));
            Vector3 worldPos = EditorGUILayout.Vector3Field("", Vector3.zero);
            if (GUILayout.Button("Convert", GUILayout.Width(60)))
            {
                HexCoordinate coord = grid.WorldPositionToCoordinate(worldPos);
                Debug.Log($"World position {worldPos} converts to hex coordinate {coord}");
            }
            EditorGUILayout.EndHorizontal();
            
            // Coordinate to World converter
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Hex Coordinate:", GUILayout.Width(100));
            int q = EditorGUILayout.IntField(0, GUILayout.Width(40));
            int r = EditorGUILayout.IntField(0, GUILayout.Width(40));
            if (GUILayout.Button("Convert", GUILayout.Width(60)))
            {
                HexCoordinate coord = new HexCoordinate(q, r);
                Vector3 worldPosition = grid.CoordinateToWorldPosition(coord);
                Debug.Log($"Hex coordinate {coord} converts to world position {worldPosition}");
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // Grid Statistics
            EditorGUILayout.LabelField("Grid Statistics", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Total Tiles: {grid.MTiles.Count}");
            EditorGUILayout.LabelField($"Tile Size: {grid.TileSize:F2}");
            EditorGUILayout.LabelField($"Tile Spacing: {grid.TileSpacing:F2}");
            EditorGUILayout.LabelField($"Orientation: {(grid.PointyTop ? "Pointy Top" : "Flat Top")}");
            
            // Check for coordinate conflicts
            Dictionary<HexCoordinate, List<HexTile>> coordinateMap = new Dictionary<HexCoordinate, List<HexTile>>();
            foreach (HexTile tile in grid.MTiles)
            {
                if (tile != null)
                {
                    if (!coordinateMap.ContainsKey(tile.Coordinate))
                    {
                        coordinateMap[tile.Coordinate] = new List<HexTile>();
                    }
                    coordinateMap[tile.Coordinate].Add(tile);
                }
            }
            
            int conflicts = 0;
            foreach (var kvp in coordinateMap)
            {
                if (kvp.Value.Count > 1)
                {
                    conflicts++;
                }
            }
            
            if (conflicts > 0)
            {
                EditorGUILayout.HelpBox($"Warning: {conflicts} coordinate conflicts detected!", MessageType.Warning);
            }
            else
            {
                EditorGUILayout.HelpBox("No coordinate conflicts detected.", MessageType.Info);
            }
            
            EditorGUI.indentLevel--;
        }
        
        serializedObject.ApplyModifiedProperties();
    }
    
    private void OnSceneGUI()
    {
        if (grid == null) return;
        
        // Draw coordinate labels in scene view
        foreach (HexTile tile in grid.MTiles)
        {
            if (tile != null)
            {
                Vector3 worldPos = tile.transform.position;
                Handles.Label(worldPos + Vector3.up * 0.5f, tile.Coordinate.ToString());
            }
        }
    }
}
