using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(HexTile))]
public class HexTileEditor : Editor
{
    private HexTile tile;
    private bool showUtilities = true;
    
    private void OnEnable()
    {
        tile = (HexTile)target;
    }
    
    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        
        // Draw default inspector
        DrawDefaultInspector();
        
        EditorGUILayout.Space();
        
        // Utilities Section
        showUtilities = EditorGUILayout.Foldout(showUtilities, "Utilities", true);
        if (showUtilities)
        {
            EditorGUI.indentLevel++;
            
            // Current coordinate info
            EditorGUILayout.LabelField("Current Coordinate Info", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Coordinate: {tile.Coordinate}");
            EditorGUILayout.LabelField($"Is Valid: {tile.Coordinate.IsValid}");
            
            if (tile.ParentGrid != null)
            {
                Vector3 worldPos = tile.ParentGrid.CoordinateToWorldPosition(tile.Coordinate);
                EditorGUILayout.LabelField($"Grid World Position: {worldPos}");
                EditorGUILayout.LabelField($"Current World Position: {tile.transform.position}");
                
                float distance = Vector3.Distance(worldPos, tile.transform.position);
                EditorGUILayout.LabelField($"Distance from Grid Position: {distance:F3}");
                
                if (distance > 0.01f)
                {
                    EditorGUILayout.HelpBox("Tile is not aligned with grid position!", MessageType.Warning);
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No parent grid assigned.", MessageType.Info);
            }
            
            EditorGUILayout.Space();
            
            // Action buttons
            EditorGUILayout.LabelField("Actions", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Snap to Grid"))
            {
                if (tile.ParentGrid != null)
                {
                    Undo.RecordObject(tile.transform, "Snap to Grid");
                    tile.SnapToGridPosition();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "No parent grid assigned to this tile.", "OK");
                }
            }
            
            if (GUILayout.Button("Find Parent Grid"))
            {
                HexTileGrid parentGrid = tile.GetComponentInParent<HexTileGrid>();
                if (parentGrid != null)
                {
                    Undo.RecordObject(tile, "Assign Parent Grid");
                    tile.ParentGrid = parentGrid;
                    parentGrid.RegisterTile(tile);
                    EditorUtility.SetDirty(tile);
                }
                else
                {
                    EditorUtility.DisplayDialog("Info", "No HexTileGrid found in parent objects.", "OK");
                }
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Set Coordinate from Position"))
            {
                if (tile.ParentGrid != null)
                {
                    Undo.RecordObject(tile, "Set Coordinate from Position");
                    HexCoordinate newCoord = tile.ParentGrid.WorldPositionToCoordinate(tile.transform.position);
                    tile.Coordinate = newCoord;
                    EditorUtility.SetDirty(tile);
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "No parent grid assigned to this tile.", "OK");
                }
            }
            
            if (GUILayout.Button("Reset to Origin"))
            {
                Undo.RecordObject(tile, "Reset to Origin");
                tile.Coordinate = HexCoordinate.Zero;
                EditorUtility.SetDirty(tile);
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // Neighbor information
            if (tile.ParentGrid != null)
            {
                EditorGUILayout.LabelField("Neighbors", EditorStyles.boldLabel);
                var neighbors = tile.ParentGrid.GetNeighbors(tile.Coordinate, 1);
                
                EditorGUILayout.LabelField($"Adjacent Coordinates ({neighbors.Count}):");
                EditorGUI.indentLevel++;
                foreach (var neighbor in neighbors)
                {
                    HexTile neighborTile = tile.ParentGrid.GetTileAtCoordinate(neighbor);
                    string status = neighborTile != null ? $"Occupied by {neighborTile.name}" : "Empty";
                    EditorGUILayout.LabelField($"{neighbor}: {status}");
                }
                EditorGUI.indentLevel--;
            }
            
            EditorGUI.indentLevel--;
        }
        
        serializedObject.ApplyModifiedProperties();
    }
    
    private void OnSceneGUI()
    {
        if (tile == null || tile.ParentGrid == null) return;
        
        // Draw coordinate label
        Vector3 worldPos = tile.transform.position;
        Handles.Label(worldPos + Vector3.up * 0.5f, tile.Coordinate.ToString());
        
        // Draw line to grid position if not aligned
        Vector3 gridPos = tile.ParentGrid.CoordinateToWorldPosition(tile.Coordinate);
        float distance = Vector3.Distance(worldPos, gridPos);
        
        if (distance > 0.01f)
        {
            Handles.color = Color.red;
            Handles.DrawDottedLine(worldPos, gridPos, 5f);
            Handles.color = Color.green;
            Handles.DrawWireCube(gridPos, Vector3.one * 0.1f);
        }
    }
}
