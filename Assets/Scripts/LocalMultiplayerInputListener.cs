using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.Input;
using UnityEngine;
using UnityEngine.InputSystem;

public class LocalMultiplayerInputListener : InputListener
{
    [SerializeField] private int m_maxPlayers;
    
    private ServiceReference<InputService> m_inputService = new();
    private ServiceReference<MonoService> m_monoService = new();

    private bool m_keyboardRegistered = false;
    private List<Gamepad> m_registeredGamepads = new();

    protected override async void Start()
    {
        await UniTask.WaitForSeconds(1f);
        
        PlayerInput input = PlayerInput.Instantiate(m_inputListenerPrefab,
            controlScheme: "KeyboardAndMouse",
            pairWithDevices: new InputDevice[] { Keyboard.current, Mouse.current });
        input.gameObject.transform.SetParent(gameObject.transform);

        Debug.LogError($"Registering main input with {input.currentControlScheme}");
        m_inputService.Value.RegisterMainInput(input).Forget();
        m_monoService.Value.OnUpdate.AddListener((_) => Debug.Log($"scheme: {input.currentControlScheme}"));
        m_monoService.Value.OnUpdate.AddListenerUntil(ControlledUpdate, (_) => m_registeredGamepads.Count >= m_maxPlayers);
    }

    protected override void ControlledUpdate(float arg0)
    {
        foreach (Gamepad gamepad in Gamepad.all)
        {
            if (m_registeredGamepads.Contains(gamepad))
            {
                continue;
            }
            
            if (gamepad.buttonSouth.wasPressedThisFrame)
            {
                PlayerInput input = PlayerInput.Instantiate(m_inputListenerPrefab,
                controlScheme: "Controller",
                pairWithDevice: gamepad);
                
                input.gameObject.transform.SetParent(gameObject.transform);
                
                m_registeredGamepads.Add(gamepad);
                m_inputService.Value.RegisterMultiplayerInput(input, InputRegisteredCallback).Forget();
            }
        }
    }

    private void InputRegisteredCallback(Dictionary<PlayerInputActions, InputEvent> obj)
    {
        
    }
}
