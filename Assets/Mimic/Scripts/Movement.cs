using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace MimicSpace
{
    /// <summary>
    /// This is a very basic movement script, if you want to replace it
    /// Just don't forget to update the Mimic's velocity vector with a Vector3(x, 0, z)
    /// </summary>
    public class Movement : MonoBehaviour
    {
        [SerializeField] private Vector3 target;
        [SerializeField] private bool move;
        [SerializeField] private float moveSpeed;
        [SerializeField] private float timeToTarget;
        [SerializeField] private AnimationCurve curve;
        private float time;
        private Vector3 originalPosition;
        private bool moving;


        private void Update()
        {
            if (move)
            {
                //Linear, jittery
                //transform.position += moveSpeed * Time.deltaTime * (target - transform.position).normalized;
                
                //Linear, no jitter
                /*
                Vector3 direction = (target - transform.position);
                Vector3 movement =
                    moveSpeed *
                    Time.deltaTime *
                    direction
                    .normalized;
                if (direction.magnitude < 1f)
                {
                    movement *= direction.magnitude;
                }
                transform.position += movement;
                */
                
                //Linear, stopping
                /*
                Vector3 direction = (target - transform.position);
                float magnitude = direction.magnitude;
                Vector3 movement =
                    moveSpeed *
                    Time.deltaTime *
                    direction
                        .normalized;
                if (magnitude < 1f)
                {
                    movement *= magnitude;
                }
                transform.position += movement;

                if (magnitude < 0.001f)
                {
                    transform.position = target;
                    move = false;
                }
                */
                
                //Quadratic
                /*
                Vector3 direction = (target - transform.position);
                float magnitude = direction.magnitude;
                transform.position =
                    Vector3.Lerp(transform.position,
                        target,
                        Time.deltaTime * speed);
                        
                        
                if (magnitude < 0.001f)
                {
                    transform.position = target;
                    move = false;
                }
                */
                
                //Animation curve

                if (!moving)
                {
                    moving = true;
                    originalPosition = transform.position;
                }

                if (moving)
                {
                    time += Time.deltaTime;
                    float t = curve.Evaluate(time / timeToTarget);
                    transform.position =
                        Vector3.LerpUnclamped(originalPosition,
                            target,
                            t);
                    
                    if (time / timeToTarget > 0.99f)
                    {
                        transform.position = target;
                        move = false;
                        moving = false;
                        time = 0f;
                    }
                }
            }
        }






        [Header("Controls")]
        [Tooltip("Body Height from ground")]
        [Range(0.5f, 5f)]
        public float height = 0.8f;
        public float speed = 5f;
        Vector3 velocity = Vector3.zero;
        public float velocityLerpCoef = 4f;
        Mimic myMimic;

        private void Start()
        {
            myMimic = GetComponent<Mimic>();
        }

        private void NotUpdate()
        {






            return;
            velocity = Vector3.Lerp(velocity, new Vector3(Input.GetAxisRaw("Horizontal"), 0, Input.GetAxisRaw("Vertical")).normalized * speed, velocityLerpCoef * Time.deltaTime);

            // Assigning velocity to the mimic to assure great leg placement
            myMimic.velocity = velocity;

            transform.position = transform.position + velocity * Time.deltaTime;
            RaycastHit hit;
            Vector3 destHeight = transform.position;
            if (Physics.Raycast(transform.position + Vector3.up * 5f, -Vector3.up, out hit))
                destHeight = new Vector3(transform.position.x, hit.point.y + height, transform.position.z);
            transform.position = Vector3.Lerp(transform.position, destHeight, velocityLerpCoef * Time.deltaTime);
        }
    }

}