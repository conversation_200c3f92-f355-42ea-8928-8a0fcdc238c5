{"name": "FMODUnity", "references": ["Unity.Timeline", "Unity.Addressables", "Unity.ResourceManager", "Unity.RenderPipelines.Universal.Runtime"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": ["UNITY_2021_3_OR_NEWER"], "versionDefines": [{"name": "com.unity.timeline", "expression": "1.0.0", "define": "UNITY_TIMELINE_EXIST"}, {"name": "com.unity.addressables", "expression": "1.0.0", "define": "UNITY_ADDRESSABLES_EXIST"}, {"name": "com.unity.modules.physics", "expression": "1.0.0", "define": "UNITY_PHYSICS_EXIST"}, {"name": "com.unity.modules.physics2d", "expression": "1.0.0", "define": "UNITY_PHYSICS2D_EXIST"}, {"name": "com.unity.urp", "expression": "1.0.0", "define": "UNITY_URP_EXIST"}], "noEngineReferences": false}