using System;

namespace RibCageGames.Base
{
    using UnityEngine;

    public abstract class BaseService : ScriptableObject
    {

        [SerializeField] protected GameObject m_servicePrefab;

        public GameObject ServicePrefab => m_servicePrefab;

        public virtual Type RegisteredType => GetType();

        /// <summary>
        /// Used for own initialization
        /// </summary>
        public abstract void Init(GameObject servicePrefab = null);

        /// <summary>
        /// Used for connecting to other services and related processes
        /// </summary>
        public abstract void StartService(MonoInjector injector);

        /// <summary>
        /// Used for disabling a service
        /// </summary>
        public abstract void Dispose();
    }
}