using System;
using Cysharp.Threading.Tasks;

namespace RibCageGames.Base
{
    using System.Collections.Generic;
    using System.Reflection;
    using UnityEngine;
    using UnityEngine.Events;

    /// <summary>
    /// A service to better handle Update style callbacks
    /// </summary>
    [CreateAssetMenu(fileName = "MonoService", menuName = "RibCageGames/Services/MonoService")]
    public class MonoService : BaseService
    {

        [SerializeField] private int _slowUpdateRate = 10;

        public class OnUpdateHandler : UnityEvent<float>{}

        public OnUpdateHandler OnUpdate { get; } = new OnUpdateHandler();
        
        public class OnUpdateUnscaledHandler : UnityEvent<float>{}

        public OnUpdateUnscaledHandler OnUpdateUnscaled { get; } = new OnUpdateUnscaledHandler();

        public class OnLateUpdateHandler : UnityEvent<float>{}

        public OnLateUpdateHandler OnLateUpdate { get; } = new OnLateUpdateHandler();

        public class OnFixedUpdateHandler : UnityEvent<float> {}

        public OnFixedUpdateHandler OnFixedUpdate { get; } = new OnFixedUpdateHandler();

        public class OnPerSecondUpdateHandler : UnityEvent<float> {}

        public OnPerSecondUpdateHandler OnPerSecondUpdate { get; } = new OnPerSecondUpdateHandler();
        
        public class OnTimeScaleChangedHandler : UnityEvent<float> {}

        public OnTimeScaleChangedHandler OnTimeScaleChanged { get; } = new OnTimeScaleChangedHandler();

        public List<List<UnityAction<float>>> OnSlowUpdate { get; private set; }

        public MonoInjector MonoInjector { get; private set; }

        private float _slowUpdateTimeSum = 0f;
        private int _slowUpdateFrameCount = 0;
        private float _slowUpdateGroupAverage = 0f;

        private float m_timeScale = 1f;

        private Dictionary<UnityAction<float>, int>
            _slowUpdateInsertionDict = new Dictionary<UnityAction<float>, int>();

        private HashSet<MonoScriptableObject> m_registeredMonoScriptableObjects = new HashSet<MonoScriptableObject>();

        private float _perSecondUpdateCounter = 0f;

        private Queue<float> _frameDurations = new Queue<float>();
        [NonSerialized] private bool m_monoscriptableObjectsInitialized = false;

        public void SetTimeScale(float value)
        {
            m_timeScale = value;
            OnTimeScaleChanged?.Invoke(m_timeScale);
        }

        public override void Init(GameObject servicePrefab = null)
        {
            OnSlowUpdate = new List<List<UnityAction<float>>>(_slowUpdateRate);
            for (int i = 0; i < _slowUpdateRate; i++)
            {
                OnSlowUpdate.Add(new List<UnityAction<float>>());
            }
            
            SetTimeScale(1f);
        }

        public override void StartService(MonoInjector injector)
        {
            MonoInjector = injector;
            InitializeMonoScriptableObjects();
            injector.OnUpdate.AddListener(ControlledUpdate);
            injector.OnFixedUpdate.AddListener(ControlleFixedUpdate);
            injector.OnLateUpdate.AddListener(ControlledLateUpdate);
        }

        private async void InitializeMonoScriptableObjects()
        {
            await UniTask.DelayFrame(1);
            foreach (MonoScriptableObject monoScriptable in m_registeredMonoScriptableObjects)
            {
                monoScriptable.Initialize();
            }

            m_monoscriptableObjectsInitialized = true;
        }

        public override void Dispose() {}
        
        private void ControlledUpdate(float deltaTime)
        {
            float scaledDelta = m_timeScale * deltaTime;
            OnUpdate?.Invoke(scaledDelta);
            OnUpdateUnscaled?.Invoke(deltaTime);
            PerSecondUpdate(scaledDelta);
            SlowUpdate(scaledDelta);
        }
        
        public static int GetListenerNumber(UnityEventBase unityEvent)
        {
            var field = typeof(UnityEventBase).GetField("m_Calls", BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.DeclaredOnly );
            var invokeCallList = field.GetValue(unityEvent);
            var property = invokeCallList.GetType().GetProperty("Count");
            return (int)property.GetValue(invokeCallList);
        }

        private void ControlleFixedUpdate(float deltaTime)
        {
            OnFixedUpdate?.Invoke(Time.fixedDeltaTime);
        }

        private void PerSecondUpdate(float deltaTime)
        {
            _perSecondUpdateCounter += deltaTime;
            if (_perSecondUpdateCounter >= 1f)
            {
                OnPerSecondUpdate.Invoke(_perSecondUpdateCounter);
                _perSecondUpdateCounter -= 1f;
            }
        }

        //TODO: check accuracy
        private void SlowUpdate(float deltaTime)
        {

            _frameDurations.Enqueue(deltaTime);
            _slowUpdateTimeSum += deltaTime;
            if (_frameDurations.Count >= _slowUpdateRate)
            {
                //If it's been more than _slowUpdateRate frames, start calling slowUpdates
                _slowUpdateTimeSum -= _frameDurations.Dequeue();
                List<UnityAction<float>> slowUpdateGroup = OnSlowUpdate[_slowUpdateFrameCount];
                if (slowUpdateGroup != null)
                {
                    int groupCount = slowUpdateGroup.Count;
                    for (int i = 0; i < groupCount; i++)
                    {
                        slowUpdateGroup[i]?.Invoke(_slowUpdateTimeSum);
                    }
                }

                _slowUpdateFrameCount = (_slowUpdateFrameCount + 1) % _slowUpdateRate;
            }
        }

        public void RegisterSlowUpdate(UnityAction<float> controlledUpdate)
        {
            //No registered yet register to bucket 0
            if (_slowUpdateGroupAverage == 0f)
            {
                OnSlowUpdate[0].Add(controlledUpdate);
                _slowUpdateInsertionDict.Add(controlledUpdate, 0);
            }
            else
            {
                bool controlledUpdateInserted = false;
                for (int i = 0; i < OnSlowUpdate.Capacity; i++)
                {
                    //Find a bucket with less than average load, register to that
                    if (OnSlowUpdate[i].Count < _slowUpdateGroupAverage)
                    {
                        OnSlowUpdate[i].Add(controlledUpdate);
                        _slowUpdateInsertionDict.Add(controlledUpdate, i);
                        controlledUpdateInserted = true;
                        break;
                    }
                }

                //Everything is uniform, insert into first bucket
                if (!controlledUpdateInserted)
                {
                    OnSlowUpdate[0].Add(controlledUpdate);
                    _slowUpdateInsertionDict.Add(controlledUpdate, 0);
                }
            }

            //Update average
            _slowUpdateGroupAverage += 1f / _slowUpdateRate;
        }

        public void UnregisterSlowUpdate(UnityAction<float> controlledUpdate)
        {
            OnSlowUpdate[_slowUpdateInsertionDict[controlledUpdate]].Remove(controlledUpdate);
            _slowUpdateInsertionDict.Remove(controlledUpdate);
            //Update average
            _slowUpdateGroupAverage -= 1f / _slowUpdateRate;
        }

        private void ControlledLateUpdate(float deltaTime)
        {
            float scaledDelta = m_timeScale * deltaTime;
            OnLateUpdate?.Invoke(scaledDelta);
        }

        public void RegisterMonoScriptableObject(MonoScriptableObject monoScriptableObject)
        {
            m_registeredMonoScriptableObjects.Add(monoScriptableObject);
            if (m_monoscriptableObjectsInitialized)
            {
                monoScriptableObject.Initialize();
            }
        }
    }
}