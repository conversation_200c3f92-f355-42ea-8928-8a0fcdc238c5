using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using UnityEngine;

public abstract class MonoScriptableObject : ScriptableObject
{
    private async void OnEnable()
    {
        var monoservice = ServiceLocator.EditorGet<MonoService>();

        while (monoservice == null)
        {
            await UniTask.DelayFrame(1);
            monoservice = ServiceLocator.EditorGet<MonoService>();
        }
        
        monoservice.RegisterMonoScriptableObject(this);
    }

    public abstract void Initialize();
}
