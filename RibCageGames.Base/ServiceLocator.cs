namespace RibCageGames.Base
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using UnityEngine;

    [CreateAssetMenu(fileName = "ServiceLocator", menuName = "RibCageGames/Services/ServiceLocator")]
    public class ServiceLocator : ScriptableObject
    {
        private static ServiceLocator m_instance;

        [SerializeField] private List<BaseService> m_services;

        [Tooltip("Used for assets that need controlled initialization that aren't services")] [SerializeField]
        private List<BaseService> m_initializedAssets;

        private static Dictionary<Type, BaseService> m_serviceDict;
        private MonoInjector m_injector;
        [NonSerialized] private bool m_initialized = false;

        [RuntimeInitializeOnLoadMethod]
        public static void InitializeServiceLocator()
        {
            m_instance.Init();
        }

        /// <summary>
        /// Pre-mono Injector initialization
        /// </summary>
        public void Init()
        {
            if (m_initialized)
            {
                Debug.LogError($"Initializing Service locator a second time");
                return;
            }
            m_initialized = true;
            Debug.Log($"Init ServiceLocator");
            m_serviceDict = new Dictionary<Type, BaseService>();

            m_injector = MonoInjector.Instance;
            m_injector.SetOnComplete(InitializeMono);

            for (int i = 0; i < m_services.Count; i++)
            {
                GameObject sp = null;
                if (m_services[i].ServicePrefab != null)
                {
                    sp = Instantiate(m_services[i].ServicePrefab);
                    DontDestroyOnLoad(sp.gameObject); //WHAT THE ACTUAL FUCK
                    //TODO: these are loaded into some kind of virtual scene and then deleted so without DontDestroyOnLoad everything nullrefs the fuck out  
                    //TODO: figure out WTF
                }

                m_services[i].Init(sp);
                m_serviceDict.Add(m_services[i].RegisteredType, m_services[i]);
                //Debug.Log("Added service of type: " + m_services[i].GetType());
            }

            foreach (BaseService asset in m_initializedAssets)
            {
                asset.Init(); //TODO:Use different base class instead of service
            }
        }

        /// <summary>
        /// Pre-mono Injector initialization
        /// </summary>
        private void InitializeMono()
        {
            for (int i = 0; i < m_services.Count; i++)
            {
                m_services[i].StartService(m_injector);
            }

            foreach (BaseService asset in m_initializedAssets)
            {
                asset.StartService(m_injector); //TODO:Use different base class instead of service
            }
        }

        public static T Get<T>() where T : BaseService
        {
            Type type = typeof(T);

            if (m_serviceDict != null && m_serviceDict.TryGetValue(type, out BaseService res))
            {
                return (T)res;
            }

            return null;
        }

        public static T EditorGet<T>() where T : BaseService
        {
            if (m_instance == null)
            {
                return null;    
            }
            
            Type type = typeof(T);

            foreach (BaseService service in m_instance.m_services)
            {
                if (service.RegisteredType == type)
                {
                    return (T)service;
                }
            }
            return null;
        }

        public void QuitGame()
        {
            Application.Quit();
        }

        private void OnEnable()
        {
            //Init();
            m_instance = this; 
            m_initialized = false;
            Debug.Log($"OnEnable ServiceLocator");
#if !UNITY_EDITOR
        m_instance.Init();
#endif
        }
    }
}