using System;
using System.Collections;
using System.Collections.Generic;
using RibCageGames.Base;
using UnityEngine;
using UnityEngine.Events;


[CreateAssetMenu(fileName = "UnityEventAsset", menuName = "RibCageGames/Base/UnityEventAsset")]
public class UnityEventAsset : MonoScriptableObject
{
    [SerializeField] private UnityEvent m_event;

    [SerializeField] private List<DelayedEventStep> m_events;
    
    public void Activate()
    {
        m_event?.Invoke();
        if (m_events != null && m_events.Count > 0)
        {
            ActivateSequenceOnly();
        }
    }

    public override void Initialize()
    {
    }
    
    public void ActivateSequenceOnly()
    {
        MonoProcess process = MonoProcess.New();

        foreach (DelayedEventStep step in m_events)
        {
            process.WaitForSeconds(step.delay)
                .Do(step.action.Invoke);
        }
    }

    [Serializable]
    public class DelayedEventStep
    {
        [SerializeField] private float m_delay;
        [SerializeField] private UnityEvent m_action;

        internal float delay => m_delay;
        internal UnityEvent action => m_action;
    }
}
