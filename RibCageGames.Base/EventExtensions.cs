using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine.Events;

namespace RibCageGames.Base
{
    public static class EventExtensions
    {
        /// <summary>
        /// Used for an event listener that only happens once
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="cancellationToken"></param>
        public static UnityAction AddCancellableListener(this UnityEvent unityEvent, UnityAction action,
            CancellationToken cancellationToken = default(CancellationToken))
        {
            UnityAction deactivate = null;
            deactivate = () =>
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    unityEvent.RemoveListener(deactivate);
                }
                else
                {
                    action?.Invoke();
                }
            };

            unityEvent.AddListener(deactivate);
            return () => unityEvent.RemoveListener(deactivate);
        }

        /// <summary>
        /// Used for an event listener that only happens once
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="cancellationToken"></param>
        public static void AddSingleUseListener(this UnityEvent unityEvent, UnityAction action,
            CancellationToken cancellationToken = default)
        {
            UnityAction deactivate = null;
            deactivate = () =>
            {
                if (!cancellationToken.IsCancellationRequested)
                {
                    action?.Invoke();
                }

                unityEvent.RemoveListener(deactivate);
            };

            unityEvent.AddListener(deactivate);
        }

        /// <summary>
        /// Used for an event listener that only happens once
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="cancellationToken"></param>
        /// <typeparam name="T"></typeparam>
        public static void AddSingleUseListener<T>(this UnityEvent<T> unityEvent, UnityAction<T> action,
            CancellationToken cancellationToken = default)
        {
            UnityAction<T> deactivate = null;
            deactivate = (x) =>
            {
                if (!cancellationToken.IsCancellationRequested)
                {
                    action?.Invoke(x);
                }

                unityEvent.RemoveListener(deactivate);
            };

            unityEvent.AddListener(deactivate);
        }
        
        /// <summary>
        /// Used for an event listener that only happens once
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="cancellationToken"></param>
        public static async UniTask WaitForEventTrigger(this UnityEvent unityEvent,
            CancellationToken cancellationToken = default)
        {
            bool triggered = false;
            UnityAction deactivate = null;
            deactivate = () =>
            {
                triggered = true;
                unityEvent.RemoveListener(deactivate);
            };

            unityEvent.AddListener(deactivate);
            
            await UniTask.WaitUntil(() => triggered, cancellationToken: cancellationToken);
        }
        
        /// <summary>
        /// Used for an event listener that only happens once
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="cancellationToken"></param>
        public static async UniTask WaitForEventTrigger<T>(this UnityEvent<T> unityEvent,
            CancellationToken cancellationToken = default)
        {
            bool triggered = false;
            UnityAction<T> deactivate = null;
            deactivate = (_) =>
            {
                triggered = true;
                unityEvent.RemoveListener(deactivate);
            };

            unityEvent.AddListener(deactivate);
            
            await UniTask.WaitUntil(() => triggered, cancellationToken: cancellationToken);
        }
        
        /// <summary>
        /// Used for an event listener that only happens once
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="cancellationToken"></param>
        public static async UniTask WaitForEventTrigger<T,S>(this UnityEvent<T,S> unityEvent,
            CancellationToken cancellationToken = default)
        {
            bool triggered = false;
            UnityAction<T,S> deactivate = null;
            deactivate = (_,_) =>
            {
                triggered = true;
                unityEvent.RemoveListener(deactivate);
            };

            unityEvent.AddListener(deactivate);
            
            await UniTask.WaitUntil(() => triggered, cancellationToken: cancellationToken);
        }
        
        /// <summary>
        /// Used for an event listener that only happens once
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="cancellationToken"></param>
        public static void AddCancellableListener<T>(this UnityEvent<T> unityEvent, UnityAction<T> action,
            CancellationToken cancellationToken = default(CancellationToken))
        {
            UnityAction<T> deactivate = null;
            deactivate = (x) =>
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    unityEvent.RemoveListener(deactivate);
                }
                else
                {
                    action?.Invoke(x);
                }
            };

            unityEvent.AddListener(deactivate);
        }

        /// <summary>
        /// Used for an event listener that only happens once
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="cancellationToken"></param>
        /// <typeparam name="T"></typeparam>
        /// <typeparam name="S"></typeparam>
        public static void AddSingleUseListener<T, S>(this UnityEvent<T, S> unityEvent, UnityAction<T, S> action,
            CancellationToken cancellationToken = default(CancellationToken))
        {
            UnityAction<T, S> deactivate = null;
            deactivate = (x, y) =>
            {
                if (!cancellationToken.IsCancellationRequested)
                {
                    action?.Invoke(x, y);
                }

                unityEvent.RemoveListener(deactivate);
            };

            unityEvent.AddListener(deactivate);
        }
        
        /// <summary>
        /// Used for an event listener that only happens once
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="cancellationToken"></param>
        public static UnityAction<T,S> AddCancellableListener<T,S>(this UnityEvent<T,S> unityEvent, UnityAction<T,S> action,
            CancellationToken cancellationToken = default(CancellationToken))
        {
            UnityAction<T,S> deactivate = null;
            deactivate = (x, y) =>
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    unityEvent.RemoveListener(deactivate);
                }
                else
                {
                    action?.Invoke(x, y);
                }
            };

            unityEvent.AddListener(deactivate);
            return (x, y) => unityEvent.RemoveListener(deactivate);;
        }

        /// <summary>
        /// Used for an event listener that keeps getting called as long as predicate is false (inclusive)
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="predicate"></param>
        /// <param name="cancellationToken"></param>
        /// <typeparam name="T"></typeparam>
        public static void AddListenerUntil(this UnityEvent unityEvent, UnityAction action, Func<bool> predicate,
            CancellationToken cancellationToken = default(CancellationToken), bool inclusiveCallback = true)
        {
            UnityAction deactivate = null;
            deactivate = () =>
            {
                if (predicate.Invoke() || cancellationToken.IsCancellationRequested)
                {
                    unityEvent.RemoveListener(deactivate);
                    if (inclusiveCallback)
                    {
                        action?.Invoke();
                    }
                }
                else
                {
                    action?.Invoke();
                }
            };

            unityEvent.AddListener(deactivate);
        }

        /// <summary>
        /// Used for an event listener that keeps getting called as long as predicate is false (inclusive)
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="predicate"></param>
        /// <param name="cancellationToken"></param>
        /// <typeparam name="T"></typeparam>
        public static void AddListenerUntil<T>(this UnityEvent<T> unityEvent, UnityAction<T> action,
            Func<T, bool> predicate, CancellationToken cancellationToken = default(CancellationToken), bool inclusiveCallback = true)
        {
            UnityAction<T> deactivate = null;
            deactivate = (x) =>
            {
                if (predicate.Invoke(x) || cancellationToken.IsCancellationRequested)
                {
                    unityEvent.RemoveListener(deactivate);
                    if (inclusiveCallback)
                    {
                        action?.Invoke(x);
                    }
                }
                else
                {
                    action?.Invoke(x);
                }
            };

            unityEvent.AddListener(deactivate);
        }

        /// <summary>
        /// Used for an event listener that keeps getting called as long as predicate is false (inclusive)
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="predicate"></param>
        /// <param name="cancellationToken"></param>
        /// <typeparam name="T"></typeparam>
        public static void AddListenerUntil<T, S>(this UnityEvent<T, S> unityEvent, UnityAction<T, S> action,
            Func<T, S, bool> predicate, CancellationToken cancellationToken = default(CancellationToken), bool inclusiveCallback = true)
        {
            UnityAction<T, S> deactivate = null;
            deactivate = (x, y) =>
            {
                if (predicate.Invoke(x, y) || cancellationToken.IsCancellationRequested)
                {
                    unityEvent.RemoveListener(deactivate);
                    if (inclusiveCallback)
                    {
                        action?.Invoke(x, y);
                    }
                }
                else
                {
                    action?.Invoke(x, y);
                }
            };

            unityEvent.AddListener(deactivate);
        }

        /// <summary>
        /// Used for a single action activateable by multiple events
        /// </summary>
        /// <param name="unityEvents"></param>
        /// <param name="action"></param>
        /// <param name="cancellationToken"></param>
        /// <typeparam name="T"></typeparam>
        public static void AddSingleUseListenerGroup<T>(this List<UnityEvent<T>> unityEvents, UnityAction<T> action,
            CancellationToken cancellationToken = default(CancellationToken))
        {
            UnityAction<T> deactivate = null;
            deactivate = (x) =>
            {

                if (!cancellationToken.IsCancellationRequested)
                {
                    action?.Invoke(x);
                }

                foreach (UnityEvent<T> register in unityEvents)
                {
                    register.RemoveListener(deactivate);
                }
            };

            foreach (UnityEvent<T> register in unityEvents)
            {
                register.AddListener(deactivate);
            }
        }
        
        /// <summary>
        /// Used for an event listener that only happens once
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="cancellationToken"></param>
        public static void AddExclusiveListener(this UnityEvent unityEvent, UnityAction action, CancellationToken cancellationToken = default(CancellationToken))
        {
            unityEvent.RemoveListener(action);
            unityEvent.AddListener(action);
            
            //TODO: figure out why this causes double registration
            //unityEvent.AddCancellableListener(action, cancellationToken);
        }
        
        /// <summary>
        /// Used for an event listener that only happens once
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="cancellationToken"></param>
        public static void AddExclusiveListener<T>(this UnityEvent<T> unityEvent, UnityAction<T> action, CancellationToken cancellationToken = default(CancellationToken))
        {
            unityEvent.RemoveListener(action);
            unityEvent.AddListener(action);
            
            //unityEvent.AddCancellableListener(action, cancellationToken);
        }
        
        /// <summary>
        /// Used for an event listener that only happens once
        /// </summary>
        /// <param name="unityEvent"></param>
        /// <param name="action"></param>
        /// <param name="cancellationToken"></param>
        public static void AddExclusiveListener<T,S>(this UnityEvent<T,S> unityEvent, UnityAction<T,S> action, CancellationToken cancellationToken = default(CancellationToken))
        {
            unityEvent.RemoveListener(action);
            unityEvent.AddListener(action);
            
            //unityEvent.AddCancellableListener(action, cancellationToken);
        }
    }
}
