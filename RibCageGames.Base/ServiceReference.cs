using System;
using RibCageGames.Base;

//TODO: make this implicit and only compare once
public class ServiceReference<T> where T : BaseService
{
    private T m_backingValue;
    
    private Func<T> m_getterAction;

    public T Value => m_getterAction();
    
    public ServiceReference()
    {
        m_getterAction = AssignAndGetValue;
    }

    private T AssignAndGetValue()
    {
        m_backingValue = ServiceLocator.Get<T>();
        m_getterAction = GetValue;
        return m_backingValue;
    }
    
    private T GetValue()
    {
        return m_backingValue;
    }
    
    public static implicit operator T(ServiceReference<T> serviceRef)
    {
        return serviceRef.Value;
    }
}
