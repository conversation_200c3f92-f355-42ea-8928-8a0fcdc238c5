namespace RibCageGames.Base
{
    using System;
    using UnityEngine;
    using UnityEngine.Events;

//TODO: make self creating
    public class MonoInjector : MonobehaviourSingleton<MonoInjector>
    {

        public class OnUpdateHandler : UnityEvent<float>
        {
        }

        public OnUpdateHandler OnUpdate { get; } = new OnUpdateHandler();

        public class OnLateUpdateHandler : UnityEvent<float>
        {
        }

        public OnLateUpdateHandler OnLateUpdate { get; } = new OnLateUpdateHandler();

        public class OnFixedUpdateHandler : UnityEvent<float>
        {
        }

        public OnFixedUpdateHandler OnFixedUpdate { get; } = new OnFixedUpdateHandler();

        public class OnApplicationFocusHandler : UnityEvent<bool>
        {
        }

        public OnApplicationFocusHandler OnApplicationFocusEvent { get; } = new OnApplicationFocusHandler();

        public class OnApplicationPauseHandler : UnityEvent<bool>
        {
        }

        public OnApplicationPauseHandler OnApplicationPauseEvent { get; } = new OnApplicationPauseHandler();

        public class OnApplicationQuitHandler : UnityEvent
        {
        }

        public OnApplicationQuitHandler OnApplicationQuitEvent { get; } = new OnApplicationQuitHandler();

        private Action m_onStartupComplete;

        protected override void Awake()
        {
            base.Awake();
        }

        public void SetOnComplete(Action onComplete)
        {
            m_onStartupComplete = onComplete;
        }

        private void Start()
        {
            m_onStartupComplete?.Invoke();
        }

        private void Update()
        {
            OnUpdate?.Invoke(Time.deltaTime);
        }

        private void FixedUpdate()
        {
            OnFixedUpdate?.Invoke(Time.fixedDeltaTime);
        }

        private void LateUpdate()
        {
            OnLateUpdate?.Invoke(Time.deltaTime);
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            OnApplicationFocusEvent?.Invoke(hasFocus);
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            OnApplicationPauseEvent?.Invoke(pauseStatus);
        }

        

        private void OnApplicationQuit()
        {
            OnApplicationQuitEvent?.Invoke();
        }
    }
}