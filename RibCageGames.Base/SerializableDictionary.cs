using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

[Serializable]
public class SerializableDictionary<Tkey, Tvalue> : IEnumerable
{
    private Dictionary<Tkey, Tvalue> m_generatedDictionary;

    [SerializeField] private Tkey[] m_keys;
    [SerializeField] private Tvalue[] m_values;

    public Dictionary<Tkey, Tvalue> GeneratedDictionary
    {
        get
        {
            InitDictionary();
            return m_generatedDictionary;
        }
    }
    
    public Tvalue this[Tkey index]
    {
        get
        {
            InitDictionary();
            return m_generatedDictionary[index];
        }
        set
        {
            InitDictionary();
            m_generatedDictionary[index] = value;
        }
    }

    public bool ContainsKey(Tkey key)
    {
        InitDictionary();
        return m_generatedDictionary.ContainsKey(key);
    }

    public Tvalue[] Values => m_values;
    
    public Tkey[] Keys => m_keys;

    public void ReSerialize()
    {
        m_keys = m_generatedDictionary.Keys.ToArray();
        m_values = m_generatedDictionary.Values.ToArray();
    }

    private void InitDictionary()
    {
        if (m_generatedDictionary != null) { return; }
        
        m_generatedDictionary = new Dictionary<Tkey, Tvalue>();

        if (m_keys.Length != m_values.Length)
        {
            //TODO: Make property drawer to use hashsets here for validation
            Debug.Log($"SerializableDictionary for {nameof(Tkey)} and {nameof(Tvalue)} not setup properly");
            return;
        }

        for (int i = 0; i < m_keys.Length; i++)
        {
            m_generatedDictionary.Add(m_keys[i], m_values[i]);
        }
    }

    public IEnumerator GetEnumerator()
    {
        InitDictionary();
        return m_generatedDictionary.GetEnumerator();
    }

    public bool TryGetValue(Tkey key, out Tvalue value)
    {
        if (ContainsKey(key))
        {
            value = m_generatedDictionary[key];
            return true;
        }
        
        value = default(Tvalue);
        return false;
    }
}
