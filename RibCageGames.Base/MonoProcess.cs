using System.Threading;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;

namespace RibCageGames.Base
{
    using System;
    using System.Collections.Generic;
    using UnityEngine;

    public class MonoProcess : IDisposable
    {
        protected enum StepType
        {
            Wait<PERSON><PERSON><PERSON><PERSON><PERSON>,
            Wait<PERSON>or<PERSON><PERSON>ed<PERSON>rame,
            WaitForSecond,
            WaitForSecondUnscaled,
            WaitForSecondExact,
            WaitWhile,
            WaitFor,
            Action,
            ICompletable,
            UniTask,
        }

        protected Queue<MonoProcessStep> m_stepQueue;
        private MonoService m_monoService;
        private float m_exactTimeElapsed = 0f;
        private float m_exactTimeRequired = 0f;
        private Func<bool> m_predicate = null;
        private bool m_done = false;
        private bool m_disposed = false;
        private CancellationToken m_cancellationToken;

        private string debugId;

        private StepType m_currentStepType;

        private bool m_debug = false;
        private bool m_running = false;

        public int StepsInQueue => m_stepQueue.Count;

        public bool Done => m_done;

        public float ElapsedTime =>
            m_exactTimeElapsed; //TODO: rework a bit does not track entire process only single waitExactStep

        private static int processesRunning = 0;

        protected MonoProcess(CancellationToken cancellationToken = default(CancellationToken))
        {
            processesRunning++;
            m_stepQueue = new Queue<MonoProcessStep>();
            //TODO: cache this statically?
            m_monoService = ServiceLocator.EditorGet<MonoService>();
            m_cancellationToken = cancellationToken;
        }

        ~MonoProcess()
        {
            processesRunning--;
            //Debug.Log($"Killed MonoProcess mow have {processesRunning}");
        }


        public MonoProcess SetDebug()
        {
            m_debug = true;
            return this;
        }

        public MonoProcess SetDebug(string debugID)
        {
            m_debug = true;
            debugId = debugID;
            return this;
        }

        /// <summary>
        /// Create new MonoProcess, auto starts after one frame by default
        /// </summary>
        /// <param name="autoStart"></param>
        /// <returns></returns>
        public static MonoProcess New(bool autoStart = true, CancellationToken cancellationToken = default(CancellationToken))
        {
            MonoProcess process = new MonoProcess(cancellationToken);
            if (autoStart)
            {
                NewManual(cancellationToken) //Needs to be false to not cause infinite recursion
                    .WaitForFrame()
                    .Do(process.Run)
                    .Run();
            }

            return process;
        }

        public static MonoProcess NextFrame => New();

        public static MonoProcess NewManual(CancellationToken cancellationToken = default(CancellationToken))
        {
            return new MonoProcess(cancellationToken);
        }

        public static MonoProcess WaitForSecondsProcess(float duration, CancellationToken cancellationToken = default(CancellationToken))
        {
            MonoProcess process = new MonoProcess(cancellationToken);
            process.m_stepQueue.Enqueue(new MonoProcessStep
            {
                StepType = StepType.WaitForSecondExact,
                Action = null,
                TimeRequired = duration,
            });

            NewManual(cancellationToken)
                .WaitForFrame()
                .Do(process.Run)
                .Run();

            return process;
        }
        
        public static MonoProcess WaitForSecondsUnscaledProcess(float duration, CancellationToken cancellationToken = default(CancellationToken))
        {
            MonoProcess process = new MonoProcess(cancellationToken);
            process.m_stepQueue.Enqueue(new MonoProcessStep
            {
                StepType = StepType.WaitForSecondUnscaled,
                Action = null,
                TimeRequired = duration,
            });

            NewManual(cancellationToken)
                .WaitForFrame()
                .Do(process.Run)
                .Run();

            return process;
        }

        public void Run()
        {
            if (!m_running)
            {
                m_running = true;
                PerformProcessStep();
            }
        }

        public void Stop()
        {
            Dispose();
        }

        private async void PerformProcessStep()
        {
            if (m_cancellationToken.IsCancellationRequested || m_done || m_stepQueue.Count < 1)
            {
                Dispose();
                return;
            }

            MonoProcessStep step = m_stepQueue.Dequeue();
            m_currentStepType = step.StepType;
            switch (step.StepType)
            {
                case StepType.WaitForFrame:
                    m_monoService.OnUpdate.AddListener(UpdateStep);
                    break;

                case StepType.WaitForFixedFrame:
                    m_monoService.OnFixedUpdate.AddListener(UpdateFixedStep);
                    break;

                case StepType.WaitForSecond:
                    m_monoService.OnPerSecondUpdate.AddListener(UpdatePerSecondStep);
                    break;
                
                case StepType.WaitForSecondUnscaled:
                    m_exactTimeRequired = step.TimeRequired;
                    m_exactTimeElapsed = 0f;
                    m_monoService.OnUpdateUnscaled.AddListener(UpdateStepExact);
                    break;

                case StepType.WaitForSecondExact:
                    m_exactTimeRequired = step.TimeRequired;
                    m_exactTimeElapsed = 0f;
                    if (!string.IsNullOrEmpty(debugId))
                    {
                        Debug.Log($"Started UpdateStepExact for {debugId}");
                    }

                    m_monoService.OnUpdate.AddListener(UpdateStepExact);
                    break;

                case StepType.WaitWhile:
                    m_predicate = step.Predicate;
                    m_monoService.OnUpdate.AddListener(UpdateStepPredicate);
                    break;

                case StepType.WaitFor:
                    m_predicate = step.Predicate;
                    m_exactTimeElapsed = 0f;
                    m_monoService.OnUpdate.AddListener(UpdateStepPredicate);
                    break;
                case StepType.UniTask:
                    await step.Task;
                    PerformProcessStep();
                    break;
                case StepType.Action:
                    step.Action.Invoke();
                    PerformProcessStep();
                    break;

                default:
                    break;
            }
        }

        private void UpdateStep(float delta)
        {
            if (m_cancellationToken.IsCancellationRequested || m_disposed) { return; }

            m_monoService.OnUpdate.RemoveListener(UpdateStep);
            PerformProcessStep();
        }

        private void UpdateFixedStep(float delta)
        {
            if (m_cancellationToken.IsCancellationRequested || m_disposed) { return; }
            
            m_monoService.OnFixedUpdate.RemoveListener(UpdateFixedStep);
            PerformProcessStep();
        }

        private void UpdatePerSecondStep(float delta)
        {
            if (m_cancellationToken.IsCancellationRequested || m_disposed) { return; }
            
            m_monoService.OnPerSecondUpdate.RemoveListener(UpdatePerSecondStep);
            PerformProcessStep();
        }

        private void UpdateStepExact(float delta)
        {
            if (m_cancellationToken.IsCancellationRequested || m_disposed) { return; }

            if (m_debug)
            {
                //Debug.Log($"UpdateStepExact for WaitForSeconds m_exactTimeElapsed is {m_exactTimeElapsed}");
            }

            m_exactTimeElapsed += delta;
            if (m_exactTimeElapsed > m_exactTimeRequired)
            {
                if (!string.IsNullOrEmpty(debugId))
                {
                    Debug.Log($"Ending UpdateStepExact for {debugId}");
                }

                m_monoService.OnUpdate.RemoveListener(UpdateStepExact);
                PerformProcessStep();
            }
        }

        private void UpdateStepPredicate(float delta)
        {
            if (m_cancellationToken.IsCancellationRequested || m_disposed) { return; }
            
            m_exactTimeElapsed += delta;

            if (m_predicate == null) //TODO: understand why this derps sometimes
            {
                m_monoService.OnUpdate.RemoveListener(UpdateStepPredicate);
                return;
            }

            if (m_predicate.Invoke())
            {
                m_ICompletableDone = false;
                m_monoService.OnUpdate.RemoveListener(UpdateStepPredicate);
                PerformProcessStep();
            }
        }

        public MonoProcess WaitForFrame(int amount = 1)
        {
            if (!m_cancellationToken.IsCancellationRequested && !m_done)
            {
                for (int i = 0; i < amount; i++)
                {
                    m_stepQueue.Enqueue(new MonoProcessStep
                    {
                        StepType = StepType.WaitForFrame,
                        Action = null,
                    });
                }

                return this;
            }
            else
            {
                return null;
            }
        }

        public MonoProcess WaitForFrameAsync(int amount = 1)
        {
            if (!m_cancellationToken.IsCancellationRequested && !m_done)
            {
                m_stepQueue.Enqueue(new MonoProcessStep
                {
                    StepType = StepType.UniTask,
                    Task = UniTask.DelayFrame(amount, cancellationToken: m_cancellationToken),
                });
                return this;
            }
            else
            {
                return null;
            }
        }
        
        public MonoProcess Do(UniTask task)
        {
            if (!m_cancellationToken.IsCancellationRequested && !m_done)
            {
                m_stepQueue.Enqueue(new MonoProcessStep
                {
                    StepType = StepType.UniTask,
                    Task = task,
                });
                return this;
            }
            else
            {
                return null;
            }
        }

        public async Task Await()
        {
            await UniTask.WaitUntil(() => Done, cancellationToken: m_cancellationToken);
        }

        public MonoProcess WaitForFixedFrame(int amount = 1)
        {
            if (!m_cancellationToken.IsCancellationRequested && !m_done)
            {
                for (int i = 0; i < amount; i++)
                {
                    m_stepQueue.Enqueue(new MonoProcessStep
                    {
                        StepType = StepType.WaitForFixedFrame,
                        Action = null,
                    });
                }

                return this;
            }
            else
            {
                return null;
            }
        }

        public MonoProcess WaitForSeconds(int amount = 1)
        {
            if (!m_cancellationToken.IsCancellationRequested && !m_done)
            {
                for (int i = 0; i < amount; i++)
                {
                    m_stepQueue.Enqueue(new MonoProcessStep
                    {
                        StepType = StepType.WaitForSecond,
                        Action = null,
                    });
                }

                return this;
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Continue the process after duration seconds.
        /// </summary>
        /// <param name="duration"></param>
        /// <returns></returns>
        public MonoProcess WaitForSeconds(float duration)
        {
            if (!m_cancellationToken.IsCancellationRequested && !m_done)
            {
                if (!string.IsNullOrEmpty(debugId))
                {
                    Debug.Log($"Start WaitForSeconds for {debugId}");
                }

                m_stepQueue.Enqueue(new MonoProcessStep
                {
                    StepType = StepType.WaitForSecondExact,
                    Action = null,
                    TimeRequired = duration,
                });
                return this;
            }
            else
            {
                return null;
            }
        }
        
        /// <summary>
        /// Continue the process after duration seconds.
        /// </summary>
        /// <param name="duration"></param>
        /// <returns></returns>
        public MonoProcess WaitForSecondsUnscaled(float duration)
        {
            if (!m_cancellationToken.IsCancellationRequested && !m_done)
            {
                if (!string.IsNullOrEmpty(debugId))
                {
                    Debug.Log($"Start WaitForSeconds for {debugId}");
                }

                m_stepQueue.Enqueue(new MonoProcessStep
                {
                    StepType = StepType.WaitForSecondUnscaled,
                    Action = null,
                    TimeRequired = duration,
                });
                return this;
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Continue the process until predicate is true. Checked once every frame
        /// </summary>
        /// <param name="predicate"></param>
        /// <returns></returns>
        public MonoProcess WaitUntil(Func<bool> predicate)
        {
            if (!m_cancellationToken.IsCancellationRequested && !m_done)
            {
                m_stepQueue.Enqueue(new MonoProcessStep
                {
                    StepType = StepType.WaitWhile,
                    Action = null,
                    Predicate = predicate,
                });
                return this;
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Continue the process until predicate is true or timeout is reached. Checked once every frame.
        /// For usage of timeout only return false from the predicate.
        /// </summary>
        /// <param name="predicate"></param>
        /// <returns></returns>
        public MonoProcess WaitFor(Func<bool> predicate, float timeout)
        {
            if (!m_cancellationToken.IsCancellationRequested && !m_done)
            {
                m_stepQueue.Enqueue(new MonoProcessStep
                {
                    StepType = StepType.WaitFor,
                    Action = null,
                    Predicate = () => predicate.Invoke() || m_exactTimeElapsed >= timeout,
                    TimeRequired = timeout,
                });
                return this;
            }
            else
            {
                return null;
            }
        }
        
        /// <summary>
        /// Continue the process until predicate is true or timeout is reached. Checked once every frame.
        /// For usage of timeout only return false from the predicate.
        /// </summary>
        /// <param name="predicate"></param>
        /// <returns></returns>
        public MonoProcess WaitFor(Func<float, bool> predicate, float timeout)
        {
            if (!m_cancellationToken.IsCancellationRequested && !m_done)
            {
                m_stepQueue.Enqueue(new MonoProcessStep
                {
                    StepType = StepType.WaitFor,
                    Action = null,
                    Predicate = () => predicate.Invoke(m_exactTimeElapsed) || m_exactTimeElapsed >= timeout,
                    TimeRequired = timeout,
                });
                return this;
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Continue the process until predicate is false. Checked once every frame
        /// </summary>
        /// <param name="predicate"></param>
        /// <returns></returns>
        public MonoProcess WaitWhile(Func<bool> predicate)
            => WaitUntil(() => !predicate.Invoke());

        /// <summary>
        /// Perform the designated action.
        /// </summary>
        /// <param name="action"></param>
        /// <returns></returns>
        public MonoProcess Do(Action action)
        {
            if (!m_cancellationToken.IsCancellationRequested && !m_done)
            {
                if (!string.IsNullOrEmpty(debugId))
                {
                    Debug.Log($"Added Do for {debugId}");
                }

                m_stepQueue.Enqueue(new MonoProcessStep
                {
                    StepType = StepType.Action,
                    Action = action,
                });

                return this;
            }
            else
            {
                return null;
            }
        }

        private bool m_ICompletableDone = false;

        public MonoProcess Do(ICompletable action)
        {
            if (!m_cancellationToken.IsCancellationRequested && !m_done)
            {
                m_ICompletableDone = false;
                Do(() =>
                {
                    action.Perform(() => { m_ICompletableDone = true; });
                    //action.RegisterForOnComplete(() => { m_ICompletableDone = true; });
                });

                WaitWhile(() => !m_ICompletableDone);

                return this;
            }
            else
            {
                return null;
            }
        }
        
        public MonoProcess Delay(int milliseconds)
        {
            if (!m_cancellationToken.IsCancellationRequested && !m_done)
            {
                m_ICompletableDone = false;
                Do(async () =>
                {
                    await Task.Delay(milliseconds, m_cancellationToken);
                    m_ICompletableDone = true;
                });

                WaitWhile(() => !m_ICompletableDone);

                return this;
            }
            else
            {
                return null;
            }
        }

        public MonoProcess Do(Action<Action> action)
        {
            if (!m_cancellationToken.IsCancellationRequested && !m_done)
            {
                m_ICompletableDone = false;
                if (m_debug)
                {
                    Debug.Log($"Start wait");
                }

                Do(() =>
                {
                    action.Invoke(() =>
                    {
                        if (m_debug)
                        {
                            Debug.Log($"End wait");
                        }

                        m_ICompletableDone = true;
                    });
                    //action.RegisterForOnComplete(() => { m_ICompletableDone = true; });
                });

                WaitUntil(() => m_ICompletableDone);

                return this;
            }
            else
            {
                return null;
            }
        }

        protected struct MonoProcessStep
        {
            public StepType StepType;
            public float TimeRequired;
            public Func<bool> Predicate;
            public UniTask Task;
            public Action Action;
        }

        public interface ICompletable
        {
            void Perform(Action onComplete);
            //void RegisterForOnComplete(Action onComplete);
        }

        public struct MonoTask : ICompletable
        {
            public Action<Action> taskAction;

            public void Perform(Action onComplete)
            {
                taskAction.Invoke(onComplete);
            }
        }

        public void Dispose()
        {
            if (m_disposed)
            {
                return;
            }
            
            m_disposed = true;

            if (m_debug)
            {
                Debug.Log($"Dispose {debugId}");
            }

            m_monoService.OnUpdate.RemoveListener(UpdateStep);
            m_monoService.OnFixedUpdate.RemoveListener(UpdateFixedStep);
            m_monoService.OnPerSecondUpdate.RemoveListener(UpdatePerSecondStep);
            m_monoService.OnUpdate.RemoveListener(UpdateStepExact);
            m_monoService.OnUpdate.RemoveListener(UpdateStepPredicate);

            m_stepQueue = null;
            m_predicate = null;
            m_monoService = null;
            m_done = true;
        }
    }
}