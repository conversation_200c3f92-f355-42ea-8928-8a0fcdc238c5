using System;
using UnityEngine;
using ZLinq;

namespace RibCageGames.Base
{
    using System.Linq;
    using System.Collections.Generic;
    using Random = UnityEngine.Random;

    public static class CollectionExtensions
    {
        public static void CopyToByValue<T>(this List<T> source, out List<T> target) where T : IConcreteCloneable<T>
        {
            List<T> list = new List<T>();
            foreach (T item in source)
            {
                list.Add(item.Copy());
            }

            target = list;
        }

        public static List<T> CopyFromByValue<T>(this List<T> source) where T : IConcreteCloneable<T>
        {
            List<T> target = new List<T>();
            foreach (T item in source)
            {
                target.Add(item.Copy());
            }

            return target;
        }

        public static IEnumerable<T> Shuffle<T>(this IEnumerable<T> source)
        {
            T[] elements = source.ToArray();
            for (int i = elements.Length - 1; i >= 0; i--)
            {
                int swapIndex = Random.Range(0, i + 1);
                yield return elements[swapIndex];
                elements[swapIndex] = elements[i];
            }
        }

        public static IEnumerable<T> TakeRandom<T>(this IEnumerable<T> source, int count)
        {
            count = Mathf.Min(count, source.Count());
            return source.Shuffle().Take(count);
        }

        public static IEnumerable<TSource> TakeRandom<TEnumerator, TSource>(
            this ValueEnumerable<TEnumerator, TSource> source, int count)
            where TEnumerator : struct, IValueEnumerator<TSource>
        {
            count = Mathf.Min(count, source.Count());
            return source.ToList().Shuffle().Take(count);
        }
    }

    public interface IConcreteCloneable<T>
    {
        T Copy();
    }
}