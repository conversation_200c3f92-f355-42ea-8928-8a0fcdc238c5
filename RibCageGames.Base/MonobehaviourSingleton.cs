namespace RibCageGames.Base
{
    using UnityEngine;

    public class MonobehaviourSingleton<T> : MonoBehaviour where T : MonoBehaviour
    {

        private static T m_instance;

        public static T Instance
        {
            get
            {
                if (m_instance == null)
                {
                    GameObject holder = new GameObject(typeof(T).ToString());
                    //DontDestroyOnLoad(holder);
                    m_instance = holder.AddComponent<T>();
                }

                return m_instance;
            }
        }

        protected virtual void Awake()
        {
            if (m_instance == null)
            {
                m_instance = this as T;
                DontDestroyOnLoad(this.gameObject);
            }
            else
            {
                Destroy(this.gameObject);
            }
        }
    }
}