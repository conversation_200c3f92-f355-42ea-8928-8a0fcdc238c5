{"name": "RibCageGames.VFX", "rootNamespace": "", "references": ["RibCageGames.Base", "RibCageGames.Editor", "RibCageGames.Animation", "RibCageGames.MonoUtils", "RibCageGames.Input", "Unity.VisualEffectGraph.Editor", "Unity.VisualEffectGraph.Runtime"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}