namespace RibCageGames.VFX
{
    using UnityEngine;
    using UnityEngine.VFX;
    using UnityEngine.VFX.Utility;

    [VFXBinder("Mesh/Basic")]
    public class VFXMeshBinder : VFXBinderBase
    {
        // VFXPropertyBinding attributes enables the use of a specific
        // property drawer that populates the VisualEffect properties of a
        // certain type.
        [VFXPropertyBinding("UnityEngine.Mesh")]
        public ExposedProperty meshProperty;

        [SerializeField] private Mesh m_mesh;

        public override bool IsValid(VisualEffect component)
        {
            return m_mesh != null && component.HasMesh(meshProperty);
        }

        public override void UpdateBinding(VisualEffect component)
        {
            component.SetMesh(meshProperty, m_mesh);
        }
    }
}
