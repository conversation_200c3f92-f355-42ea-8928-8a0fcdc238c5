namespace RibCageGames.ProceduralGeneration
{
    using MonoUtils;
    using System;
    using UnityEngine;

    /// <summary>
    /// A pool of combatEntities
    /// </summary>
    [CreateAssetMenu(fileName = "EnemyPrefabPoolAsset", menuName = "RibCageGames/Settings/EnemyPrefabPoolAsset")]
    public class EnvironmentPartPrefabPool : PrefabPoolAsset<EnvironmentPartReference>
    {
        public Type ConcreteType => Prefab.GetType();
    }
}