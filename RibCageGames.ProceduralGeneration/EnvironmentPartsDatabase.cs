namespace RibCageGames.ProceduralGeneration
{
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using RibCageGames.MonoUtils;
using UnityEngine;
using UnityEngine.Events;

    public class EnvironmentPartsDatabase : MonoBehaviour
    {
        private List<Type> m_visualTypes;
        
        private void OnValidate()
        {

            if (m_visualTypes == null)
            {
                m_visualTypes = EnvironmentPartReference.GetAllPartReferenceTypes();
            }
            
            //for (int i = 0; i < m_musicVisuals.Count; i++)
            //{
            //    EnvironmentPartReference visual = m_musicVisuals[i];
            //    if (visual != null && visual.TypeIndex >= 0 && visual.TypeIndex != m_visualTypes.IndexOf(visual.GetType()))
            //    {
            //        ChangeVisualType(i, visual.TypeIndex, m_visualTypes[visual.TypeIndex]);
            //    }
            //    else if(visual == null)
            //    {
            //        ChangeVisualType(i, -1, typeof(EnvironmentPartReference));
            //    }
            //}
        }
        
        //private void ChangeVisualType(int index, int typeIndex, Type visualType)
        //{
        //    if (m_musicVisuals.Count > index && m_musicVisuals[index] != null)
        //    {
        //        MusicVisualBase old = m_musicVisuals[index];
        //        m_musicVisuals[index] = Activator.CreateInstance(visualType) as MusicVisualBase;
        //        m_musicVisuals[index].TypeIndex = typeIndex;
        //        m_musicVisuals[index].Validate(old, old.Target ? old.Target : gameObject);
        //    }
        //    else
        //    {
        //        m_musicVisuals[index] = Activator.CreateInstance(visualType) as MusicVisualBase;
        //        m_musicVisuals[index].TypeIndex = typeIndex;
        //        m_musicVisuals[index].Validate(null, gameObject);
        //    }
        //}
        
        private class EnvironmentPartPoolReference
        {
            [SerializeField] private EnvironmentPartPrefabPool m_pool;

            private EnvironmentPartPrefabPool Pool => m_pool;
        }
    }
    
    public enum EnvironmentPartType
    {
        Floor,
        Wall,
        Door,
        Prop,
        Decoration,
        SmallEnemySpawn,
        LargeEnemySpawn,
    }

    public abstract class EnvironmentPartReference : MonoBehaviour, IPoolable
    {
        //Used to allow type selection in the editor
        [HideInInspector] [SerializeField] private int m_concreteTypeIndex = -1;
        
        public int TypeIndex
        {
            get => m_concreteTypeIndex;
            set => m_concreteTypeIndex = value;
        }
        
        public static List<Type> GetAllPartReferenceTypes()
        {
            return AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(assembly => assembly.GetTypes())
                .Where(type => type.IsSubclassOf(typeof(EnvironmentPartReference))).ToList();
        }

        public void ActivatePoolable() {}

        public void DeactivatePoolable() {}

        public void ResetPoolable(bool resetMovement = true) {}

        public void Initialize() {}
        
        public UnityEvent OnDisable { get; }
    }
}