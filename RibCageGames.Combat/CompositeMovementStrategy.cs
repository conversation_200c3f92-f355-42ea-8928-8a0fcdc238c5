using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;

namespace RibCageGames.Combat
{
    [Serializable]
    public class CompositeMovementStrategy
    {
        [SerializeReference] private DeltaApplicationStrategy m_deltaStrategy;
        [SerializeReference] private SmoothingStrategy m_lerpStrategy;
        [SerializeReference] private StabilizationStrategy m_stabilizationStrategy;
        
        public DeltaApplicationStrategy DeltaStrategy => m_deltaStrategy;
        public SmoothingStrategy LerpStrategy => m_lerpStrategy;
        public StabilizationStrategy StabilizationStrategyInstance => m_stabilizationStrategy;
        
        [NonSerialized] private Vector3 m_lastFrameEventual = Vector3.zero;
        [NonSerialized] private Vector3 m_eventualPosition = Vector3.zero;
        [NonSerialized] private Vector3 m_lerpPosition = Vector3.zero;

        public virtual bool Grounded { get => DeltaStrategy.Grounded; set => DeltaStrategy.Grounded = value; }
        
        internal Vector3 LastFrameEventual
        {
            get => m_lastFrameEventual;
            set => m_lastFrameEventual = value;
        }

        internal Vector3 EventualPosition
        {
            get => m_eventualPosition;
            set => m_eventualPosition = value;
        }

        internal Vector3 LerpPosition
        {
            get => m_lerpPosition;
            set => m_lerpPosition = value;
        }

        public void Initialize(CombatEntity owner)
        {
            m_eventualPosition = owner.transform.position;
            m_lerpPosition = owner.transform.position;
            m_lastFrameEventual = owner.transform.position;
            
            m_deltaStrategy.Initialize(owner, this);
            m_lerpStrategy.Initialize(owner, this);
            m_stabilizationStrategy.Initialize(owner, this);
        }
        
        public void OnUpdate(float delta)
        {
            m_deltaStrategy.OnUpdate(delta);
            m_lerpStrategy.OnUpdate(delta);
            m_stabilizationStrategy.OnUpdate(delta);
        }

        public void OnFixedUpdate(float delta)
        {
            m_deltaStrategy.OnFixedUpdate(delta);
            m_lerpStrategy.OnFixedUpdate(delta);
            m_stabilizationStrategy.OnFixedUpdate(delta);
        }

        #region Editor type population

        [NonSerialized] private List<Type> m_deltaTypes;
        [NonSerialized] private List<Type> m_lerpTypes;
        [NonSerialized] private List<Type> m_stabilizationTypes;

        public void OnValidate()
        {
            if(Application.isPlaying) { return; }
            
            if (m_deltaTypes == null)
            {
                m_deltaTypes = GetAllDerivedTypes<DeltaApplicationStrategy>();
            }
            if (m_lerpTypes == null)
            {
                m_lerpTypes = GetAllDerivedTypes<SmoothingStrategy>();
            }
            if (m_stabilizationTypes == null)
            {
                m_stabilizationTypes = GetAllDerivedTypes<StabilizationStrategy>();
            }

            if (m_deltaStrategy != null && m_deltaStrategy.TypeIndex >= 0 &&
                m_deltaStrategy.TypeIndex != m_deltaTypes.IndexOf(m_deltaStrategy.GetType()))
            {
                m_deltaStrategy = Activator.CreateInstance(m_deltaTypes[m_deltaStrategy.TypeIndex]) as DeltaApplicationStrategy;
            }
            else if (m_deltaStrategy == null)
            {
                m_deltaStrategy = Activator.CreateInstance(m_deltaTypes[0]) as DeltaApplicationStrategy;
            }
            
            if (m_lerpStrategy != null && m_lerpStrategy.TypeIndex >= 0 &&
                m_lerpStrategy.TypeIndex != m_lerpTypes.IndexOf(m_lerpStrategy.GetType()))
            {
                m_lerpStrategy = Activator.CreateInstance(m_lerpTypes[m_lerpStrategy.TypeIndex]) as SmoothingStrategy;
            }
            else if (m_lerpStrategy == null)
            {
                m_lerpStrategy = Activator.CreateInstance(m_lerpTypes[0]) as SmoothingStrategy;
            }
            
            if (m_stabilizationStrategy != null && m_stabilizationStrategy.TypeIndex >= 0 &&
                m_stabilizationStrategy.TypeIndex != m_stabilizationTypes.IndexOf(m_stabilizationStrategy.GetType()))
            {
                m_stabilizationStrategy = Activator.CreateInstance(m_stabilizationTypes[m_stabilizationStrategy.TypeIndex]) as StabilizationStrategy;
            }
            else if (m_stabilizationStrategy == null)
            {
                m_stabilizationStrategy = Activator.CreateInstance(m_stabilizationTypes[0]) as StabilizationStrategy;
            }
        }

        public static List<Type> GetAllDerivedTypes<T>()
        {
            return AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(assembly => assembly.GetTypes())
                .Where(type => type.IsSubclassOf(typeof(T))).ToList();
        }

        #endregion


        [Serializable]
        public abstract class DeltaApplicationStrategy
        {
            [HideInInspector] [SerializeField] private int m_concreteTypeIndex = -1;
            protected CombatEntity m_owner;
            protected CompositeMovementStrategy m_compositeMovementStrategy;

            public virtual UnityEvent<Collider> OnCollisionDetected { get; } = new UnityEvent<Collider>();

            public virtual bool Grounded { get; protected internal set; }
            
            protected Vector3 LastFrameEventual
            {
                get => m_compositeMovementStrategy.LastFrameEventual;
                set => m_compositeMovementStrategy.LastFrameEventual = value;
            }

            protected Vector3 EventualPosition
            {
                get => m_compositeMovementStrategy.EventualPosition;
                set => m_compositeMovementStrategy.EventualPosition = value;
            }

            protected Vector3 LerpPosition
            {
                get => m_compositeMovementStrategy.LerpPosition;
                set => m_compositeMovementStrategy.LerpPosition = value;
            }

            public int TypeIndex
            {
                get => m_concreteTypeIndex;
                set => m_concreteTypeIndex = value;
            }

            public virtual void Activate(){}
            public virtual void Deactivate(){}
            public virtual void Reset(){}
            public virtual void OnDestroy(){}
            public abstract void OnUpdate(float delta);
            public abstract void OnFixedUpdate(float delta);

            public void Initialize(CombatEntity owner, CompositeMovementStrategy compositeMovementStrategy)
            {
                m_owner = owner;
                m_compositeMovementStrategy = compositeMovementStrategy;
            }
        }

        [Serializable]
        public abstract class SmoothingStrategy
        {
            [HideInInspector] [SerializeField] private int m_concreteTypeIndex = -1;
            protected CombatEntity m_owner;
            protected CompositeMovementStrategy m_compositeMovementStrategy;
            
            protected Vector3 LastFrameEventual
            {
                get => m_compositeMovementStrategy.LastFrameEventual;
                set => m_compositeMovementStrategy.LastFrameEventual = value;
            }

            protected Vector3 EventualPosition
            {
                get => m_compositeMovementStrategy.EventualPosition;
                set => m_compositeMovementStrategy.EventualPosition = value;
            }

            protected Vector3 LerpPosition
            {
                get => m_compositeMovementStrategy.LerpPosition;
                set => m_compositeMovementStrategy.LerpPosition = value;
            }
            
            public int TypeIndex
            {
                get => m_concreteTypeIndex;
                set => m_concreteTypeIndex = value;
            }
            
            public abstract void OnUpdate(float delta);
            public abstract void OnFixedUpdate(float delta);
            
            public void Initialize(CombatEntity owner, CompositeMovementStrategy compositeMovementStrategy)
            {
                m_owner = owner;
                m_compositeMovementStrategy = compositeMovementStrategy;
            }
        }

        [Serializable]
        public abstract class StabilizationStrategy
        {
            [HideInInspector] [SerializeField] private int m_concreteTypeIndex = -1;
            [HideInInspector] [SerializeField] protected CombatEntity m_owner;
            [HideInInspector] [SerializeField] protected CompositeMovementStrategy m_compositeMovementStrategy;
            
            protected Vector3 LastFrameEventual
            {
                get => m_compositeMovementStrategy.LastFrameEventual;
                set => m_compositeMovementStrategy.LastFrameEventual = value;
            }

            protected Vector3 EventualPosition
            {
                get => m_compositeMovementStrategy.EventualPosition;
                set => m_compositeMovementStrategy.EventualPosition = value;
            }

            protected Vector3 LerpPosition
            {
                get => m_compositeMovementStrategy.LerpPosition;
                set => m_compositeMovementStrategy.LerpPosition = value;
            }
            
            public int TypeIndex
            {
                get => m_concreteTypeIndex;
                set => m_concreteTypeIndex = value;
            }
            
            public abstract void OnUpdate(float delta);
            public abstract void OnFixedUpdate(float delta);
            
            public void Initialize(CombatEntity owner, CompositeMovementStrategy compositeMovementStrategy)
            {
                m_owner = owner;
                m_compositeMovementStrategy = compositeMovementStrategy;
            }
        }

        public void Activate()
        {
            m_deltaStrategy.Activate();
        }
        
        public void Deactivate()
        {
            m_deltaStrategy.Deactivate();
        }
        
        public void Reset()
        {
            m_deltaStrategy.Reset();
        }
        
        public void OnDestroy()
        {
            m_deltaStrategy.OnDestroy();
        }
    }
}