namespace RibCageGames.Editor
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using UnityEngine;

    public class ComponentReferenceAccessorAttribute : PropertyAttribute
    {
        public string CollectionPath;
        public string VariableName;
        public Type ReferenceType;

        public ComponentReferenceAccessorAttribute(string collectionPath, string variableName, Type referenceType)
        {
            CollectionPath = collectionPath;
            VariableName = variableName;
            ReferenceType = referenceType;
        }
    }
}