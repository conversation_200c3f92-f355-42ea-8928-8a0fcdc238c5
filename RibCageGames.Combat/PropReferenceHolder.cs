using System.Collections;
using System.Collections.Generic;
using RibCageGames.MonoUtils;
using UnityEngine;
using UnityEngine.Events;

public class PropReferenceHolder : MonoBehaviour, IPoolable
{
    public void ActivatePoolable()
    {
        gameObject.SetActive(true);
    }

    public void DeactivatePoolable()
    {
        gameObject.SetActive(false);
    }

    public void ResetPoolable(bool resetMovement = true)
    {
    }

    public void Initialize()
    {
    }

    public UnityEvent OnDisable { get; } = new();
}
