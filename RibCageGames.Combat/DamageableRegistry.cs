using Cysharp.Threading.Tasks;
using RibCageGames.MonoUtils;
using UnityEngine.Events;

namespace RibCageGames.Combat
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Linq;
    using RibCageGames.Base;
    using UnityEngine;
    
    [CreateAssetMenu(fileName = "DamageableRegistry", menuName = "RibCageGames/Services/DamageableRegistry")]
    public class DamageableRegistry : BaseService
    {
        [SerializeField] private float m_freezeFrameDuration;
        
        [Range(0f, 1f)]
        [SerializeField] private float m_freezeFrameSpeed;
        
        public UnityEvent<float, IDamageable, bool> OnDamageToEntity = new UnityEvent<float, IDamageable, bool>();
        private Dictionary<ColliderReference, IDamageable> m_damageableDictionary;
        private Dictionary<Collider, IDamageable> m_collidableDictionary;
        private Dictionary<ColliderReference, int> m_gridPositionMap;
        private MultiHashMap<int, ColliderReference> m_gridColliderMap;
        private bool m_loopLock = false;

        public override void Init(GameObject servicePrefab = null)
        {
            m_loopLock = false;
            m_damageableDictionary = new Dictionary<ColliderReference, IDamageable>();
            m_gridPositionMap = new Dictionary<ColliderReference, int>();
            m_gridColliderMap = new MultiHashMap<int, ColliderReference>();
            m_collidableDictionary = new Dictionary<Collider, IDamageable>();
        }

        public override void StartService(MonoInjector injector)
        {
            ServiceLocator.Get<MonoService>().OnUpdate.AddListener(ControlledUpdate);
        }

        public bool TryGetValue(Collider key, out IDamageable damageable)
        {
            return m_collidableDictionary.TryGetValue(key, out damageable);
        }

        private void ControlledUpdate(float deltaTime)
        {
            ColliderReference[] keys = m_gridPositionMap.Keys.ToArray();

            for (int i = 0; i < keys.Length; i++)
            {
                ColliderReference collider = keys[i];
                if (collider == null || collider.Transform == null)
                {
                    UnRegisterDamageable(collider); //TODO: understand why this is necessary
                    continue;
                }
                
                int currentHash = HashPosition(collider.Transform.position);
                if (m_gridPositionMap[collider] != currentHash)
                {
                    m_gridColliderMap.Remove(m_gridPositionMap[collider], collider);
                    m_gridPositionMap[collider] = currentHash;
                    m_gridColliderMap.Add(currentHash, collider);
                }
            }
        }
        
        public async void ApplyDamageAreaUnrestricted(ColliderReference hitbox, float damage, bool directHit, CombatEntity owner, ActionData.ActionHitBox hitData, Action<List<IDamageable>> onHitBox = null)
        {
            await UniTask.WaitForFixedUpdate();
            MonoProcess.NewManual()
                //.WaitForFixedFrame()
                .Do(() =>
                {
                    if (m_loopLock)
                    {
                        MonoProcess.NextFrame.Do(() =>
                        {
                            DamageAreaUnrestricted(hitbox, damage, directHit, owner, hitData, onHitBox);
                        });
                    }
                    else
                    {
                        DamageAreaUnrestricted(hitbox, damage, directHit, owner, hitData, onHitBox);   
                    }
                })
                .Run();
        }

        public async void ApplyDamageArea(ColliderReference hitbox, float damage, bool directHit, CombatEntity owner, ActionData.ActionHitBox hitData, Action<List<(IDamageable damageable, float damage)>> onHitBox = null)
        {
            await UniTask.WaitForFixedUpdate();
            MonoProcess.NewManual()
                //.WaitForFixedFrame()
                .Do(() =>
                {
                    if (m_loopLock)
                    {
                        MonoProcess.NextFrame.Do(() =>
                        {
                            DamageArea(hitbox, damage, directHit, owner, hitData, onHitBox);
                        });
                    }
                    else
                    {
                        DamageArea(hitbox, damage, directHit, owner, hitData, onHitBox);   
                    }
                })
                .Run();
        }

        private int DamageArea(ColliderReference hitbox, float damage, bool directHit, CombatEntity owner, ActionData.ActionHitBox hitData, Action<List<(IDamageable damageable, float damage)>> onHitBox)
        {
            int hitAreaHash = HashPosition(hitbox.bounds.center);
            int damageablesHitCount = 0;
            List<(IDamageable, float)> damageablesHit = null;

            if (onHitBox != null)
            {
                damageablesHit = new List<(IDamageable, float)>();
            }

            m_loopLock = true;
            for (int i = 0; i < 27; i++)
            {
                int currentCheckedHash = hitAreaHash + gridOffsetTable[i];
                foreach (ColliderReference collider in m_gridColliderMap.GetValues(currentCheckedHash))
                {
                    if (!ReferenceEquals(m_damageableDictionary[collider], owner))
                    {
                        //&& hitbox.bounds.Intersects(collider.bounds)) {
                        //This check uses AABB, should improve and take cilinders into account 
                        
                        if (((hitbox.Type == ColliderShape.Capsule &&
                              MathUtils.CapsuleCapsuleIntersect(hitbox.Capsule, collider.Capsule))
                             || (hitbox.Type == ColliderShape.Box &&
                                 MathUtils.BoxCapsuleIntersect(hitbox.Box, collider.Capsule))))
                        {
                            damageablesHitCount++;
                            float damageDone = m_damageableDictionary[collider]
                                .TakeDamage(damage, owner, hitData, out _);

                            if (owner && damageDone > 50) //Freeze frame
                            {
                                //TODO: move freeze frame to enemy
                                m_damageableDictionary[collider].Animator.speed = m_freezeFrameSpeed;
                                if (owner.Animator != null)
                                {
                                    owner.Animator.speed = m_freezeFrameSpeed;
                                }

                                MonoProcess.WaitForSecondsProcess(m_freezeFrameDuration)
                                    .Do(() =>
                                    {
                                        if (m_damageableDictionary.TryGetValue(collider, out IDamageable value))
                                        {
                                            value.Animator.speed = 1f;
                                        }

                                        if (owner.Animator != null)
                                        {
                                            owner.Animator.speed = 1f;
                                        }
                                    });
                            }

                            OnDamageToEntity?.Invoke(damageDone, m_damageableDictionary[collider], directHit);

                            if (damageDone > 0)
                            {
                                if (onHitBox != null)
                                {
                                    damageablesHit.Add((m_damageableDictionary[collider], damageDone));
                                }
                            }
                        }
                    }
                }
            }

            m_loopLock = false;
            onHitBox?.Invoke(damageablesHit);
            return damageablesHitCount;
        }
        
        private int DamageAreaUnrestricted(ColliderReference hitbox, float damage, bool directHit, CombatEntity owner, ActionData.ActionHitBox hitData, Action<List<IDamageable>> onHitBox)
        {
            if (!hitbox.Valid)
            {
                return 0;
            }
            
            var bounds = hitbox.bounds;
            int hitAreaHash = HashPosition(bounds.center);
            int damageablesHitCount = 0;
            
            List<IDamageable> damageablesHit = null;

            if (onHitBox != null)
            {
                damageablesHit = new List<IDamageable>();
            }

            m_loopLock = true;
            foreach (int index in m_gridColliderMap.Keys)
            {
                foreach (ColliderReference collider in m_gridColliderMap.GetValues(index))
                {
                    if (!ReferenceEquals(m_damageableDictionary[collider], owner) &&
                        hitbox.bounds.Intersects(collider.bounds))
                    {
                        if (!((hitbox.Type == ColliderShape.Capsule &&
                             MathUtils.CapsuleCapsuleIntersect(hitbox.Capsule, collider.Capsule))
                            || MathUtils.BoxCapsuleIntersect(hitbox.Box, collider.Capsule)))
                        {
                            continue;
                        }
                        damageablesHitCount++;
                        float damageDone = m_damageableDictionary[collider].TakeDamage(damage, owner, hitData, out _);
                        
                        if (owner && damageDone > 50) //Freeze frame
                        {
                            m_damageableDictionary[collider].Animator.speed = m_freezeFrameSpeed;
                            if (owner.Animator != null)
                            {
                                owner.Animator.speed = m_freezeFrameSpeed;
                            }
                            MonoProcess.WaitForSecondsProcess(m_freezeFrameDuration)
                                .Do(() =>
                                {
                                    if (m_damageableDictionary.TryGetValue(collider, out IDamageable value))
                                    {
                                        value.Animator.speed = 1f;
                                    }

                                    if (owner.Animator != null)
                                    {
                                        owner.Animator.speed = 1f;
                                    }
                                });
                        }

                        OnDamageToEntity?.Invoke(damageDone, m_damageableDictionary[collider], directHit);
                        
                        if (damageDone > 0)
                        {
                            if (onHitBox != null)
                            {
                                damageablesHit.Add(m_damageableDictionary[collider]);
                            }
                        }
                    }
                }
            }

            m_loopLock = false;
            onHitBox?.Invoke(damageablesHit);
            return damageablesHitCount;
        }


        private void LockCheckAction(Action action)
        {
            if (m_loopLock)
            {
                MonoProcess.NextFrame.Do(() => LockCheckAction(action));
            }
            else
            {
                action();
            }
        }

        public void RegisterDamageable(ColliderReference collider, IDamageable damageable)
        {
            LockCheckAction(() => { RegisterDamageableInternal(collider, damageable); });
        }

        private void RegisterDamageableInternal(ColliderReference collider, IDamageable damageable)
        {
            Vector3 position = collider.Transform.position;
            
            if (m_damageableDictionary.ContainsKey(collider))
            {
                m_damageableDictionary[collider] = damageable;
                m_collidableDictionary[collider.Collider] = damageable;
                m_gridPositionMap[collider] = HashPosition(position);
                m_gridColliderMap.Remove(m_gridPositionMap[collider], collider);
                m_gridColliderMap.Add(HashPosition(position), collider);
            }
            else
            {
                m_damageableDictionary.Add(collider, damageable);
                m_collidableDictionary.Add(collider.Collider, damageable);
                m_gridPositionMap.Add(collider, HashPosition(position));
                m_gridColliderMap.Add(HashPosition(position), collider);
            }
        }
        
        public void UnRegisterDamageable(ColliderReference collider)
        {
            LockCheckAction(() => { UnRegisterDamageableInternal(collider); });
        }

        private void UnRegisterDamageableInternal(ColliderReference collider)
        {
                m_damageableDictionary.Remove(collider);
                m_collidableDictionary.Remove(collider.Collider);
                m_gridPositionMap.Remove(collider);
                //TODO: Temp fix. Fix properly
                foreach (KeyValuePair<int,HashSet<ColliderReference>> pair in m_gridColliderMap)
                {
                    foreach (ColliderReference collider1 in pair.Value)
                    {
                        if (collider1 == collider)
                        {
                            pair.Value.Remove(collider);
                            break;
                        }
                    }
                }
        }

        #region Debug

        [SerializeField] private List<CombatEntity> m_entities;
        [SerializeField] private List<ColliderReference> m_colliders;

        [ContextMenu("PopulateDebugArrays")]
        public void PopulateDebugArrays()
        {
            m_colliders = m_damageableDictionary.Keys.ToList();
            m_entities = m_damageableDictionary.Values.Select(value => (CombatEntity) value).ToList();
        }

        #endregion

        public interface IDamageable
        {
            float TakeDamage(float damage, CombatEntity owner, ActionData.ActionHitBox hitData, out bool flinch);
            Transform ConnectedTransform { get; }

            bool IsPlayer { get; } //TODO: remove. is temporary

            Animator Animator { get; }
        }

        private const int k_gridHashFactor = 1000;
        private const int k_gridSize = 5;

        private static readonly int[] gridOffsetTable =
        {
            -1 - k_gridHashFactor - (k_gridHashFactor * k_gridHashFactor), //---
            0 - k_gridHashFactor - (k_gridHashFactor * k_gridHashFactor), //0--
            1 - k_gridHashFactor - (k_gridHashFactor * k_gridHashFactor), //+--
            -1 - (k_gridHashFactor * k_gridHashFactor), //-0-
            0 - (k_gridHashFactor * k_gridHashFactor), //00-
            1 - (k_gridHashFactor * k_gridHashFactor), //+0-
            -1 + k_gridHashFactor - (k_gridHashFactor * k_gridHashFactor), //-+-
            0 + k_gridHashFactor - (k_gridHashFactor * k_gridHashFactor), //0+-
            1 + k_gridHashFactor - (k_gridHashFactor * k_gridHashFactor), //++-
            -1 - k_gridHashFactor, //--0
            0 - k_gridHashFactor, //0-0
            1 - k_gridHashFactor, //+-0
            -1, //-00
            0, //000
            1, //+00
            -1 + k_gridHashFactor, //-+0
            0 + k_gridHashFactor, //0+0
            1 + k_gridHashFactor, //++0
            -1 - k_gridHashFactor + (k_gridHashFactor * k_gridHashFactor), //--+
            0 - k_gridHashFactor + (k_gridHashFactor * k_gridHashFactor), //0-+
            1 - k_gridHashFactor + (k_gridHashFactor * k_gridHashFactor), //+-+
            -1 + (k_gridHashFactor * k_gridHashFactor), //-0+
            0 + (k_gridHashFactor * k_gridHashFactor), //00+
            1 + (k_gridHashFactor * k_gridHashFactor), //+0+
            -1 + k_gridHashFactor + (k_gridHashFactor * k_gridHashFactor), //-++
            0 + k_gridHashFactor + (k_gridHashFactor * k_gridHashFactor), //0++
            1 + k_gridHashFactor + (k_gridHashFactor * k_gridHashFactor), //+++
        };

        private int HashPosition(Vector3 position)
        {
            int hash = (Mathf.FloorToInt(position.x) / k_gridSize);
            hash += (Mathf.FloorToInt(position.y) / k_gridSize) * k_gridHashFactor;
            hash += (Mathf.FloorToInt(position.z) / k_gridSize) * k_gridHashFactor * k_gridHashFactor;

            return hash;
        }

        public override void Dispose()
        {
        }

        //TODO: use instead of simple float numbers for damage
        /*public class AttackData {
            private float m_baseDamage;
            private Func<float, float, float> m_damageResolver;
            private float m_damageDealt;
    
            public float DamageDealt => m_damageDealt;
    
            public AttackData(float baseDamage, Func<float, float, float> damageResolver) {
                m_baseDamage = baseDamage;
                m_damageResolver = damageResolver;
                m_damageDealt = 0f;
            }
    
            public float CalculateDamageDone(float defense) {
                float damage = m_damageResolver(m_baseDamage, defense);
                m_damageDealt += damage;
                return damage;
            }
        }*/
    }

    public class ColliderReference
    {
        private ColliderShape m_type;
        private BoxCollider m_box;
        private CapsuleCollider m_capsule;
        private Transform m_transform;
        
        public bool Valid => (m_type == ColliderShape.Box && m_box != null) || m_type == ColliderShape.Capsule && m_capsule != null;
        public Transform Transform => m_transform;
        public ColliderShape Type => m_type;
        public Bounds bounds => m_type == ColliderShape.Box ? m_box.bounds : m_capsule.bounds;
        public Collider Collider => m_type == ColliderShape.Box ? (Collider) m_box : m_capsule;
        public BoxCollider Box => m_box;
        public CapsuleCollider Capsule => m_capsule;

        public static ColliderReference Create(Collider collider)
        {
            if (collider is CapsuleCollider capsuleCollider)
            {
                return new ColliderReference(capsuleCollider);
            }
            else
            {
                return new ColliderReference(collider as BoxCollider);
            }
        }
        
        public ColliderReference(BoxCollider box)
        {
            this.m_box = box;
            m_capsule = null;
            m_type = ColliderShape.Box;
            m_transform = box.transform;
        }
        
        public ColliderReference(CapsuleCollider capsule)
        {
            m_box = null;
            this.m_capsule = capsule;
            m_type = ColliderShape.Capsule;
            m_transform = capsule.transform;
        }
    }

    public enum ColliderShape
    {
        Capsule = 0,
        Box = 1,
    }
}