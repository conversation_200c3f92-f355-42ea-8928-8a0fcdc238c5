using System.Threading;

namespace RibCageGames.Combat
{
    using Animation;
    using Base;
    using MonoUtils;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using UnityEngine;
    using UnityEngine.Events;
    using Input;
    using UnityEngine.Serialization;
    using DG.Tweening;
    using Cysharp.Threading.Tasks;

    public abstract class CombatEntity : MonoBehaviour, DamageableRegistry.IDamageable,
        ISceneReferenceCollection<Collider>, ISceneReferenceCollection<GameObject>, IPoolable
    {
        [Header("Basic settings")]
        [SerializeField] protected int m_maxHealth;
        [SerializeField] protected float m_baseDamage;
        [SerializeField] protected int m_flinchThreshold = 5;
        
        [SerializeField] protected float m_flinchResetDelay;
        [SerializeField] protected float m_flinchRestorationDelay;
        [SerializeField] protected float m_flinchRestorationRate = 1f;

        [Header("References")]
        [Space(10)]
        [Header("Combat Entity")]
        
        [SerializeField]
        protected CapsuleCollider m_hurtBoxCollider;
        
        [NonSerialized] protected ColliderReference m_hurtBox;
        [SerializeField] protected ContinuousOverlapHandler m_floorCheck;
        [SerializeField] protected ContinuousOverlapHandler m_remoteFloorCheck;
        
        public ContinuousOverlapHandler FloorCheck => m_floorCheck;
        [SerializeField] protected Animator m_animator;
        [SerializeField] protected List<ActionData> m_combatActions;
        
        //TODO: use collider data but not actual colliders
        [FormerlySerializedAs("ColliderReferences")] [SerializeField] private ColliderReferenceCollection m_colliderReferences;
        [FormerlySerializedAs("ParticleSystemReferences")] [SerializeField] private EffectParentReferenceCollection m_particleSystemReferences;
        
        [Header("Settings")]
        [SerializeField] protected float m_animatorRootMotionTransfer = 1f;
        [SerializeField] private Vector3 m_lockOnOffset;
        [SerializeField] private bool m_keepGraphicRotation = true;
        [SerializeField] private bool m_groundCheckRaycast = false;
        [SerializeField] private CompositeMovementStrategy m_compositeMovementStrategy;
        
        [Header("Events")]
        [SerializeField] private UnityEvent OnHit;
        
        protected virtual float FlinchThreshold => m_flinchThreshold;
        
        protected CompositeMovementStrategy CompositeMovementStrategy => m_compositeMovementStrategy;
        
        public float HealthPercentage => (float) CurrentHealth / m_maxHealth;

        public bool Busy => m_busy;

        [NonSerialized] protected float m_originalRootMotionTransfer;
        [NonSerialized] protected bool m_busy = false;
        [NonSerialized] protected Action m_currentMoveCancel;
        [NonSerialized] protected bool m_invulnerable = false;
        [NonSerialized] protected bool m_dead = false;
        [NonSerialized] protected int m_currentHealth;
        [NonSerialized] protected MonoService m_monoService;
        [NonSerialized] protected DamageableRegistry m_damageableRegistry;
        [NonSerialized] protected ActionData m_currentActionData;
        [NonSerialized] private Quaternion m_initialRotation;
        [NonSerialized] private List<VisualEffectHolder> m_effectholders = new();
        [NonSerialized] private List<ColliderReference> m_hitboxColliders = new();
        [NonSerialized] private int m_remoteGroundingObjects;
        [NonSerialized] private bool m_remoteGrounded = true;
        [NonSerialized] protected bool m_initialized = false;
        [NonSerialized] protected Vector3 m_moveTweenMotionModifier = Vector3.one;
        [NonSerialized] protected Vector3 m_animationMotionModifier = Vector3.one;
        [NonSerialized] protected float m_originalAnimatorSpeed;
        [NonSerialized] protected Sequence m_hitTween;
        [NonSerialized] protected MonoProcess m_hitProcess;
        [NonSerialized] protected MonoProcess m_staggerBrokenProcess;
        [NonSerialized] private Vector3 m_groundNormal;
        [NonSerialized] private float m_groundDistance;
        
        protected virtual float CurrentFlinchAmount { set; get; }

        public UnityEvent OnDisable { get; private set; } = new();
        public UnityEvent<CombatEntity> OnDead { get; } = new();
        public OnAnimationEventHandler AnimationTriggerEvent { get; } = new();
        public UnityEvent<ActionData, List<(DamageableRegistry.IDamageable, float)>> OnAttackLanded { get; } = new();
        public UnityEvent<ActionData, PlayerInputActions> OnMoveStarted { get; } = new();        
        public UnityEvent<ActionData> OnMoveEnded { get; } = new();
        public UnityEvent<ActionData> OnMoveCancelled { get; } = new();
        public Action CurrentMoveCancel => m_currentMoveCancel;
        public UnityEvent<float> OnDamageTaken { get; } = new();
        public UnityEvent<float, CombatEntity, ActionData.ActionHitBox> OnHitReceived { get; } = new();
        public List<ActionData> CombatActions => m_combatActions;
        public List<VisualEffectHolder> Effectholders => m_effectholders;
        public Animator Animator => m_animator;
        public virtual Vector3 OwnForward => m_animator.transform.forward;
        public bool IsDead => m_dead;
        public Vector3 LockOnOffset => m_lockOnOffset;
        public float RootMotionModifier
        {
            set => m_animatorRootMotionTransfer = value;
        }
        public Vector3 MoveTweenMotionModifier
        {
            set => m_moveTweenMotionModifier = value;
        }
        
        public Vector3 AnimationMotionModifier
        {
            set => m_animationMotionModifier = value;
        }

        //TODO: handle in movement strategy
        private bool m_grounded = true;
        
        public bool Grounded
        {
            get =>
                m_compositeMovementStrategy.Grounded;
            protected set => m_compositeMovementStrategy.Grounded = value;
        }
        public bool RemoteGrounded => m_remoteGrounded;
        public Vector3 GroundNormal => m_groundNormal;
        public float GroundDistance
        {
            get => m_groundDistance;
            set => m_groundDistance = value;
        }
        public bool InAir => !Grounded;
        
        public Transform ConnectedTransform => transform;
        public abstract bool IsPlayer { get; }
        public virtual float BaseDamage
        {
            get => m_baseDamageMultiplier * m_baseDamage;
            set => m_baseDamage = value;
        }

        [NonSerialized] protected float m_baseDamageMultiplier = 1f;

        public virtual int CurrentHealth
        {
            get => m_currentHealth;
            set => m_currentHealth = value;
        }

        public virtual bool IsStunned { get; protected set; }

        #region Mono

        public virtual void Initialize()
        {
            if (m_initialized) { return; }
            m_initialized = true;
            
            m_monoService = ServiceLocator.Get<MonoService>();
            m_damageableRegistry = ServiceLocator.Get<DamageableRegistry>();
            m_hurtBox = new ColliderReference(m_hurtBoxCollider);
            m_originalAnimatorSpeed = m_animator.speed;
            m_originalRootMotionTransfer = m_animatorRootMotionTransfer;
            m_initialRotation = m_animator.gameObject.transform.localRotation;

            foreach (OnStartDelayedAnimationEvent delayedStartBehaviour in m_animator
                .GetBehaviours<OnStartDelayedAnimationEvent>())
            {
                delayedStartBehaviour.AnimationDelayedStart.AddListener(OnAnimationEvent);
            }

            foreach (GameObject effectParent in m_particleSystemReferences.GetItems())
            {
                VisualEffectHolder holder = null;
                try
                {
                    holder = new VisualEffectHolder(effectParent);
                }
                catch (Exception exception)
                {
                    Debug.LogError($"Broken effect on object {gameObject.name}");
                    return;
                }

                m_effectholders.Add(holder);
            }

            foreach (Collider col in m_colliderReferences.GetItems())
            {
                m_hitboxColliders.Add(ColliderReference.Create(col));
            }
            
            m_compositeMovementStrategy?.Initialize(this);
        }

        protected virtual void Start()
        {
            Initialize();
        }

        public void SetBaseDamage(float value)
        {
            m_baseDamage = value;
        }

        public virtual async void RepositionEntity(Vector3 newPosition, Quaternion rotation)
        {
            transform.position = newPosition;
            transform.rotation = rotation;

            m_compositeMovementStrategy.Reset();
            
            m_floorCheck.ResetOverlap();
            m_remoteFloorCheck.ResetOverlap();
            await UniTask.WaitForFixedUpdate();
            GroundCheck(0f);
        }

        protected virtual void OnDestroy()
        {
            m_damageableRegistry?.UnRegisterDamageable(m_hurtBox);
            m_compositeMovementStrategy.OnDestroy();
        }

        #endregion

        #region InjectedMono

        protected virtual void ControlledUpdate(float deltaTime) {}
        
        protected virtual void ControlledLateUpdate(float deltaTime)
        {
            if (m_keepGraphicRotation)
            {
                m_animator.gameObject.transform.localRotation = m_initialRotation;
            }
        }
        
        private void ControlledFixedUpdate(float fixedDeltaTime)
        {
            TransferAnimatorRootMotion(fixedDeltaTime);
        }
        
        private void OnTimeScaleChanged(float timeScale)
        {
            m_animator.speed = timeScale * m_originalAnimatorSpeed;
        }      
        
        #endregion
        
        
        protected virtual void SetGrounded(bool grounded, Collider collider = null)
        {
            Grounded = grounded;
        }

        protected virtual CancellationTokenSource PerformAction(ActionData actionData, Action onComplete,
            PlayerInputActions initiatingAction = PlayerInputActions.Default)
        {
            m_currentMoveCancel?.Invoke();
            var source = new CancellationTokenSource();
            //m_currentMoveCancel = 
            actionData.Perform(
                new ActionData.PerformParams
                {
                    owner = this,
                    effectholders = m_effectholders,
                    hitboxColliders = m_hitboxColliders,
                    onComplete = onComplete,
                    anim = Animator,
                    initiatingPlayerAction = initiatingAction,
                }, source.Token, source);
            return source;
        }

        public virtual void PerformCombatAction(ActionData action)
        {
            PerformCombatAction(action, null);
        }

        public virtual void PerformCombatAction(ActionData action, Action onComplete)
        {
            m_currentMoveCancel?.Invoke();
            m_busy = true;
            PerformAction(action, () =>
            {
                m_animationMotionModifier = Vector3.one;
                m_moveTweenMotionModifier = Vector3.one;
                m_busy = false;
                onComplete?.Invoke();
            });
        }

        protected virtual void OnAnimationEvent(OnStartDelayedAnimationEvent.AnimationEventType type)
        {
            AnimationTriggerEvent?.Invoke(type);
        }
        
        public virtual void Jump() { }

        public virtual void MoveStarted(ActionData actionData,
            Action cancelMoveSource,
            PlayerInputActions initiatingAction)
        {
            OnMoveStarted?.Invoke(actionData, initiatingAction);
            m_currentMoveCancel?.Invoke();
            m_currentMoveCancel = cancelMoveSource;
            //m_currentMoveCancel += cancelMoveSource;
            m_currentMoveCancel += () => OnMoveCancelled.Invoke(actionData);
        }
        
        public void AttackLanded(ActionData actionData, List<(DamageableRegistry.IDamageable, float)> damageablesHit)
        {
            OnAttackLanded?.Invoke(actionData, damageablesHit);
        }
        
        private void TransferAnimatorRootMotion(float deltaTime)
        {
            Transform animatorTransform = m_animator.transform;
            Transform animatorTransformParent = animatorTransform.parent;
            Transform animatorTransformGrandParent = animatorTransformParent.parent;
            
            animatorTransform.localPosition = Vector3.Scale(animatorTransform.localPosition, m_animationMotionModifier);
            
            Vector3 frameMovement = m_animatorRootMotionTransfer * (animatorTransform.position - animatorTransformParent.position);
            Vector3 parentFrameMovement = animatorTransformParent.position - animatorTransformGrandParent.position;

            // Scale parentFrameMovement by m_moveTweenMotionModifier based on animatorTransformParent's local axes
            Vector3 forwardComponent = Vector3.Project(parentFrameMovement, animatorTransformParent.forward) * m_moveTweenMotionModifier.z;
            Vector3 rightComponent = Vector3.Project(parentFrameMovement, animatorTransformParent.right) * m_moveTweenMotionModifier.x;
            Vector3 upComponent = Vector3.Project(parentFrameMovement, animatorTransformParent.up) * m_moveTweenMotionModifier.y;
            
            parentFrameMovement = forwardComponent + rightComponent + upComponent;
            
            animatorTransform.localPosition = Vector3.zero; //Used
            animatorTransformParent.localPosition = Vector3.zero; //Used
            
            Vector3 worldMovement = frameMovement + parentFrameMovement;
                
            if (worldMovement.magnitude > 0.001f)
            {
                NonConcurrentMovement += worldMovement;
            }
        }

        public virtual float TakeDamage(float damage, CombatEntity owner, ActionData.ActionHitBox hitData, out bool flinch)
        {
            if (m_dead || !gameObject.activeSelf)
            {
                flinch = true;
                return 0f;
            }

            if (m_invulnerable)
            {
                flinch = false;
                OnDamageTaken?.Invoke(-1f);
                return -1f;
            }
            
            float damageDealt = Mathf.Max((int) damage, 1);
            CurrentHealth -= (int) damageDealt;
            
            OnHitReceived?.Invoke(damage, owner, hitData);
            OnDamageTaken?.Invoke(damage);
            OnHit?.Invoke();

            flinch = ((hitData.HitSeverity != HitSeverity.NonDisruptive && CurrentFlinchAmount > FlinchThreshold) && CurrentHealth > 0) || CurrentHealth <= 0f;
            
            return damage;

            //TODO: use in derivitives
            if (flinch)
            {
                m_animationMotionModifier = Vector3.one;
                m_moveTweenMotionModifier = Vector3.one;
                m_hitTween?.Kill();
                m_hitProcess?.Stop();
                m_currentMoveCancel?.Invoke();
                OnMoveCancelled?.Invoke(m_currentActionData);
            }
        }

        public void SetMaximalHealth(int newMaxHealth)
        {
            float healthPercentage = HealthPercentage;
            m_maxHealth = newMaxHealth;
            CurrentHealth = (int)(m_maxHealth * healthPercentage);
        }
        
        public void HealDamage(int amount)
        {
            CurrentHealth += amount;
        }
        
        public virtual bool CanPerform(PlayerInputActions playerInputAction, ActionData action)
        {
            return true;
        }

        public virtual void ActivateCombatEntity(bool resetHealth = true)
        {
            DeactivateCombatEntity(true); //Deactivate to prevent double registering
            m_monoService.OnTimeScaleChanged.AddExclusiveListener(OnTimeScaleChanged);

            m_compositeMovementStrategy.Activate();

            m_monoService.OnUpdate.AddExclusiveListener(ControlledUpdate);
            m_monoService.OnLateUpdate.AddExclusiveListener(ControlledLateUpdate);
            m_monoService.OnFixedUpdate.AddExclusiveListener(ControlledFixedUpdate);

            m_monoService.OnUpdate.AddExclusiveListener(m_compositeMovementStrategy.OnUpdate);
            m_monoService.OnFixedUpdate.AddExclusiveListener(m_compositeMovementStrategy.OnFixedUpdate);
            
            if (m_groundCheckRaycast)
            {
                m_monoService.OnUpdate.AddExclusiveListener(GroundCheck);
            }
            else
            {
                m_floorCheck.TriggerOverlap.AddExclusiveListener(SetGrounded);
            }
            
            m_remoteFloorCheck.TriggerOverlap.AddExclusiveListener(((overlapping, otherCollider) => m_remoteGrounded = overlapping));

            if (resetHealth)
            {
                CurrentHealth = m_maxHealth;
            }
            
            m_compositeMovementStrategy.Reset();
            
            GroundCheck(0f);
        }
        
        public virtual void DeactivateCombatEntity(bool immediate = false)
        {
            m_monoService.OnTimeScaleChanged.RemoveListener(OnTimeScaleChanged);
            m_monoService.OnUpdate.RemoveListener(ControlledUpdate);
            m_monoService.OnLateUpdate.RemoveListener(ControlledLateUpdate);
            m_monoService.OnFixedUpdate.RemoveListener(ControlledFixedUpdate);
            m_monoService.OnUpdate.RemoveListener(m_compositeMovementStrategy.OnUpdate);
            m_monoService.OnFixedUpdate.RemoveListener(m_compositeMovementStrategy.OnFixedUpdate);
            
            if (m_groundCheckRaycast)
            {
                m_monoService.OnUpdate.RemoveListener(GroundCheck);
            }
            else
            {
                m_floorCheck.TriggerOverlap.RemoveListener(SetGrounded);
            }
            
            m_remoteFloorCheck.TriggerOverlap.RemoveListener(((overlapping, otherCollider) => m_remoteGrounded = overlapping));

        }

        public virtual void ActivatePoolable()
        {
            ActivateCombatEntity();
            gameObject.SetActive(true);
            m_compositeMovementStrategy.Activate();
            m_busy = false;
            IsStunned = false;
            m_damageableRegistry.RegisterDamageable(m_hurtBox, this);
        }
        
        public virtual void DeactivatePoolable()
        {
            DeactivateCombatEntity();
            gameObject.SetActive(false);
            m_compositeMovementStrategy.Deactivate();
            m_busy = true;
            m_damageableRegistry.UnRegisterDamageable(m_hurtBox);
            Animator.speed = 1f;
        }
        
        public virtual void ResetPoolable(bool resetMovement = true)
        {
            ResetCombatEntity(true, resetMovement);
            if (resetMovement)
            {
                ResetMovement();
            }
        }

        public virtual void ResetCombatEntity(bool resetHealth = true, bool resetMovement = true)
        {
            m_dead = false;
            m_invulnerable = false;
            if (resetHealth) { CurrentHealth = m_maxHealth; }
        }

        public Transform GetCombatTarget()
        {
            return GetCombatTarget(null);
        }
        
        public abstract Transform GetCombatTarget(ActionData actionData);

        public Vector3 ConcurrentMovement { get; set; }
        protected Vector3 NonConcurrentMovement { get; set; }
        
        [SerializeField] private LayerMask m_abstractingLayers;
        [SerializeField] protected LayerMask m_hardAbstraction;
        
        public Vector3 GetFrameMovement(float deltaTime)
        {
            Vector3 move = ConcurrentMovement * deltaTime + NonConcurrentMovement;

            NonConcurrentMovement = Vector3.zero; //Used

            if (m_groundNormal.sqrMagnitude > 0f)
            {
                //float vertical = move.y;
                //move.y = 0f;
                //Vector3 projected = Vector3.ProjectOnPlane(move, m_groundNormal).normalized * move.magnitude;
                //return projected + vertical * Vector3.up;// * (m_currentMoveCancel != null ? m_moveRootMotionModifier.y : 1f);
            }
            
            return move;
        }

        #region debug

        private void OnDrawGizmosSelected()
        {
            return;
            Gizmos.color = Color.blue;
            Gizmos.DrawLine(
                transform.position + (m_compositeMovementStrategy.EventualPosition - transform.position).normalized,
                transform.position);
            Gizmos.color = Color.magenta;
            Gizmos.DrawLine(
                transform.position + (m_compositeMovementStrategy.LerpPosition - transform.position).normalized * 1.1f,
                transform.position);
            Gizmos.color = Color.green;
            Gizmos.DrawLine(
                transform.position + (m_compositeMovementStrategy.LastFrameEventual - transform.position).normalized *
                1.2f,
                transform.position);
        }

        /*
        public bool debugMovement;
        private List<Vector3> debughits = new List<Vector3>();
        private List<(Vector3,Vector3)> debugMoves = new List<(Vector3,Vector3)>();
        private List<(Vector3,Vector3)> debugPreviousMoves = new List<(Vector3,Vector3)>();
        private List<(Vector3,Vector3)> debugScanPositions = new List<(Vector3,Vector3)>();

        private void OnDrawGizmos()
        {

            Gizmos.color = Color.magenta;
            foreach (Vector3 debughit in debughits)
            {
                Gizmos.DrawSphere(debughit, 0.1f);
            }

            foreach ((Vector3, Vector3) move in debugMoves)
            {
                Gizmos.color = Color.blue;
                Gizmos.DrawLine(move.Item1, move.Item2);

                Gizmos.color = Color.white;
                Gizmos.DrawSphere(move.Item2, 0.1f);
            }

            foreach ((Vector3, Vector3) move in debugPreviousMoves)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawLine(move.Item1, move.Item2);
                Gizmos.color = Color.white;
                Gizmos.DrawSphere(move.Item2, 0.1f);
            }

            foreach ((Vector3, Vector3) scan in debugScanPositions)
            {
                Gizmos.color = Color.cyan;
                Gizmos.DrawLine(scan.Item1, scan.Item2);
                Gizmos.color = Color.cyan;
                Gizmos.DrawWireSphere(scan.Item1, m_hurtBox.Capsule.radius);
                Gizmos.DrawWireSphere(scan.Item2, m_hurtBox.Capsule.radius);
            }
        }
        */
        #endregion
        
        private void GroundCheck(float deltaTime)
        {
            if (m_groundCheckRaycast)
            {
                Vector3 eventual = m_compositeMovementStrategy.EventualPosition;
                

                bool hit = Physics.Raycast(
                    (eventual - transform.position) + m_floorCheck.transform.position + Vector3.up,
                        Vector3.down,
                    out RaycastHit hitData,
                    ConcurrentMovement.y > 0f ? 1.01f : 1.1f,//1.01f,
                    m_hardAbstraction);
                
                m_groundNormal = hit ? hitData.normal : Vector3.zero;
                m_groundDistance = hitData.distance - 1f;

                SetGrounded(hit);
            }
            else
            {
                (bool, Vector3) res = m_floorCheck.CheckOverlap(m_hardAbstraction);
                m_remoteFloorCheck.CheckOverlap(m_hardAbstraction);
                m_groundNormal = res.Item1 ? res.Item2 : Vector3.zero;
                SetGrounded(res.Item1);
            }
        }

        protected virtual void ResetMovement()
        {
            m_moveTweenMotionModifier = Vector3.one;
            m_animationMotionModifier = Vector3.one;
            ConcurrentMovement = Vector3.zero;
            NonConcurrentMovement = Vector3.zero;
            m_hitTween?.Kill();
            
            m_compositeMovementStrategy.Reset();

            GroundCheck(0f);
        }

        public void SetInvulnerable(bool value)
        {
            m_invulnerable = value;
        }

        protected void ChangeDamageMultiplier(float multiplier)
        {
            
            m_baseDamageMultiplier = Mathf.Clamp(multiplier, 1f, 2f);
        }

        public virtual void FaceDirection(Vector3 direction)
        {
            FaceDirection(direction, 1f);
        }

        public abstract void FaceDirection(Vector3 direction, float delta);

#if UNITY_EDITOR
        protected virtual void OnValidate()
        {
            if (Application.isPlaying)
            {
                return;
            }
            
            m_compositeMovementStrategy.OnValidate();
            HashSet<ActionData> moveHashset = new HashSet<ActionData>();

            foreach (ActionData combatAction in m_combatActions)
            {
                moveHashset.Add(combatAction);
            }

            m_combatActions = moveHashset.ToList();

            foreach (ActionData combatAction in m_combatActions)
            {
                if (combatAction)
                {
                    combatAction.Owner = this.gameObject;
                }
            }
        }
#endif

        List<Collider> ISceneReferenceCollection<Collider>.GetItems() => m_colliderReferences.GetItems();

        Collider ISceneReferenceCollection<Collider>.GetItem(int index) => m_colliderReferences.GetItem(index);

        int ISceneReferenceCollection<Collider>.GetIndex(Collider item) => m_colliderReferences.GetIndex(item);

        GameObject ISceneReferenceCollection<GameObject>.GetItem(int index) => m_particleSystemReferences.GetItem(index);

        int ISceneReferenceCollection<GameObject>.GetIndex(GameObject item) => m_particleSystemReferences.GetIndex(item);

        List<GameObject> ISceneReferenceCollection<GameObject>.GetItems() => m_particleSystemReferences.GetItems();
        
        public class OnAnimationEventHandler : UnityEvent<OnStartDelayedAnimationEvent.AnimationEventType> { }
        
        [Serializable]
        public class HitSeverityReaction
        {
            [SerializeField] private HitSeverity m_severity;
        
            public HitSeverity Severity => m_severity;
        
            [SerializeField] private float m_stunDuration;
        
            public float StunDuration => m_stunDuration;
        }
    }

    public enum BlendingStrategy
    {
        Lerp,
        SquareInterpolation,
        SmoothDamp,
    }
    
    public enum MovementStrategy
    {
        DOPE,
        RigidbodyMovePosition,
        Direct,
        NavMesh,
    }
}