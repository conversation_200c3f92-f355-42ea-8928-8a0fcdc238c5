using System;
using UnityEngine;
using Unity.Collections;
using Unity.Jobs;

public class RaycastBatchProcessor {
    //[SerializeField] 
    private static int maxRaycastsPerJob = 10000;

    private static NativeArray<RaycastCommand> rayCommands;
    private static NativeArray<SpherecastCommand> sphereCommands;
    private static NativeArray<RaycastHit> hitResults;

    public static void PerformRaycasts(
        Vector3[] origins,
        Vector3[] directions,
        float[] distances,
        int layerMask,
        bool hitBackfaces,
        bool hitTriggers,
        bool hitMultiFace,
        Action<RaycastHit[]> callback) {
        int rayCount = Mathf.Min(origins.Length, maxRaycastsPerJob);

        QueryTriggerInteraction queryTriggerInteraction = hitTriggers ? QueryTriggerInteraction.Collide : QueryTriggerInteraction.Ignore;

        #if UNITY_2022_3_OR_NEWER
        using (rayCommands = new NativeArray<RaycastCommand>(rayCount, Allocator.TempJob)) {
            QueryParameters parameters = new QueryParameters {
                layerMask = layerMask,
                hitBackfaces = hitBackfaces,
                hitTriggers = queryTriggerInteraction,
                hitMultipleFaces = hitMultiFace
            };

            for (int i = 0; i < rayCount; i++) {
                rayCommands[i] = new RaycastCommand(origins[i], directions[i], parameters, distances[i]);
            }

            ExecuteRaycasts(rayCommands, callback);
        }
        #endif
    }
    
    private static  void ExecuteRaycasts(NativeArray<RaycastCommand> raycastCommands, Action<RaycastHit[]> callback) {
        int maxHitsPerRaycast = 3;
        int totalHitsNeeded = raycastCommands.Length * maxHitsPerRaycast;

        using (hitResults = new NativeArray<RaycastHit>(totalHitsNeeded, Allocator.TempJob)) {
            foreach (RaycastCommand t in raycastCommands) {
                Debug.DrawLine(t.from, t.from + t.direction * 1f, Color.red, 0.5f);
            }

            JobHandle raycastJobHandle = RaycastCommand.ScheduleBatch(raycastCommands, hitResults, maxHitsPerRaycast);
            raycastJobHandle.Complete();

            if (hitResults.Length > 0) {
                RaycastHit[] results = hitResults.ToArray();

                // for (int i = 0; i < results.Length; i++) {
                //     if (results[i].collider != null) {
                //         Debug.Log($"Hit: {results[i].collider.name} at {results[i].point}");
                //         Debug.DrawLine(raycastCommands[i].from, results[i].point, Color.green, 1.0f);
                //     }
                // }

                callback?.Invoke(results);
            }
        }
    }
}