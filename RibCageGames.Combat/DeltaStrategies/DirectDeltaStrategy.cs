using RibCageGames.Combat;
using UnityEngine;

public class DirectDeltaStrategy : CompositeMovementStrategy.DeltaApplicationStrategy
{
    [SerializeField] private float m_testMultiplier;
    public override void OnUpdate(float delta)
    {
        MoveEntityDirect(delta);
    }
    
    private void MoveEntityDirect(float deltaTime)
    {
        LastFrameEventual = EventualPosition;
        EventualPosition += m_owner.GetFrameMovement(deltaTime);
        LerpPosition = EventualPosition;
    }

    public override void OnFixedUpdate(float delta)
    {
    }
}
