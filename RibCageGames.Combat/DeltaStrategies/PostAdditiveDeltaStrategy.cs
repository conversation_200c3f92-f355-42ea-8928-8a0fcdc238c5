using JetBrains.Annotations;
using RibCageGames.Combat;
using RibCageGames.MonoUtils;
using UnityEngine;

[UsedImplicitly]
public class PostAdditiveDeltaStrategy : CompositeMovementStrategy.DeltaApplicationStrategy
{
    [SerializeField] private CapsuleCollider m_hurtBox;
    [SerializeField] private LayerMask m_abstractingLayers;
    [SerializeField] protected LayerMask m_hardAbstraction;
    
    private Vector3 m_previousFrameMove = Vector3.zero;
    
    public override void OnUpdate(float delta)
    {
        MoveEntityPostAdditive(delta);
    }

    private void MoveEntityPostAdditive(float deltaTime)
    {
        Vector3 pos = m_compositeMovementStrategy.EventualPosition;
        Vector3 frameMovement = m_owner.GetFrameMovement(deltaTime);
        Vector3 move = frameMovement;

        float distance = m_previousFrameMove.magnitude;
        Vector3 direction = m_previousFrameMove.normalized;
        float radius = m_hurtBox.radius;

        move = CastMovement(pos + move, radius, direction, distance, move);
        move = CastMovement(pos + move, radius, direction, distance, move);
        move = CastMovement(pos + move, radius, direction, distance, move);

        if (m_owner.Grounded)
        {
            //TODO: move remote floor check to proper side, zero out only x and z, see if both move and previous move necessary 
            if (!m_owner.RemoteGrounded && m_owner.Busy)
            {
                m_previousFrameMove.x = 0f;
                m_previousFrameMove.z = 0f;
            }
        }

        m_compositeMovementStrategy.LastFrameEventual = m_compositeMovementStrategy.EventualPosition;

        m_compositeMovementStrategy.EventualPosition += m_previousFrameMove;
        m_previousFrameMove = move;

        m_compositeMovementStrategy.LerpPosition = m_compositeMovementStrategy.EventualPosition;
    }

    private Vector3 CastMovement(Vector3 pos, float radius, Vector3 direction, float distance, Vector3 move)
    {
        Vector3 p1 = pos + m_hurtBox.center - Vector3.up * (m_hurtBox.height * 0.5f - radius);
        Vector3 p2 = p1 + Vector3.up * (m_hurtBox.height - 2f * radius);

        //TODO: Change to non-alloc
        RaycastHit[] hit = Physics.CapsuleCastAll(p1 - direction, p2 - direction, radius, direction,
            distance + 1, m_abstractingLayers, QueryTriggerInteraction.Ignore);
        
        if (hit.Length >= 1)
        {
            for (int i = 0; i < hit.Length; i++)
            {
                RaycastHit raycastHit = hit[i];
                if (raycastHit.transform == m_owner.transform)
                {
                    continue;
                }

                Vector3 effectiveNormal = raycastHit.normal.normalized;

                bool hardAbstraction = (m_hardAbstraction & (1 << raycastHit.collider.gameObject.layer)) != 0;
                bool softAbstraction = (m_abstractingLayers & (1 << raycastHit.collider.gameObject.layer)) != 0;
                
                if ((hardAbstraction || softAbstraction) && raycastHit.distance > 0.001f)
                {
                    if (raycastHit.distance < 1f)
                    {
                        move -= Vector3.Project(move, effectiveNormal).normalized * (1f - raycastHit.distance);
                    }
                }
            }
        }

        return move;
    }

    public override void OnFixedUpdate(float delta)
    {
    }
}


