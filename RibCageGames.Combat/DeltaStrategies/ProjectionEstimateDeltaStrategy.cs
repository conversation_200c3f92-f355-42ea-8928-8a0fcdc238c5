using JetBrains.Annotations;
using RibCageGames.Combat;
using UnityEngine;

[UsedImplicitly]
public class ProjectionEstimateDeltaStrategy : CompositeMovementStrategy.DeltaApplicationStrategy
{
    [SerializeField] private CapsuleCollider m_hurtBox;
    [SerializeField] private float m_underShootCorrection = 1f;
    [SerializeField] private LayerMask m_abstractingLayers;
    [SerializeField] protected LayerMask m_hardAbstraction;
    
    private Vector3 m_previousFrameMove = Vector3.zero;
    
    public override void OnUpdate(float delta)
    {
        MoveEntityProjectionEstimate(delta);
    }
    
    private void MoveEntityProjectionEstimate(float deltaTime)
    {
        Vector3 pos = m_owner.transform.position;
        Vector3 frameMovement = m_owner.GetFrameMovement(deltaTime);
        Vector3 move = frameMovement;
        Vector3 groundNormal = m_owner.GroundNormal;
        bool grounded = groundNormal.sqrMagnitude > 0.01f;
        groundNormal = m_owner.GroundNormal.normalized;
        bool launching = (grounded 
                          && (Vector3.Dot(groundNormal, move.normalized) > 0.5f
                              || Vector3.Dot(groundNormal, m_previousFrameMove.normalized) > 0.5f));
        
        float distance = m_previousFrameMove.magnitude;
        Vector3 direction = m_previousFrameMove.normalized;
        float radius = m_hurtBox.radius;
        float height = m_hurtBox.height;

        Vector3 p1 = pos + m_hurtBox.center - Vector3.up * (height * 0.5f - radius);
        Vector3 p2 = p1 + Vector3.up * (height - 2f * radius);

        //TODO: Change to non-alloc
        RaycastHit[] hit = Physics.CapsuleCastAll(p1 - m_underShootCorrection * direction, p2 - m_underShootCorrection * direction, radius, direction,
            distance + m_underShootCorrection, m_hardAbstraction | m_abstractingLayers, QueryTriggerInteraction.Ignore);
        
        if (hit.Length >= 1)
        {
            for (int i = 0; i < hit.Length; i++)
            {
                RaycastHit raycastHit = hit[i];
                if (raycastHit.transform == m_owner.transform || raycastHit.transform == m_hurtBox.transform)
                {
                    continue;
                }

                Vector3 effectiveNormal = raycastHit.normal.normalized;
                
                Vector3 abstracted = Vector3.Project(move, effectiveNormal);
                Vector3 abstractedPrevious = Vector3.Project(m_previousFrameMove, effectiveNormal);

                int layer = raycastHit.collider.gameObject.layer;
                bool hardAbstraction = (m_hardAbstraction & (1 << layer)) != 0;
                bool softAbstraction = (m_abstractingLayers & (1 << layer)) != 0;
                
                if ((hardAbstraction || softAbstraction) && raycastHit.distance > 0.001f){
                    
                    float upDot = Vector3.Dot(effectiveNormal, Vector3.up);
                    bool floorHit = hardAbstraction && upDot > 0.5f;
                    bool wallHit = hardAbstraction && upDot < 0.5f && upDot > -0.5f;
                    
                    //Floor
                    //Only consider positive distance to avoid snagging on environment vertexes
                    if (floorHit && raycastHit.distance > 1f)
                    {
                        if (Vector3.Dot(m_previousFrameMove.normalized, effectiveNormal) < -0.5f)
                        {
                            m_previousFrameMove = m_previousFrameMove.normalized *
                                                  Mathf.Min(
                                                      Mathf.Max(0f, raycastHit.distance - 1f),
                                                      m_previousFrameMove.magnitude);
                        }

                        if (Vector3.Dot(move.normalized, effectiveNormal) < -0.5f)
                        {
                            move = move.normalized *
                                   Mathf.Min(
                                       Mathf.Max(0f, raycastHit.distance - 1f),
                                       move.magnitude);
                        }
                    }
                    else if (wallHit) //Wall
                    {
                        if (raycastHit.distance < 1f)
                        {
                            Vector3 bounceOut = //Mathf.Clamp01
                                Mathf.Max(- Vector3.Dot(m_previousFrameMove, effectiveNormal), 0.1f)
                                * (1f - raycastHit.distance) * effectiveNormal;// * Vector3.Dot(m_previousFrameMove, effectiveNormal);
                            m_previousFrameMove -= Vector3.Project(m_previousFrameMove, effectiveNormal);
                            m_previousFrameMove += bounceOut;
                        }
                        else
                        {
                            Vector3 onVector = Vector3.Project(m_previousFrameMove, effectiveNormal);
                            Vector3 offVector = m_previousFrameMove - onVector;

                            m_previousFrameMove = offVector + onVector.normalized * (raycastHit.distance - 1f);
                            
                            if (grounded)
                            {
                                move = Vector3.Project(move,
                                    Vector3.Cross(effectiveNormal, groundNormal));
                            }
                            else
                            {
                                move = move.normalized *
                                       Mathf.Min(
                                           Mathf.Max(0f, raycastHit.distance - 1f),
                                           move.magnitude);
                            }
                            move += (raycastHit.distance - 1f) * effectiveNormal
                                                               * (-1f) * Vector3.Dot(move.normalized, effectiveNormal);
                        }
                    }
                    //Undefined collider behaviour
                    else if (hardAbstraction && distance < 0.001f && upDot < 0.5f)
                    {
                        m_previousFrameMove = Vector3.zero;
                        move = Vector3.zero;
                    }
                    else //Neither
                    {
                        if (raycastHit.distance < 1f)
                        {
                            if (Vector3.Dot(effectiveNormal, move.normalized) < 0f)
                            {
                                move -= Vector3.Project(move, effectiveNormal);
                            }
                            if (Vector3.Dot(effectiveNormal, m_previousFrameMove.normalized) < 0f)
                            {
                                m_previousFrameMove -= Vector3.Project(m_previousFrameMove, effectiveNormal);
                            }
                        }
                        else
                        {
                            move -= abstracted.normalized * (raycastHit.distance - 1f);
                            m_previousFrameMove -= abstractedPrevious.normalized * (raycastHit.distance - 1f);
                        }
                    }
                }
            }
        }
        
        if (m_owner.Grounded)
        {
            //TODO: move remote floor check to proper side, zero out only x and z, see if both move and previous move necessary 
            if (!m_owner.RemoteGrounded && m_owner.Busy)
            {
                m_previousFrameMove.x = 0f;
                m_previousFrameMove.z = 0f;
            }
        }

        if (!launching && grounded)
        {
            move -= Vector3.Project(move, groundNormal);
            m_previousFrameMove -= Vector3.Project(m_previousFrameMove, groundNormal);  
        }

        m_compositeMovementStrategy.LastFrameEventual = m_compositeMovementStrategy.EventualPosition;

        m_compositeMovementStrategy.EventualPosition += m_previousFrameMove;
        m_previousFrameMove = move;

        float groundDistance = m_owner.GroundDistance;
        m_owner.GroundDistance = 0f;
        
        if (m_owner.Grounded && groundDistance < 0f) //Under floor
        {
            m_compositeMovementStrategy.EventualPosition -= Vector3.up * groundDistance;
        }
        else if (!launching) //Over floor and not launching
        {
            //Is this needed?
            //m_compositeMovementStrategy.EventualPosition -= Vector3.up * m_owner.GroundDistance;
        }
        //m_compositeMovementStrategy.LerpPosition = m_compositeMovementStrategy.EventualPosition;
    }
    
    public override void OnFixedUpdate(float delta)
    {
    }

    public override void Reset()
    {
        base.Reset();
        EventualPosition = m_owner.transform.position;
        LastFrameEventual = EventualPosition;
        LerpPosition = EventualPosition;
    }
}


