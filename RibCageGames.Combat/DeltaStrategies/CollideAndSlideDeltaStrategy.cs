using JetBrains.Annotations;
using RibCageGames.Combat;
using UnityEngine;
using UnityEngine.Events;

[UsedImplicitly]
public class CollideAndSlideDeltaStrategy : CompositeMovementStrategy.DeltaApplicationStrategy
{
    [SerializeField] protected LayerMask m_collisionMask;
    [SerializeField] private CapsuleCollider m_hurtBox;
    [SerializeField] private bool m_appplyVertical;
    [SerializeField] private int m_maxBounces = 5;
    [SerializeField] private float m_skinWidth = 0.015f;
    [SerializeField] private float m_maxSlopeAngle = 55f;

    public override UnityEvent<Collider> OnCollisionDetected { get; } = new();

    public override void OnUpdate(float delta)
    {
        MoveEntityCollideAndSlide(delta);
    }

    public bool CollideAndSlideFullDebug;

    private bool debugLine = true;
    private void MoveEntityCollideAndSlide(float delta)
    {
        debugLine = false;
        Vector3 movement = m_owner.GetFrameMovement(delta);

        Vector3 movementBase = movement;
        //Debug.LogError($"Movement is {movement}");
        
        Vector3 vertical = movement.y * Vector3.up;
        movement.y = 0f;

        movement = CollideAndSlide(movement, m_owner.transform.position, 0, false, movement, Vector3.zero);//movement.normalized);
        //movement.y = Mathf.Min(movementBase.y, movement.y);
        if (debugLine && CollideAndSlideFullDebug)
        {
            Debug.LogError($"CollideAndSlide is {movement} with base {movementBase}");
        }
        
        if (m_appplyVertical)
        {
            //Debug.LogError($"Vertical is {vertical} at pos {m_owner.transform.position + movement}");
            var vMovement = CollideAndSlide(vertical, m_owner.transform.position + movement,
                 0, true, vertical,
                vertical.y < 0f ? Vector3.up : 
                    Vector3.zero);
            if (vertical.magnitude > 0.1f)
            {
                //Debug.LogError($"Vertical add to CollideAndSlide is {vMovement} for original {vertical}");
            }
            if (vMovement.y > 1f)
            {
                Debug.Break();
            }
            
            //if (vertical.y > 0f)
            //{
            //    vMovement.y = Mathf.Min(vMovement.y, vertical.y);
            //}
            //else
            //{
            //    vMovement.y = Mathf.Max(vMovement.y, vertical.y);
            //}
            //movement.y = Mathf.Min(movementBase.y, movement.y);
            movement += vMovement;
        }
        
        m_compositeMovementStrategy.EventualPosition += movement;
    }

    private Vector3 CollideAndSlide(Vector3 vel, Vector3 pos, int depth, bool gravityPass, Vector3 velInit,
        Vector3 offset)
    {
        if (depth >= m_maxBounces)
        {
            return Vector3.zero;
        }

        float dist = vel.magnitude + m_skinWidth;

        RaycastHit[] raycastResults = new RaycastHit[5];
        Vector3 p1 = pos + m_hurtBox.center - Vector3.up * (m_hurtBox.height * 0.5f - m_hurtBox.radius);
        Vector3 p2 = p1 + Vector3.up * (m_hurtBox.height - 2f * m_hurtBox.radius);

        if (CollideAndSlideFullDebug)
        {
            Debug.LogError($"Points: p1 {p1} p2 {p2} offset {offset} m_hurtBox.radius {m_hurtBox.radius} vel {vel}");
        }
        //Vector3 p1 = pos + m_hurtBox.center - Vector3.up * m_hurtBox.height * 0.5f;
        //Vector3 p2 = p1 + Vector3.up * m_hurtBox.height;
        if (Physics.CapsuleCastNonAlloc(p1 + offset, p2 + offset, m_hurtBox.radius, vel.normalized, raycastResults,
                dist + offset.magnitude, m_collisionMask, QueryTriggerInteraction.Ignore) > 0)
        {
            Vector3 snapToSurface = vel;
            Vector3 leftover = Vector3.zero;
            Vector3 rollingValue = vel;
            foreach (RaycastHit hit in raycastResults)
            {
                if (CollideAndSlideFullDebug)
                {
                    Debug.LogError($"Hit result {hit.collider} at distance {hit.distance} at point {hit.point}");
                }
                
                if (hit.collider == null || hit.collider == m_hurtBox || hit.collider == m_hurtBox || !(hit.distance > 0f) || (offset.magnitude > 0.5f && hit.distance < m_skinWidth))
                {
                    continue;
                }

                snapToSurface = rollingValue.normalized * (hit.distance - offset.magnitude - m_skinWidth);
                leftover = rollingValue - snapToSurface;
                float angle = Vector3.Angle(Vector3.up, hit.normal);

                if (!gravityPass && CollideAndSlideFullDebug)
                {
                    //Debug.LogError($"projection was {p1 + offset} and {p2 + offset} at {dist + offset.magnitude} in direction {rollingValue.normalized}");
                    Debug.LogError(
                        $"Hit at depth {depth} hit {hit.collider.gameObject.name} snapToSurface {snapToSurface} as rollingValue.normalized {rollingValue.normalized} at dist {hit.distance} with {offset.magnitude} and {m_skinWidth} angle {angle} at {hit.point}");
                    debugLine = true;
                }

                OnCollisionDetected?.Invoke(hit.collider);

                if (snapToSurface.magnitude <= m_skinWidth)
                {
                    snapToSurface = Vector3.zero;
                }

                // normal ground / slope
                if (angle <= m_maxSlopeAngle)
                {
                    if (gravityPass)
                    {
                        return snapToSurface;
                    }

                    leftover = ProjectAndScale(leftover, hit.normal);
                }
                // wall or steep slope
                else
                {
                    float scale = 1 - Vector3.Dot(
                        new Vector3(hit.normal.x, 0, hit.normal.z).normalized,
                        -new Vector3(velInit.x, 0, velInit.z).normalized
                    );

                    if (m_owner.Grounded && gravityPass)
                    {
                        leftover = ProjectAndScale(
                            new Vector3(leftover.x, 0, leftover.z),
                            new Vector3(hit.normal.x, 0, hit.normal.z)
                        ).normalized;
                        leftover *= scale;
                    }
                    else
                    {
                        leftover = ProjectAndScale(leftover, hit.normal) * scale;
                    }

                    if (!gravityPass)
                    {
                        if (CollideAndSlideFullDebug)
                        {
                            Debug.LogError($"Hit wall {scale} leftover {leftover}");
                        }
                    }
                }
                rollingValue = leftover;
            }
            return snapToSurface + CollideAndSlide(leftover, pos + snapToSurface, depth + 1, gravityPass, velInit,
                Vector3.zero);
        }
        else
        {
            if (CollideAndSlideFullDebug)
            {
                Debug.LogError($"No hit");
            }
        }
        return vel;
    }

    private Vector3 ProjectAndScale(Vector3 vec, Vector3 normal)
    {
        float mag = vec.magnitude;
        vec = Vector3.ProjectOnPlane(vec, normal).normalized;
        vec *= mag;
        return vec;
    }


    public override void OnFixedUpdate(float delta)
    {
    }
}
