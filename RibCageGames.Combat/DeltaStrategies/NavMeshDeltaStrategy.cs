using System;
using Cysharp.Threading.Tasks;
using RibCageGames.Combat;
using UnityEngine;
using UnityEngine.AI;
using Object = UnityEngine.Object;

public class NavMeshDeltaStrategy : CompositeMovementStrategy.DeltaApplicationStrategy
{
    [SerializeField] protected NavMeshAgent m_navMeshGuide;
    [SerializeField] private float m_groundTolerance;
    [NonSerialized] protected bool m_navigating = false;
    [NonSerialized] private Vector3 m_lastFrameNavMeshPosition;

    public NavMeshAgent NavMeshGuide => m_navMeshGuide;
    
    public override bool Grounded => m_owner.transform.position.y - m_navMeshGuide.transform.position.y < m_groundTolerance;

    public Vector3 Forward
    {
        get => m_navMeshGuide.transform.forward;
        set => m_navMeshGuide.transform.forward = value;
    }

    public float Speed
    {
        //get => m_navMeshGuide.transform.forward;
        set
        {
            m_navMeshGuide.speed = value;
            if (m_owner.gameObject.activeInHierarchy)
            {
                m_navMeshGuide.isStopped = value < 0.1f;
            }
        }
    }


    public bool Navigating { get => m_navigating; set => m_navigating = value; }

    public override void Activate()
    {
        base.Activate();
        m_navMeshGuide.transform.SetParent(null);
        m_navMeshGuide.enabled = true;
    }

    public override void Deactivate()
    {
        base.Deactivate();
        m_navMeshGuide.enabled = false;
    }

    public override void Reset()
    {
        base.Reset();
        m_navMeshGuide.transform.position = m_owner.transform.position;
        EventualPosition = m_owner.transform.position;
        LastFrameEventual = EventualPosition;
        LerpPosition = EventualPosition;
        MatchGuideHeight().Forget();
    }

    private async UniTask MatchGuideHeight()
    {
        await UniTask.NextFrame();
        m_owner.transform.position = m_navMeshGuide.transform.position;
    }

    public override void OnDestroy()
    {
        base.OnDestroy();
        if (m_navMeshGuide != null)
        {
            Object.Destroy(m_navMeshGuide.gameObject);
        }
    }

    public override void OnUpdate(float delta)
    {
        MoveEntityNavMesh(delta);
    }
    
    private void MoveEntityNavMesh(float deltaTime)
        {
            if (!m_navMeshGuide.enabled || !m_owner.gameObject.activeInHierarchy) { return; }

            LastFrameEventual = EventualPosition;

            if (m_navigating)
            {
                EventualPosition = m_navMeshGuide.transform.position;
                LerpPosition = EventualPosition;
                return;
            }

            Vector3 navMeshPos = m_navMeshGuide.transform.position;
            Vector3 posDelta = EventualPosition - navMeshPos;
            posDelta.y = 0f;

            Vector3 concurrentDelta = m_owner.ConcurrentMovement * deltaTime;
            Vector3 nonConcurrentdelta = m_owner.GetFrameMovement(deltaTime) - concurrentDelta;

            // Calculate new eventual position
            Vector3 newEventualPosition = EventualPosition + concurrentDelta + nonConcurrentdelta;

            // Zero out Y components for movement calculations
            concurrentDelta.y = 0f;
            nonConcurrentdelta.y = 0f;

            // Check if we need to snap to navmesh position (when moving away from navmesh)
            float eventualDot = Vector3.Dot(concurrentDelta, posDelta);
            if (eventualDot < 0.5f) //TODO: should only affect concurrent movement
            {
                float preservedY = newEventualPosition.y;
                newEventualPosition = navMeshPos + concurrentDelta + nonConcurrentdelta;
                newEventualPosition.y = preservedY;
            }

            // Update navmesh guide
            Vector3 dest = navMeshPos + (concurrentDelta + nonConcurrentdelta).normalized;
            m_navMeshGuide.transform.position += nonConcurrentdelta;
            m_navMeshGuide.SetDestination(dest);

            // Handle stopping
            if (posDelta.magnitude < m_navMeshGuide.stoppingDistance)
            {
                m_navMeshGuide.isStopped = true;
            }

            // Handle Y coordinate based on grounding and navmesh position
            if (Grounded)
            {
                newEventualPosition.y = !m_owner.Busy ? navMeshPos.y : Mathf.Max(navMeshPos.y, newEventualPosition.y);
            }

            // Handle case where navmesh hasn't moved much (stick to navmesh X/Z)
            if ((m_lastFrameNavMeshPosition - navMeshPos).magnitude < 0.01f)
            {
                newEventualPosition.x = navMeshPos.x;
                newEventualPosition.z = navMeshPos.z;
            }

            // Final assignments
            EventualPosition = newEventualPosition;
            LerpPosition = new Vector3(navMeshPos.x, newEventualPosition.y, navMeshPos.z);
            m_lastFrameNavMeshPosition = navMeshPos;
        }

    public override void OnFixedUpdate(float delta)
    {
    }
}

