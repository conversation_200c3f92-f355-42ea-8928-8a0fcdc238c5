using RibCageGames.Combat;
using UnityEngine;

public class RigidbodyDeltaStrategy : CompositeMovementStrategy.DeltaApplicationStrategy
{
    [SerializeField] private Rigidbody m_physicsGuideRigidbody;

    public override void OnUpdate(float delta)
    {
    }

    public override void Activate()
    {
        base.Activate();
        m_physicsGuideRigidbody.transform.position = m_owner.transform.position;
        m_physicsGuideRigidbody.transform.SetParent(null);
    }

    private void MoveEntityRigidbody(float deltaTime)
    {
        EventualPosition = m_physicsGuideRigidbody.position + m_owner.GetFrameMovement(deltaTime);
        m_physicsGuideRigidbody.MovePosition(EventualPosition);

        LastFrameEventual = EventualPosition;

        LerpPosition = m_physicsGuideRigidbody.position;
    }

    public override void OnFixedUpdate(float delta)
    {
        MoveEntityRigidbody(delta);
    }

    public override void Reset()
    {
        base.Reset();
        m_physicsGuideRigidbody.transform.position = m_owner.transform.position;
        m_physicsGuideRigidbody.transform.SetParent(null);
        m_physicsGuideRigidbody.linearVelocity = Vector3.zero;
    }

    public override void OnDestroy()
    {
        base.OnDestroy();
        Object.Destroy(m_physicsGuideRigidbody.gameObject);
    }
}

