using JetBrains.Annotations;
using RibCageGames.Combat;
using RibCageGames.MonoUtils;
using UnityEngine;

[UsedImplicitly]
public class DoubleProjectionStrategy : CompositeMovementStrategy.DeltaApplicationStrategy
{
    [SerializeField] private CapsuleCollider m_hurtBox;
    [SerializeField] private LayerMask m_abstractingLayers;
    [SerializeField] protected LayerMask m_hardAbstraction;
    
    public override void OnUpdate(float delta)
    {
        MoveEntityProjectionEstimate(delta);
    }
    
    private void MoveEntityProjectionEstimate(float deltaTime)
    {
            Vector3 pos = m_compositeMovementStrategy.EventualPosition;
            Vector3 frameMovement = m_owner.GetFrameMovement(deltaTime);
            Vector3 move = frameMovement;
            Vector3 groundNormal = m_owner.GroundNormal;
            
            Vector3 projectedMove = move;

            if (groundNormal.sqrMagnitude > 0.01f
                && Vector3.Dot(groundNormal, move.normalized) < 0.5f) // for jumping etc
            {
                projectedMove = move = Vector3.ProjectOnPlane(move, groundNormal);
            }

            float distance = move.magnitude;
            Vector3 direction = move.normalized;


            Vector3 p1 = pos + m_hurtBox.center - Vector3.up * (m_hurtBox.height * 0.5f - m_hurtBox.radius);
            float radius;
            Vector3 p2 = p1 + Vector3.up * (m_hurtBox.height - 2 * (radius = m_hurtBox.radius));

            //TODO: Change to non-alloc
            RaycastHit[] hit = Physics.CapsuleCastAll(p1 - direction, p2 - direction, radius, direction,
                distance + 1, m_abstractingLayers, QueryTriggerInteraction.Ignore);

            if (hit.Length >= 1)
            {
                for (int i = 0; i < hit.Length; i++)
                {
                    RaycastHit raycastHit = hit[i];
                    if (raycastHit.transform == m_owner.transform)
                    {
                        continue;
                    }
                    
                    Vector3 effectiveNormal = raycastHit.normal.normalized;
                    Vector3 abstracted = Vector3.Project(move, effectiveNormal);

                    bool hardAbstraction = (m_hardAbstraction & (1 << raycastHit.collider.gameObject.layer)) != 0;
                    if (raycastHit.distance > 1f || (hardAbstraction && raycastHit.distance > 0.001f))
                    {
                        move -= abstracted +
                                (effectiveNormal * (1f - raycastHit.distance) *
                                 Vector3.Dot(effectiveNormal, move.normalized));
                        
                        move = move.normalized *
                               Mathf.Min(Mathf.Max(0f, raycastHit.distance - 1f), move.magnitude);                                                                
                    }
                }
            }

            if (m_owner.Grounded)
            {
                //TODO: move remote floor check to proper side, zero out only x and z, see if both move and previous move necessary 
                if (!m_owner.RemoteGrounded && m_owner.Busy)
                {
                    move.x = 0f;
                    move.z = 0f;
                }
            }
            
            /*
            if (debugMovement)
            {
                debugMoves.Add((pos + m_previousFrameMove, pos));
                debugPreviousMoves.Add((Vector3.up * 0.3f + pos + move, Vector3.up * 0.3f + pos));
            }
            */
            
            //move = Vector3.ProjectOnPlane(move, groundNormal);
            //m_previousFrameMove = Vector3.ProjectOnPlane(m_previousFrameMove, groundNormal);
            
            
            move = move.ComponentClamp(projectedMove);
            
            //m_previousFrameMove = Vector3.Project(m_previousFrameMove, projectedPreviousMove);
            //move = Vector3.Project(move, projectedMove);
            
            m_compositeMovementStrategy.LastFrameEventual = m_compositeMovementStrategy.EventualPosition;
            
            m_compositeMovementStrategy.EventualPosition += move;
            
            m_compositeMovementStrategy.LerpPosition = m_compositeMovementStrategy.EventualPosition;
        }

    public override void OnFixedUpdate(float delta)
    {
    }
}



