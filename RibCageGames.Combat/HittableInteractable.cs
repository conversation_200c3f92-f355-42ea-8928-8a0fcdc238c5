using System;
using System.Collections;
using System.Collections.Generic;
using RibCageGames.Base;
using RibCageGames.Combat;
using UnityEngine;
using UnityEngine.Events;

public class HittableInteractable : MonoBehaviour, DamageableRegistry.IDamageable
{
    [SerializeField] private Collider m_hitCollider;
    [SerializeField] private UnityEvent m_onHit;

    private ColliderReference m_hurtBox;
    public UnityEvent OnHit => m_onHit;
    public Collider Collider => m_hitCollider;
    public Transform ConnectedTransform => transform;
    public bool IsPlayer { get; } = false;
    public Animator Animator { get; } = null;
    
    private void Start()
    {
        m_hurtBox = ColliderReference.Create(m_hitCollider);
        Activate();
    }

    public float TakeDamage(float damage, CombatEntity owner, ActionData.ActionHitBox hitData, out bool flinch)
    {
        flinch = false;
        if (!gameObject.activeInHierarchy) { return 0f; }

        m_onHit?.Invoke();
        return 0f;
    }

    public void Activate()
    {
        m_hurtBox ??= ColliderReference.Create(m_hitCollider);
        ServiceLocator.Get<DamageableRegistry>().RegisterDamageable(m_hurtBox, this);
        gameObject.SetActive(true);
    }

    public void Deactivate()
    {
        gameObject.SetActive(false);
        MonoProcess.NextFrame.Do(() =>
        {
            ServiceLocator.Get<DamageableRegistry>().UnRegisterDamageable(m_hurtBox);
        });
    }
}
