namespace RibCageGames.Combat
{
    /// <summary>
    /// Interface for animated visual effects that can be played and stopped
    /// </summary>
    public interface IAnimatedVisualEffect
    {
        /// <summary>
        /// Start playing the visual effect
        /// </summary>
        void Play();
        
        /// <summary>
        /// Stop the visual effect
        /// </summary>
        void Stop();
        
        /// <summary>
        /// Check if the effect is currently playing
        /// </summary>
        bool IsPlaying { get; }
    }
}
