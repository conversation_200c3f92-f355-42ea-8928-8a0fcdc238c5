using RibCageGames.Combat;
using UnityEngine;

public class SmoothDampSmoothingStrategy : CompositeMovementStrategy.SmoothingStrategy
{
    [SerializeField] private float m_smoothingFactor = 0.05f;
    
    private Vector3 m_smoothDampSpeed = Vector3.zero;
    
    public override void OnUpdate(float delta)
    {
        m_owner.transform.position = Vector3.SmoothDamp(m_owner.transform.position, EventualPosition, ref m_smoothDampSpeed, m_smoothingFactor);
    }

    public override void OnFixedUpdate(float delta)
    {
    }
}

