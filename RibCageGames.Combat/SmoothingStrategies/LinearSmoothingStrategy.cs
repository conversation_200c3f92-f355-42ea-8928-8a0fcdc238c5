using RibCageGames.Combat;
using UnityEngine;

public class LinearSmoothingStrategy : CompositeMovementStrategy.SmoothingStrategy
{
    [SerializeField] private float m_smoothingFactor = 0.05f;
    public override void OnUpdate(float delta)
    {
        m_owner.transform.position = Vector3.Lerp(m_owner.transform.position, 
            EventualPosition,
            m_smoothingFactor);
    }

    public override void OnFixedUpdate(float delta)
    {
    }
}
