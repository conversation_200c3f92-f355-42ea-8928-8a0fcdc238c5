using JetBrains.Annotations;
using RibCageGames.Combat;
using RibCageGames.MonoUtils;
using UnityEngine;

[UsedImplicitly]
public class SquareInterpolationSmoothingStrategy : CompositeMovementStrategy.SmoothingStrategy
{
    [SerializeField] private float m_smoothingFactor = 0.05f;
    public override void OnUpdate(float delta)
    {
        m_owner.transform.position = MathUtils.SquareInterpolation(m_owner.transform.position,
            m_compositeMovementStrategy.LastFrameEventual,
            m_compositeMovementStrategy.EventualPosition,
            m_smoothingFactor);
    }

    public override void OnFixedUpdate(float delta)
    {
    }
}

