namespace RibCageGames.Combat
{
    using System;
    using UnityEngine;
    using UnityEngine.VFX;

    [Serializable]
    public class VisualEffectHolder
    {
        [NonSerialized] public ParticleSystem[] effectParticleSystems;
        [NonSerialized] public VisualEffect[] vfxEffects;
        [NonSerialized] public IAnimatedVisualEffect[] animatedEffects;

        [NonSerialized] public bool usesVFX = false;
        [NonSerialized] public bool usesPS = false;
        [NonSerialized] public bool usesAnimated = false;

        [SerializeField] public GameObject EffectHolder;

        public void Initialize()
        {
            ParticleSystem[] particleSystems;
            VisualEffect[] visualEffects;
            IAnimatedVisualEffect[] animatedVisualEffects;
            
            if (EffectHolder == null)
            {
                Debug.LogError($"No EffectHolder for move {this}");
                return;
            }
            
            particleSystems = EffectHolder.GetComponentsInChildren<ParticleSystem>();
            visualEffects = EffectHolder.GetComponentsInChildren<VisualEffect>();
            animatedVisualEffects = EffectHolder.GetComponents<IAnimatedVisualEffect>();

            if (visualEffects != null && visualEffects.Length > 0)
            {
                vfxEffects = visualEffects;
                usesVFX = true;
            }
            
            if (particleSystems != null && particleSystems.Length > 0)
            {
                effectParticleSystems = particleSystems;
                usesPS = true;
            }
            
            if (animatedVisualEffects != null && animatedVisualEffects.Length > 0)
            {
                animatedEffects = animatedVisualEffects;
                usesAnimated = true;
            }
        }

        public VisualEffectHolder(GameObject parent)
        {
            EffectHolder = parent;
            Initialize();
        }
        
        public void Play()
        {
            if (usesVFX)
            {
                foreach (VisualEffect vfxEffect in vfxEffects)
                {
                    vfxEffect.Play();
                }
            }
            
            if (usesPS)
            {
                foreach (ParticleSystem particleSystem in effectParticleSystems)
                {
                    particleSystem.Play();
                }
            }
            
            if (usesAnimated)
            {
                foreach (IAnimatedVisualEffect animatedEffect in animatedEffects)
                {
                    animatedEffect.Play();
                }
            }
        }

        public void Stop()
        {
            if (usesVFX)
            {
                foreach (VisualEffect vfxEffect in vfxEffects)
                {
                    if (vfxEffect != null)
                    {
                        vfxEffect.Stop();
                    }
                }
            }
            
            if (usesPS)
            {
                foreach (ParticleSystem particleSystem in effectParticleSystems)
                {
                    if (particleSystem != null)
                    {
                        particleSystem.Stop();
                    }
                }
            }
            
            if (usesAnimated)
            {
                foreach (IAnimatedVisualEffect animatedEffect in animatedEffects)
                {
                    if (animatedEffect != null)
                    {
                        animatedEffect.Stop();
                    }
                }
            }
        }
    }
}
