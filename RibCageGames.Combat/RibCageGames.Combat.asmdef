{"name": "RibCageGames.Combat", "rootNamespace": "", "references": ["RibCageGames.Base", "RibCageGames.Animation", "RibCageGames.MonoUtils", "Unity.InputSystem", "RibCageGames.Editor", "RibCageGames.Sound", "RibCageGames.Input", "UniTask", "RibCageGames.Music"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}