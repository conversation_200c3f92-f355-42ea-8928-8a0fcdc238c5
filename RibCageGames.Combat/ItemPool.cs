using System;
using System.Collections.Generic;
using System.Linq;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Editor.EditorUtillities.SerializeInterface;
using UnityEngine;
using UnityEngine.Events;
using ZLinq;

[CreateAssetMenu(fileName = "ItemPool", menuName = "RibCageGames/Settings/ItemPool")]
public class ItemPool : MonoScriptableObject
{
    [SerializeField] private InterfaceReference<IInventory> m_targetInventory;
    [SerializeField] private List<ItemOccurence> m_availableItems;
    
    [SerializeField] private int m_maximalPowerupCount;

    private List<ItemOccurence> m_currentAvailableItems;

    public override void Initialize()
    {
        m_targetInventory.Value.OnInventoryChanged.AddListener(InventoryChanged);
        m_currentAvailableItems = new List<ItemOccurence>();
        foreach (ItemOccurence item in m_availableItems)
        {
            if (item.MaximalOccurence < 1)
            {
                continue;
            }
            
            if (item.Powerup.Type == PowerUpType.PowerupUpgrade)
            {
                if (m_targetInventory.Value.CurrentInventory.Any(x => true))
                {
                    m_currentAvailableItems.Add(item);
                }
            }
            else
            {
                m_currentAvailableItems.Add(item);
            }
        }
    }

    private void InventoryChanged(Dictionary<PowerUpType, (int, BonusEffect, Powerup)> inventory)
    {
        bool powerupCapReached = inventory.Values.Count(x => true) >= m_maximalPowerupCount;
        m_currentAvailableItems = m_availableItems
            .Select(x => new ItemOccurence
            {
                m_powerup = x.Powerup,
                m_maximalOccurences =
                    powerupCapReached && x.Powerup.IsNamedPowerup ? 0 :
                    (inventory.TryGetValue(x.Powerup.Type, out (int level, BonusEffect, Powerup) value)
                        ? x.MaximalOccurence - value.level
                        : x.MaximalOccurence),
                CurrentUses = inventory.TryGetValue(x.Powerup.Type, out (int level, BonusEffect, Powerup) uses) ? 0 : uses.level,
            }).ToList();
    }

    public List<Powerup> GetUniquePowerupChoices(int options)
    {
        return GetUniquePowerupChoicesFromCollection(m_currentAvailableItems, options);
    }
    
    private List<Powerup> GetUniquePowerupChoicesFromCollection(List<ItemOccurence> availableItems, int options)
    {
        return availableItems
            .Where(x => x.Powerup.IsNamedPowerup)
            .Where(x => x.CurrentUses < x.MaximalOccurence)
            .TakeRandom(options)
            .Select(x => x.Powerup)
            .ToList();
    }

    
    public bool IsRewardAvailable(PickupType type)
    {
        return m_currentAvailableItems.Any(x =>
        {
            return type == x.Powerup.Type switch
            {
                PowerUpType.Delay => PickupType.Powerup,
                PowerUpType.Flanger => PickupType.Powerup,
                PowerUpType.Reverb => PickupType.Powerup,
                PowerUpType.MaxHealth => PickupType.HPUpgrade,
                PowerUpType.Plugin => PickupType.Plugin,
                PowerUpType.PowerupUpgrade => PickupType.PowerupUpgrade,
                _ => PickupType.None,
            };
        });
    }
    
    public List<PickupType> GetRewardChoices(int options)
    {
        return GetRewardChoicesFromCollection(m_currentAvailableItems, options);
    }

    private List<PickupType> GetRewardChoicesFromCollection(List<ItemOccurence> availableItems, int options)
    {
        string debug = "Items in consideration:";
        foreach (ItemOccurence item in availableItems)
        {
            debug += $"\n {item.Powerup.Type} at {item.CurrentUses} out of {item.MaximalOccurence}";
        }
        //Debug.LogError(debug);

        List<PickupType> results = availableItems.AsValueEnumerable()
            .Where(x => x.CurrentUses < x.MaximalOccurence)
            .Select(x =>
            {
                return x.Powerup.Type switch
                {
                    PowerUpType.Delay => PickupType.Powerup,
                    PowerUpType.Flanger => PickupType.Powerup,
                    PowerUpType.Reverb => PickupType.Powerup,
                    PowerUpType.MaxHealth => PickupType.HPUpgrade,
                    PowerUpType.Plugin => PickupType.Plugin,
                    PowerUpType.PowerupUpgrade => PickupType.PowerupUpgrade,
                    _ => throw new ArgumentOutOfRangeException(nameof(PowerUpType), x.Powerup.Type,
                        "No mapping defined for this SourceEnum value.")
                };
            })
            .Where(x => x != PickupType.PowerupUpgrade || m_targetInventory.Value.CurrentInventory.Any(p => true))
            .Distinct()
            .Where(x => x == PickupType.Powerup) //Why do we get hp otherwise?
            .TakeRandom(options)
            .ToList();

        //Debug.LogError(results.Count > 1 ? $"Selected {results[0]} and {results[1]}" : $"Selected {results[0]}");

        return results;
    }

    #region Debug

    private List<ItemOccurence> m_currentAvailableItemsDebug;
    
    [ContextMenu("InitializeDebug")]
    public void InitializeDebug()
    {
        m_currentAvailableItemsDebug = new List<ItemOccurence>();
        foreach (ItemOccurence item in m_availableItems)
        {
            if (item.MaximalOccurence < 1)
            {
                continue;
            }
            
            if (item.Powerup.Type == PowerUpType.PowerupUpgrade)
            {
                if (m_targetInventory.Value.CurrentInventory.Any(x => true))
                {
                    m_currentAvailableItemsDebug.Add(item);
                }
            }
            else
            {
                m_currentAvailableItemsDebug.Add(item);
            }
        }
    }

    
    
    [Serializable]
    internal class DebugInventory : IInventory
    {
        public UnityEvent<Dictionary<PowerUpType, (int, BonusEffect, Powerup)>> OnInventoryChanged { get; } = new();
        public Dictionary<PowerUpType, (int, BonusEffect, Powerup)> CurrentInventory => m_powerUpLevelDict.GeneratedDictionary;
        
        [SerializeField] private SerializableDictionary<PowerUpType, (int level, BonusEffect bonus, Powerup source)> m_powerUpLevelDict;

    }
    #endregion
}

[Serializable]
internal class ItemOccurence
{
    [SerializeField] internal Powerup m_powerup;
    [SerializeField] internal int m_maximalOccurences;
    
    internal int MaximalOccurence => m_maximalOccurences;
    internal int CurrentUses { get; set; }
    internal Powerup Powerup => m_powerup;
}

public enum PickupType
{
    Powerup = 0,
    Plugin = 20,
    HPUpgrade = 50,
    PowerupUpgrade = 100,
    RunCurrency = 150,
    ArcadeToken = 160,
    ComboChallenge = 200,
    None = 777,
    RandomSelection = 999,
}