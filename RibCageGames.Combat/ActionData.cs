namespace RibCageGames.Combat
{
    using UnityEngine.Events;
    using UnityEngine.VFX;
    using DG.Tweening;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Animation;
    using Base;
    using Editor;
    using Input;
    using MonoUtils;
    using Sound;
    using UnityEditor;
    using UnityEngine;
    using System.Threading;
    using Editor.EditorUtillities.SerializeInterface;

    [CreateAssetMenu(fileName = "Action", menuName = "RibCageGames/ActionSystem/ActionData")]
    public class ActionData : MonoScriptableObject
    {
        [SerializeField] protected CombatEntity m_owner;
        [HideInInspector] [SerializeField] private Animator m_animator;
        public CombatEntity CombatEntityOwner => m_owner;
        
        public virtual int HitBoxCount => r_hitboxes.Count;
        public virtual bool Valid => true;
        
        [SerializeField] protected string m_displayName;
        [SerializeField] protected bool m_ariel = false;
        [SerializeField] protected float m_moveDuration;
        [SerializeField] protected float m_minimalMoveTime;
        [SerializeField] protected Vector3 m_rootMotionModifierVector = Vector3.zero;
        [SerializeField] protected Vector3 m_tweenMotionModifierVector = Vector3.one;
        [SerializeField] protected MonoTweenerSettings m_moveTween;
        [SerializeField] protected float m_moveDamage = 1f;
        [SerializeField][AnimatorParameterControl("m_animator")] protected List<AnimatorParameterControl> m_animatorParameters;
        [SerializeField][ComponentReferenceAccessor("m_owner", "m_hitBoxColliderIndex", typeof(Collider))] protected List<ActionHitBox> m_hitboxes;
        [SerializeField] protected List<ActionSoundEffect> m_soundEffects;
        [SerializeField] protected List<ActionMoveEffect> m_actionMoveEffects;
        [SerializeField][ComponentReferenceAccessor("m_owner", "m_particleSystemsParentIndex", typeof(GameObject))] protected List<ActionEffect> m_effects;
        [SerializeField][ArrayElementTitle("action")] protected List<InputFollowingOption> m_followingActions;
        [SerializeField] protected float m_fullSnapRadius = 3f;
        [SerializeField] protected float m_lightSnapRadius = 10f;
        [SerializeField] protected float m_lightSnapAngle = 45f;
        [SerializeField] protected bool m_ignoreHeight = false;
        
        #region RuntimeValues

        [NonSerialized] protected string r_displayName;
        [NonSerialized] protected bool r_ariel;
        [NonSerialized] protected float r_moveDuration;
        [NonSerialized] protected float r_minimalMoveTime;
        [NonSerialized] protected Vector3 r_animationMotionModifier = Vector3.one;
        [NonSerialized] protected Vector3 r_tweenMotionModifier = Vector3.one;
        [NonSerialized] protected MonoTweenerSettings r_moveTween;
        [NonSerialized] protected float r_moveDamage = 1f;
        [NonSerialized] protected List<AnimatorParameterControl> r_animatorParameters;
        [NonSerialized] protected List<ActionHitBox> r_hitboxes;
        [NonSerialized] protected List<ActionSoundEffect> r_soundEffects;
        [NonSerialized] protected List<ActionMoveEffect> r_actionMoveEffects;
        [NonSerialized] protected List<ActionEffect> r_effects;
        [NonSerialized] protected List<InputFollowingOption> r_followingActions;
        [NonSerialized] protected float r_fullSnapRadius = 3f;
        [NonSerialized] protected float r_lightSnapRadius = 10f;
        [NonSerialized] protected float r_lightSnapAngle = 45f;
        [NonSerialized] protected bool r_ignoreHeight = false;
        
        #endregion

        #region Runtime accesors

        public virtual string DisplayName
        {
            get => r_displayName;
            set => r_displayName = value;
        }
        
        public virtual bool Ariel
        {
            get => r_ariel;
            set => r_ariel = value;
        }
        
        public virtual float MoveDuration
        {
            get => r_moveDuration;
            set => r_moveDuration = value;
        }
        
        public virtual float MinimalMoveTime
        {
            get => r_minimalMoveTime;
            set => r_minimalMoveTime = value;
        }
        
        public virtual Vector3 AnimationMotionModifier
        {
            get => r_animationMotionModifier;
            set => r_animationMotionModifier = value;
        }
        
        public virtual Vector3 TweenMotionModifier
        {
            get => r_tweenMotionModifier;
            set => r_tweenMotionModifier = value;
        }
        
        public virtual MonoTweenerSettings MoveTween
        {
            get => r_moveTween;
            set => r_moveTween = value;
        }
        
        public virtual float MoveDamage
        {
            get => r_moveDamage;
            set => r_moveDamage = value;
        }
        
        public virtual List<AnimatorParameterControl> AnimatorParameters
        {
            get => r_animatorParameters;
            set => r_animatorParameters = value.CopyFromByValue();
        }

        public virtual List<ActionHitBox> Hitboxes
        {
            get => r_hitboxes;
            set => r_hitboxes = value;
        }
        
        public virtual List<ActionSoundEffect> SoundEffects
        {
            get => r_soundEffects;
            set => r_soundEffects = value;
        }
        
        public virtual List<ActionMoveEffect> ActionMoveEffects
        {
            get => r_actionMoveEffects;
            set => r_actionMoveEffects = value;
        }
        
        public virtual List<ActionEffect> Effects
        {
            get => r_effects;
            set => r_effects = value;
        }
        
        public virtual List<InputFollowingOption> FollowingActions
        {
            get => r_followingActions;
            set => r_followingActions = value;
        }
        
        public virtual float FullSnapRadius
        {
            get => r_fullSnapRadius;
            set => r_fullSnapRadius = value;
        }
        
        public virtual float LightSnapAngle
        {
            get => r_lightSnapAngle;
            set => r_lightSnapAngle = value;
        }
        
        public virtual float LightSnapRadius
        {
            get => r_lightSnapRadius;
            set => r_lightSnapRadius = value;
        }
        
        public virtual bool IgnoreHeight
        {
            get => r_ignoreHeight;
            set => r_ignoreHeight = value;
        }
        
        #endregion

        #region EditorTime Accesors
        
        public string OriginalDisplayName => m_displayName;
        public bool OriginalAriel => m_ariel;
        public float OriginalMoveDuration => m_moveDuration;
        public float OriginalMinimalMoveTime => m_minimalMoveTime;
        public Vector3 OriginalRootMotionModifier => m_rootMotionModifierVector;
        public Vector3 OriginalTweenMotionModifier => m_tweenMotionModifierVector;
        public MonoTweenerSettings OriginalMoveTween => m_moveTween;
        public float OriginalMoveDamage => m_moveDamage;
        public List<AnimatorParameterControl> OriginalAnimatorParameters => m_animatorParameters;
        public List<ActionHitBox> OriginalHitboxes => m_hitboxes;
        public List<ActionSoundEffect> OriginalSoundEffects => m_soundEffects;
        public List<ActionMoveEffect> OriginalActionMoveEffects => m_actionMoveEffects;
        public List<ActionEffect> OriginalEffects => m_effects;
        public List<InputFollowingOption> OriginalFollowingActions => m_followingActions;
        public float OriginalFullSnapRadius => m_fullSnapRadius;
        public float OriginalLightSnapAngle => m_lightSnapAngle;
        public float OriginalLightSnapRadius => m_lightSnapRadius;
        public bool OriginalIgnoreHeight => m_ignoreHeight;

        #endregion

        [NonSerialized] protected DamageableRegistry m_damageableRegistry;
        [NonSerialized] protected SoundService m_soundService;
        [NonSerialized] protected InputService m_inputService;

#if UNITY_EDITOR
        public virtual GameObject Owner
        {
            set => SetOwner(value);
        }

        protected void SetOwner(GameObject value)
        {
            GameObject source = PrefabUtility.GetCorrespondingObjectFromSource(value);
            if (source == null)
            {
                return;
            }

            m_owner = source.GetComponent<CombatEntity>();

            m_animator = m_owner.Animator;//value.GetComponent<CombatEntity>().Animator;
        }
#endif

        public override void Initialize()
        {
            m_damageableRegistry = ServiceLocator.Get<DamageableRegistry>();
            m_soundService = ServiceLocator.Get<SoundService>();
            m_inputService = ServiceLocator.Get<InputService>();
            if (m_animator == null)
            {
                if (m_owner == null)
                {
                    //Debug.LogError($"Problematic move: {name}");
                }
                else
                {
                    m_animator = m_owner.Animator;
                }
            }

            UpdateRuntimeValues();
        }
        
        [ContextMenu("Update Runtime Values")]
        protected virtual void UpdateRuntimeValues()
        {
            if (!Application.isPlaying)
            {
                return;
            }
            
            r_displayName = m_displayName;
            r_ariel = m_ariel;
            r_moveDuration = m_moveDuration;
            r_minimalMoveTime = m_minimalMoveTime;
            r_animationMotionModifier = m_rootMotionModifierVector;
            r_tweenMotionModifier = m_tweenMotionModifierVector;
            r_moveTween = m_moveTween;
            r_moveDamage = m_moveDamage;
            r_fullSnapRadius = m_fullSnapRadius;
            r_lightSnapRadius = m_lightSnapRadius;
            r_lightSnapAngle = m_lightSnapAngle;
            r_ignoreHeight = m_ignoreHeight;

            r_animatorParameters = m_animatorParameters.CopyFromByValue();
            r_hitboxes = m_hitboxes.CopyFromByValue();
            r_soundEffects = m_soundEffects.CopyFromByValue();
            r_actionMoveEffects = m_actionMoveEffects.CopyFromByValue();
            r_effects = m_effects.CopyFromByValue();
            r_followingActions = m_followingActions.CopyFromByValue();

            if (m_animator == null)
            {
                Debug.LogError($"Move {name} has no m_animator");   
            }
            else
            {
                foreach (AnimatorParameterControl parameter in r_animatorParameters)
                {
                    parameter.Initialize(m_animator);
                }
            }
        }

        /// <summary>
        /// Perform action, returns cancel action
        /// </summary>
        /// <returns></returns>
        public virtual void Perform(PerformParams performParams, CancellationToken cancellationToken = default(CancellationToken), CancellationTokenSource cancelSource = null)
        {
            PerformData performData = ConstructPerformData();
            Perform(performParams, performData, cancellationToken, cancelSource);
        }

        protected virtual void Perform(PerformParams performParams, PerformData performData,
            CancellationToken cancellationToken = default(CancellationToken),
            CancellationTokenSource cancelSource = null)
        {
            PerformProcess performProcess = new PerformProcess();

            performParams.owner.AnimationMotionModifier = performData.animationMotionModifier;
            performParams.owner.MoveTweenMotionModifier = performData.tweenMotionModifier;

            //performProcess.tween = performData.moveTween?.RunDirected(performParams.owner.Animator.transform, performParams.owner.OwnForward); //TODO: fix with incrementals?

            MonoProcess.New(cancellationToken: cancellationToken).Do(() =>
            {
                performProcess.tween = performData.moveTween?.RunDirected(
                    performParams.owner.Animator.transform.parent,
                    performParams.owner.OwnForward,
                    null,
                    null,
                    performParams.owner.GetCombatTarget,
                    performParams.owner.FaceDirection); //TODO: fix with incrementals?
            });

            MonoProcess onCompleteProcess =
                MonoProcess.NewManual()
                    .WaitFor(() => cancellationToken.IsCancellationRequested, performData.moveDuration)
                    .Do(() =>
                    {
                        performProcess.Stop();

                        if (!cancellationToken.IsCancellationRequested)
                        {
                            ResetAnimationParameters(performParams.anim, performData.animatorParameters);
                            performParams.onComplete?.Invoke();
                        }
                    });

            //Combos
            performProcess.followingOptionsUnregister = RegisterFollowingActions(performParams, performProcess,
                performData, onCompleteProcess, performParams.initiatingPlayerAction, cancellationToken);

            //Sound timings
            performProcess.soundProcess = MonoProcess.NewManual(cancellationToken);
            performProcess.soundEffectsStop = AddSounds(performParams, performProcess, performData, cancellationToken);

            //Cancelation
            performProcess.ProcessCancel = () =>
            {
                performProcess.Stop();
                cancelSource.Cancel();
                ResetAnimationParameters(performParams.anim, performData.animatorParameters);
            };

            //Hitbox timings
            performProcess.hitboxProcess = MonoProcess.NewManual(cancellationToken);
            AddHitboxes(performParams, performProcess, performData, cancellationToken);

            //Effect timings
            performProcess.effectProcess = MonoProcess.NewManual(cancellationToken);
            performProcess.effectsStop = AddEffects(performParams, performProcess, performData, cancellationToken);

            //Move effect timings
            performProcess.moveEffectProcess = MonoProcess.NewManual(cancellationToken);
            AddMoveEffects(performParams, performProcess, performData, cancellationToken);

            performParams.owner.MoveStarted(this,
                performProcess.ProcessCancel,
                performParams.initiatingPlayerAction);

            //Run
            performProcess.Run();
            onCompleteProcess.Run();

            MonoProcess.New().Do(() =>
            {
                foreach (AnimatorParameterControl parameter in performData.animatorParameters)
                {
                    parameter.ApplyParameter(performParams.anim);
                }
            });
        }

        protected virtual PerformData ConstructPerformData()
        {
            return new PerformData
            {
                moveDuration = m_moveDuration,
                minimalMoveTime = m_minimalMoveTime,
                animationMotionModifier = m_rootMotionModifierVector,
                tweenMotionModifier = m_tweenMotionModifierVector,
                moveTween = m_moveTween,
                moveDamage = m_moveDamage,
                animatorParameters = m_animatorParameters,
                hitboxes = m_hitboxes,
                soundEffects = m_soundEffects,
                effects = m_effects,
                actionMoveEffects = m_actionMoveEffects,
                followingActions = m_followingActions,
                fullSnapRadius = m_fullSnapRadius,
                lightSnapRadius = m_lightSnapRadius,
                lightSnapAngle = m_lightSnapAngle,
                ignoreHeight = m_ignoreHeight,
                inputTimeOffset = 0f,
            };
        }

        protected virtual Action AddEffects(PerformParams performParams, PerformProcess performProcess, PerformData performData, CancellationToken cancellationToken)
        {
            float effectTime = 0f;
            List<Action> cancelEffectsActions = new List<Action>();
            foreach (ActionEffect effect in performData.effects)
            {
                Action stopAction = 
                    () => {
                        if (effect.StopOnMoveEnd) {
                            performParams.effectholders[effect.ParticleSystemsParentIndex].Stop();
                        }
                    };
                cancelEffectsActions.Add(stopAction);
                performProcess.effectProcess
                    .WaitForSeconds(effect.EffectStartTime - effectTime)
                    .Do(() => {
                        stopAction.Invoke();
                        try
                        {
                            performParams.effectholders[effect.ParticleSystemsParentIndex].Play();
                        }
                        catch (Exception e)
                        {
                            Debug.LogError($"broken effect on {name} number: {effect.ParticleSystemsParentIndex} out of {performParams.effectholders.Count}");
                            throw;
                        }
                    });
                effectTime = effect.EffectStartTime;
            }
            
            return () =>
            {
                foreach (Action action in cancelEffectsActions)
                {
                    action?.Invoke();
                }
            };
        }

        protected virtual void AddHitboxes(PerformParams performParams, PerformProcess performProcess, PerformData performData, CancellationToken cancellationToken)
        {
            float damage = performData.moveDamage;
            float hitboxTime = 0f;
            foreach (ActionHitBox hitbox in performData.hitboxes)
            {
                performProcess.hitboxProcess
                    .WaitForSeconds(hitbox.HitBoxStartTime - hitboxTime)
                    .Do(() =>
                    {
                        float damageDealt = performParams.owner.BaseDamage * hitbox.DamageMultiplier * damage;
                        m_damageableRegistry.ApplyDamageArea(
                            performParams.hitboxColliders[hitbox.HitBoxColliderIndex],
                            damageDealt,
                            performData.DirectHit,
                            performParams.owner,
                            hitbox,
                            damageablesHit =>
                            {
                                performParams.owner.AttackLanded(this, damageablesHit);
                            });
                    });
                hitboxTime = hitbox.HitBoxStartTime;
            }
        }

        protected virtual Action AddSounds(PerformParams performParams, PerformProcess performProcess,
            PerformData performData, CancellationToken cancellationToken)
        {
            float soundTime = 0f;

            List<Action> cancelSoundActions = new List<Action>();

            foreach (ActionSoundEffect se in performData.soundEffects)
            {
                performProcess.soundProcess
                    .WaitForSeconds(se.SoundStartTime - soundTime)
                    .Do(() => //Play sound
                    {
                        PlaySoundEffect(se, cancelSoundActions);
                    });
                soundTime = se.SoundStartTime;
            }

            if (cancelSoundActions.Count > 0)
            {
                return null;
            }

            return () =>
            {
                foreach (Action action in cancelSoundActions)
                {
                    action?.Invoke();
                }
            };
        }

        protected void PlaySoundEffect(ActionSoundEffect soundEffect, List<Action> cancelSoundActions)
        {
            if (soundEffect.StopOnCancel)
            {
                try
                {
                    Action cancel = m_soundService.PlayClipWithCallback(soundEffect.SoundSource.Value);
                    cancelSoundActions.Add(
                        () => { cancel?.Invoke(); });
                }
                catch (Exception e)
                {
                    Debug.LogError($"No sound on {name}");
                }
            }
            else if (soundEffect.FadeDuration > 0.01f && soundEffect.FixedDuration < 0.01f)
            {
                try
                {
                    Action cancel = m_soundService.PlayClipWithCallback(soundEffect.SoundSource.Value);
                    cancelSoundActions.Add(
                        () => { cancel?.Invoke(); });
                }
                catch (Exception e)
                {
                    Debug.LogError($"No sound on {name}");
                }
            }
            else if(soundEffect.FixedDuration > 0f)
            {
                Action cancel = m_soundService.PlayClipWithCallback(soundEffect.SoundSource.Value);
                
                MonoProcess process = MonoProcess.WaitForSecondsProcess(soundEffect.FixedDuration)
                    .Do(() => cancel?.Invoke());
                            
                cancelSoundActions.Add(
                    () =>
                    {
                        process.Stop();
                        cancel?.Invoke();
                    });
            }
            else
            {
                if (soundEffect.SoundSource != null)
                {
                    m_soundService.PlayClipWithCallback(soundEffect.SoundSource.Value);   
                }
                else
                {
                    Debug.Log($"move {name} has a null sound source");
                }
            }
        }

        protected virtual void AddMoveEffects(PerformParams performParams, PerformProcess performProcess, PerformData performData, CancellationToken cancellationToken)
        {
            float moveEffectTime = 0f;
            foreach (ActionMoveEffect me in performData.actionMoveEffects)
            {
                performProcess.moveEffectProcess
                    .WaitForSeconds(me.EffectTime - moveEffectTime)
                    .Do(() =>
                    {
                        me.MoveEffect.Activate(performParams.owner);
                    });
                moveEffectTime = me.EffectTime;
            }
        }
        
        //TODO: refactor into data and execution classes
        protected virtual Action RegisterFollowingActions(PerformParams performParams, PerformProcess performProcess, PerformData performData, MonoProcess onCompleteProcess, PlayerInputActions initiatingPlayerAction, CancellationToken cancellationToken)
        {
            Action unregister = null;
            try
            {
                if (performData.followingActions.Count < 1)
                {
                    return null;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"error on {this.name} : {e}");
            }

            PlayerInputActions lastInput = initiatingPlayerAction; //Used as empty
            PlayerInputTypes lastInputState = PlayerInputTypes.Held; //Used as empty

            void BufferUpdateAction(PlayerInputActions input, PlayerInputTypes state)
            {
                lastInput = input;
                lastInputState = state;
            }

            List<(InputFollowingOption, UnityAction)> followingActionsList =
                new List<ValueTuple<InputFollowingOption, UnityAction>> ();

            foreach (InputFollowingOption followingAction in performData.followingActions)
            {
                //Can maybe not include hold?
                followingActionsList.Add((followingAction,
                    () =>
                    {
                        float offset = 
                            followingAction.InputAction == PlayerInputActions.Dodge
                                || (followingAction.InputType == PlayerInputTypes.Held
                                    && !performParams.prevoiousMoveJust)?
                                0f :
                                performData.inputTimeOffset;
                        
                        if (onCompleteProcess.ElapsedTime >= followingAction.MinTransitionTime - offset
                            && onCompleteProcess.ElapsedTime <= followingAction.MaxTransitionTime - offset
                            && followingAction.Action.Valid
                            && performParams.owner.CanPerform(followingAction.InputAction, followingAction.Action)
                            && m_inputService.InputEnabled
                            && !cancellationToken.IsCancellationRequested)
                        {
                            PerformFollowingOption(followingAction.Action, performParams, performProcess,
                                performData, followingAction.InputAction);
                        }
                    }));
            }

            void RegisterInputListener((InputFollowingOption option, UnityAction performAction) followingAction, PlayerInputActions action)
            {
                InputVoidEvent actionCallback = (InputVoidEvent) m_inputService.InGameInputs[action];
                    
                if (followingAction.option.InputType == PlayerInputTypes.Started)
                {
                    UnityAction detach = actionCallback.Performed.AddCancellableListener(followingAction.performAction, cancellationToken);
                    unregister += () => detach.Invoke();
                    var detach2 = actionCallback.ContextualEvent.AddCancellableListener(BufferUpdateAction, cancellationToken);
                    unregister += () => detach2.Invoke(PlayerInputActions.Default, PlayerInputTypes.Ended);
                }
                else if (followingAction.option.InputType == PlayerInputTypes.Ended)
                {
                    UnityAction detach = actionCallback.Canceled.AddCancellableListener(followingAction.performAction, cancellationToken);
                    unregister += () => detach.Invoke();
                    var detach2 = actionCallback.ContextualEvent.AddCancellableListener(BufferUpdateAction, cancellationToken);
                    unregister += () => detach2.Invoke(PlayerInputActions.Default, PlayerInputTypes.Ended);
                }
            }

            foreach ((InputFollowingOption option, UnityAction performAction) followingAction in followingActionsList)
            {
                RegisterInputListener(followingAction, followingAction.option.InputAction);    
            }
            
            MonoProcess inputBufferProcess = MonoProcess.NewManual(cancellationToken);
            
            Dictionary<float, List<InputFollowingOption>> minimalTimingsDict = new Dictionary<float, List<InputFollowingOption>>();
            foreach (InputFollowingOption followingAction in performData.followingActions)
            {
                float offset = 
                    followingAction.InputAction == PlayerInputActions.Dodge
                    || (followingAction.InputType == PlayerInputTypes.Held
                        && !performParams.prevoiousMoveJust)?
                        0f :
                        performData.inputTimeOffset;
                
                if (!minimalTimingsDict.ContainsKey(followingAction.MinTransitionTime - offset))
                {
                    minimalTimingsDict[followingAction.MinTransitionTime - offset] = new List<InputFollowingOption>();
                }
                
                minimalTimingsDict[followingAction.MinTransitionTime - offset].Add(followingAction);
            }

            List<float> bufferTimes = minimalTimingsDict.Keys.ToList();
            bufferTimes.Sort();
            
            float inputMinTime = 0.01f;
            
            //Initial removal of initiating input
            inputBufferProcess
                .WaitForSeconds(inputMinTime)
                .Do(() =>{
                    lastInputState = PlayerInputTypes.Held;
                });
            
            foreach (float time in bufferTimes)
            {
                inputBufferProcess
                    .WaitForSeconds(time - inputMinTime)
                    .Do(() => {
                        foreach (InputFollowingOption option in minimalTimingsDict[time])
                        {
                            if ((option.InputType == PlayerInputTypes.Started && lastInput == option.InputAction && lastInputState == PlayerInputTypes.Started)
                                || (option.InputType == PlayerInputTypes.Held && ((InputVoidEvent) m_inputService.InGameInputs[option.InputAction]).CurrentValue)
                                || (option.InputType == PlayerInputTypes.Ended && !((InputVoidEvent) m_inputService.InGameInputs[option.InputAction]).CurrentValue))
                                
                            {
                                if (option.Action.Valid &&
                                    performParams.owner.CanPerform(option.InputAction, option.Action))
                                {
                                    if (m_inputService.InputEnabled)
                                    {
                                        PerformFollowingOption(option.Action, performParams, performProcess,
                                            performData, option.InputAction);
                                    }
                                }
                            }
                        }
                    });
                inputMinTime = time;
            }

            performProcess.inputBufferProcess = inputBufferProcess;

            return unregister;
            //return () => UnregisterInputFollowingActions(performParams.owner, followingActionsList, BufferUpdateAction);
        }

        protected virtual void PerformFollowingOption(ActionData followingAction, PerformParams performParams, PerformProcess performProcess, PerformData performData, PlayerInputActions initiatingPlayerAction)
        {
            //Add bool if previous on beat
            if (performProcess.transitioningToFollowup)
            {
                return;
            }

            performProcess.transitioningToFollowup = true;
            performParams.initiatingPlayerAction = initiatingPlayerAction;
            performProcess.ProcessCancel.Invoke();
            CancellationTokenSource source = new CancellationTokenSource();
            followingAction.Perform(performParams, source.Token, cancelSource: source);
        }

        private void UnregisterInputFollowingActions(CombatEntity owner,
            List<(InputFollowingOption followingOptions, UnityAction)> followingActions,
            UnityAction<PlayerInputActions, PlayerInputTypes> bufferUpdateAction)
        {
            //return;
            if (followingActions == null) { return; }
            
            void UnRegisterInputListener((InputFollowingOption, UnityAction) followingAction, PlayerInputActions action)
            {
                InputVoidEvent actionCallback = (InputVoidEvent) m_inputService.InGameInputs[action];
                if (followingAction.Item1.InputType == PlayerInputTypes.Started)
                {
                    actionCallback.Performed.RemoveListener(followingAction.Item2);
                    actionCallback.ContextualEvent.RemoveListener(bufferUpdateAction);
                }
                else if (followingAction.Item1.InputType == PlayerInputTypes.Ended || followingAction.Item1.InputType == PlayerInputTypes.Held)
                {
                    actionCallback.Canceled.RemoveListener(followingAction.Item2);
                    actionCallback.ContextualEvent.RemoveListener(bufferUpdateAction);
                }
            }
 
            foreach ((InputFollowingOption, UnityAction) followingAction in followingActions)
            {
                UnRegisterInputListener(followingAction, followingAction.Item1.InputAction);
            }
        }
        
        private void ResetAnimationParameters(Animator anim, List<AnimatorParameterControl> parameterControls)
        {
            foreach (AnimatorParameterControl parameter in parameterControls)
            {
                if (parameter.ResetParameter)
                {
                    parameter.ResetParameterControl(anim);
                }
            }
        }

        protected virtual void OnValidate()
        {
            if (m_animator == null && m_owner != null)
            {
                m_animator = m_owner.Animator;
            }

            foreach (ActionSoundEffect effect in m_soundEffects)
            {
                effect.Validate();
            }
            
            UpdateRuntimeValues();
        }

        [Serializable]
        public class ActionHitBox : IConcreteCloneable<ActionHitBox> //TODO: add validator
        {
            [SerializeField] protected int m_hitBoxColliderIndex;
            [SerializeField] protected float m_hitBoxStartTime;
            [SerializeField][Range(0f, 1f)] protected float m_damageMultiplier;
            [SerializeField] protected MonoTweenerSettings m_hitMoveEffect;
            [SerializeField] protected HitSeverity m_hitSeverity;
            [SerializeField] protected bool m_faceDamageSource = true;

            public int HitBoxColliderIndex => m_hitBoxColliderIndex;
            public float HitBoxStartTime => m_hitBoxStartTime;
            public float DamageMultiplier => m_damageMultiplier;
            public MonoTweenerSettings HitMoveEffect => m_hitMoveEffect;
            public HitSeverity HitSeverity => m_hitSeverity;
            
            public bool FaceDamageSource
            {
                get => m_faceDamageSource;
                set => m_faceDamageSource = value;
            }

            public static ActionHitBox DirectHitbox(HitSeverity severity, MonoTweenerSettings tween, bool faceDamageSource)
            {
                ActionHitBox hitbox = new ActionHitBox
                {
                    m_hitSeverity = severity,
                    m_hitMoveEffect = tween,
                    m_faceDamageSource = faceDamageSource
                };
                return hitbox;
            }

            public ActionHitBox Copy()
            {
                ActionHitBox copy = new ActionHitBox
                {
                    m_hitBoxColliderIndex = m_hitBoxColliderIndex,
                    m_hitBoxStartTime = m_hitBoxStartTime,
                    m_damageMultiplier = m_damageMultiplier,
                    m_hitMoveEffect = m_hitMoveEffect,
                    m_hitSeverity = m_hitSeverity,
                    m_faceDamageSource = m_faceDamageSource
                };

                return copy;
            }

            public object Clone()
            {
                return Copy();
            }
        }

        [Serializable]
        public class ActionEffect : IConcreteCloneable<ActionEffect>
        {
            [SerializeField] protected int m_particleSystemsParentIndex;
            [SerializeField] protected float m_effectStartTime;
            [SerializeField] protected bool m_stopOnMoveEnd = true;

            public int ParticleSystemsParentIndex => m_particleSystemsParentIndex;
            public float EffectStartTime => m_effectStartTime;
            public bool StopOnMoveEnd => m_stopOnMoveEnd;

            public ActionEffect Copy()
            {
                ActionEffect copy = new ActionEffect
                {
                    m_particleSystemsParentIndex = m_particleSystemsParentIndex,
                    m_effectStartTime = m_effectStartTime,
                    m_stopOnMoveEnd = m_stopOnMoveEnd,
                };
                return copy;
            }
            
            public object Clone()
            {
                return Copy();
            }
        }
        
        [Serializable]
        public class ActionSoundEffect : IConcreteCloneable<ActionSoundEffect>
        {
            [SerializeField] protected InterfaceReference<ISoundSource> m_soundSource;
            [SerializeField] protected float m_soundStartTime;
            [SerializeField] protected bool m_stopOnCancel = false;
            [SerializeField] protected float m_fixedDuration;
            [SerializeField] protected float m_fadeDuration;
            public float SoundStartTime => m_soundStartTime;
            public bool StopOnCancel => m_stopOnCancel;
            public float FadeDuration => m_fadeDuration;
            public float FixedDuration => m_fixedDuration;
            public InterfaceReference<ISoundSource> SoundSource => m_soundSource;

            public void Validate()
            {
                //if (SoundSource.UnderlyingValue == null && m_soundEffect != null)
                //{
                //    if (m_soundEffect.Sound != null)
                //    {
                //        SoundSource.UnderlyingValue = m_soundEffect.Sound;
                //        SoundSource.Value = (ISoundSource)m_soundEffect.Sound;
                //    }
                //    else
                //    {
                //        SoundSource.UnderlyingValue = m_soundEffect;
                //    }
                //}
            }
            
            public ActionSoundEffect Copy()
            {
                ActionSoundEffect copy = new ActionSoundEffect
                {
                    //m_soundEffect = m_soundEffect,
                    m_soundStartTime = m_soundStartTime,
                    m_stopOnCancel = m_stopOnCancel,
                    m_fadeDuration = m_fadeDuration,
                    m_fixedDuration = m_fixedDuration,
                    m_soundSource = m_soundSource,
                };
                return copy;
            }

            public object Clone()
            {
                return Copy();
            }
        }
        
        [Serializable]
        public class ActionMoveEffect : IConcreteCloneable<ActionMoveEffect>
        {
            [SerializeField] protected MoveEffect m_moveEffect;
            [SerializeField] protected float m_effectTime;

            public MoveEffect MoveEffect => m_moveEffect;
            public float EffectTime => m_effectTime;
            
            public ActionMoveEffect Copy()
            {
                ActionMoveEffect copy = new ActionMoveEffect
                {
                    m_moveEffect = m_moveEffect,
                    m_effectTime = m_effectTime,
                };
                return copy;
            }
            
            public object Clone()
            {
                return Copy();
            }
        }
        
        [Serializable]
        public class InputFollowingOption : IConcreteCloneable<InputFollowingOption>
        {
            [SerializeField] private float minTransitionTime;
            [SerializeField] private float maxTransitionTime;

            [SerializeField] private PlayerInputActions inputAction;            
            [SerializeField] private PlayerInputTypes inputType = PlayerInputTypes.Started;
            
            [SerializeField] private ActionData action;
            
            public float MinTransitionTime => minTransitionTime;
            public float MaxTransitionTime => maxTransitionTime;
            public PlayerInputActions InputAction => inputAction;
            public PlayerInputTypes InputType => inputType;
            public ActionData Action => action;
            
            public InputFollowingOption Copy()
            {
                InputFollowingOption copy = new InputFollowingOption
                {
                    minTransitionTime = minTransitionTime,
                    maxTransitionTime = maxTransitionTime,
                    inputAction = inputAction,
                    inputType = inputType,
                    action = action,
                };
                return copy;
            }
            
            public object Clone()
            {
                return Copy();
            }
        }

        public struct PerformParams
        {
            public CombatEntity owner;
            public List<VisualEffectHolder> effectholders;
            public List<ColliderReference> hitboxColliders;
            public Action onComplete;
            public Animator anim;
            public PlayerInputActions initiatingPlayerAction;
            public bool prevoiousMoveJust;
        }

        public void SetValuesFrom(ActionData source)
        {
            m_displayName = source.m_displayName;
            m_ariel = source.m_ariel;
            m_moveDuration = source.m_moveDuration;
            m_minimalMoveTime = source.m_minimalMoveTime;
            m_rootMotionModifierVector = source.m_rootMotionModifierVector;
            m_moveTween = source.m_moveTween;
            m_moveDamage = source.m_moveDamage;
            m_animatorParameters = source.m_animatorParameters.CopyFromByValue();
            m_hitboxes = source.m_hitboxes.CopyFromByValue();
            m_soundEffects = source.m_soundEffects.CopyFromByValue();
            m_actionMoveEffects = source.m_actionMoveEffects.CopyFromByValue();
            m_effects = source.m_effects.CopyFromByValue();
            m_followingActions = source.m_followingActions.CopyFromByValue();
            m_fullSnapRadius = source.m_fullSnapRadius;
            m_lightSnapRadius = source.m_lightSnapRadius;
            m_lightSnapAngle = source.m_lightSnapAngle;
        }

        protected class PerformProcess
        {
            public Action ProcessCancel;
            public Action followingOptionsUnregister;
            public Action soundEffectsStop;
            public Action effectsStop;
            public MonoProcess hitboxProcess;
            public MonoProcess effectProcess;
            public MonoProcess inputBufferProcess;
            public MonoProcess soundProcess;
            public MonoProcess moveEffectProcess;
            public Tween tween;
            public bool transitioningToFollowup = false;

            public void Run()
            {
                hitboxProcess?.Run();
                effectProcess?.Run();
                soundProcess?.Run();
                inputBufferProcess?.Run();
                moveEffectProcess?.Run();
            }
            
            public void Stop()
            {
                followingOptionsUnregister?.Invoke();
                soundEffectsStop?.Invoke();
                effectsStop?.Invoke();
                hitboxProcess?.Stop();
                effectProcess?.Stop();
                soundProcess?.Stop();
                inputBufferProcess?.Stop();
                moveEffectProcess?.Stop();
                tween?.Kill();
                transitioningToFollowup = true;
            }
        }
        
        protected class PerformData
        {
            public float moveDuration;
            public float minimalMoveTime;
            public Vector3 animationMotionModifier;
            public Vector3 tweenMotionModifier;
            public MonoTweenerSettings moveTween;
            public float moveDamage;
            public List<AnimatorParameterControl> animatorParameters;
            public List<ActionHitBox> hitboxes;
            public List<ActionSoundEffect> soundEffects;
            public List<ActionEffect> effects;
            public List<ActionMoveEffect> actionMoveEffects;
            public List<InputFollowingOption> followingActions;
            public float fullSnapRadius;
            public float lightSnapRadius;
            public float lightSnapAngle;
            public bool ignoreHeight;
            public bool DirectHit;
            public float inputTimeOffset;
        }
    }

    public enum HitSeverity
    {
        NonDisruptive = 0,
        LightHit_1 = 1,
        LightHit_2 = 2,
        HeavyHit_1 = 3,
        HeavyHit_2 = 4,
        LaunchHit = 5,
        SmashHit = 6,
        KnockbackHit = 7,
    }
}