namespace RibCageGames.Editor.Editor
{
    using RibCageGames.Editor;
    using UnityEditor;
    using UnityEngine;
    
    [CustomPropertyDrawer(typeof(ReferenceListElementAttribute))]
    public class ReferenceListElementPropertyDrawer : PropertyDrawer
    {
        
        protected virtual ReferenceListElementAttribute Attribute
        {
            get { return (ReferenceListElementAttribute)attribute; }
        }
        
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            position = EditorGUI.PrefixLabel(position, GUIUtility.GetControlID(FocusType.Passive), label);            
            position.height = EditorGUIUtility.singleLineHeight;
            
            SerializedProperty enumProp = property.FindPropertyRelative(Attribute.EnumVarName);
            SerializedProperty referenceProp = property.FindPropertyRelative(Attribute.ReferenceVarName);


            Rect enumPropPosition = new Rect(position.x, position.y,
                position.width / 2f, position.height);
            
            Rect referencePropPosition = new Rect(
                enumPropPosition.x + enumPropPosition.width,
                position.y,
                position.width / 2f, position.height);
            
            //Object reference = property.serializedObject.FindProperty(Attribute.ReferenceVarName).objectReferenceValue;
            //referenceProp.objectReferenceValue = reference;

            EditorGUI.ObjectField(referencePropPosition, referenceProp, GUIContent.none);
            
            //EditorGUI.EnumPopup(enumPropPosition, enumProp.enumValueIndex);
            
            if (GUI.Button(enumPropPosition,
                (enumProp.enumNames.Length > enumProp.enumValueIndex) ? enumProp.enumNames[enumProp.enumValueIndex] : "Select value",
                EditorStyles.popup))
            {
                GenericMenu menu = new GenericMenu();
                foreach (string parameter in enumProp.enumNames)
                {
                    menu.AddItem(new GUIContent(parameter),
                        parameter == enumProp.enumNames[enumProp.enumValueIndex],
                        enumValue =>
                        {
                            enumProp.enumValueIndex = System.Array.IndexOf(enumProp.enumNames, parameter);
                            //enumProp.enumValueIndex = IndexOf(enumProp.enumNames, parameter);
                            property.serializedObject.ApplyModifiedProperties(); //Should be specifically string prop?
                            //idProp.intValue = Animator.StringToHash(nameProp.stringValue);
                            //idProp.serializedObject.ApplyModifiedProperties();
                            //Debug.Log("Set hash for parameter " + nameProp.stringValue + " to: " + idProp.intValue);
                        }
                        , parameter);
                }

                menu.ShowAsContext();
            }
        }
    }
    
    [CustomPropertyDrawer(typeof(ReferenceListAttribute))]
    public class ReferenceListPropertyDrawer : PropertyDrawer
    {
        
        protected virtual ReferenceListAttribute Attribute
        {
            get { return (ReferenceListAttribute)attribute; }
        }
        
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label) {
            
            EditorGUI.BeginProperty(position, label, property);

            float currentheight = position.y;
            
            //position = EditorGUI.PrefixLabel(position, GUIUtility.GetControlID(FocusType.Passive), label);            
            //position.height = EditorGUIUtility.singleLineHeight;
            
            SerializedProperty listProp = property.FindPropertyRelative(Attribute.ListVarName);
            //EditorGUI.PropertyField(position, listProp, true);
            
            //listProp.GetArrayElementAtIndex()
            //listProp.MoveArrayElement()
            //Debug.Log("Position is: " + position);
            //Debug.Log($"Drawing list: {listProp.name} at position: {position.x} at y: {currentheight += EditorGUIUtility.singleLineHeight} , {position.width} , {position.height}");
            
            //List<int> presentValues = new List<int>();
            //List<int> valuesToRemove = new List<int>();
            //Sort and clean list
            //for (int i = 0; i < listProp.arraySize; i++) {
            //    int enumValue = listProp.GetArrayElementAtIndex(i).FindPropertyRelative(Attribute.EnumVarName).enumValueIndex;
            //    if (presentValues.Contains(enumValue)) {
            //        valuesToRemove.Add(i);
            //        //listProp.DeleteArrayElementAtIndex(i);
            //    } else {
            //        presentValues.Add(enumValue);
            //    }
            //}
//
            //foreach (int i in valuesToRemove) {
            //    listProp.DeleteArrayElementAtIndex(i);
            //}
            
            //List<int> values = new List<int>();
            //for (int i = 0; i < listProp.arraySize; i++)
            //{
            //    values.Add(listProp.GetArrayElementAtIndex(i).FindPropertyRelative(Attribute.EnumVarName).enumValueIndex);
            //}
//
            //int temp;
            //for (int j = 0; j <= listProp.arraySize - 2; j++)
            //{
            //    for (int i = 0; i <= listProp.arraySize - 2; i++)
            //    {
            //        if (values[i] > values[i + 1])
            //        {
            //            temp = a[i + 1];
            //            a[i + 1] = a[i];
            //            a[i] = t;
            //            listProp.MoveArrayElement()
            //        }
            //    } 
            //}
            
            EditorGUI.PropertyField(position, listProp, true);
            //EditorGUI.PropertyField(new Rect (
            //        position.x, currentheight += EditorGUIUtility.singleLineHeight, position.width, position.height),
            //    listProp);

            //if (listProp.isExpanded) {
            //    for (int i = 0; i < listProp.arraySize; i++) {
            //        currentheight += EditorGUIUtility.singleLineHeight;
            //        SerializedProperty element = listProp.GetArrayElementAtIndex(i);
            //        if (element != null && element.isExpanded) {
            //            currentheight += EditorGUIUtility.singleLineHeight * 2.5f;
            //            DrawElementProperty(new Rect(position.x, currentheight, position.width, EditorGUIUtility.singleLineHeight), element);
            //        }
            //    }
            //}
        
            currentheight += EditorGUIUtility.singleLineHeight * listProp.arraySize;
            //position.height += EditorGUIUtility.singleLineHeight * listProp.arraySize;

            //EditorGUI.LabelField(new Rect(position.x, currentheight, position.width, position.height), "End of list");
            EditorGUI.EndProperty();
        }

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {

            SerializedProperty listProp = property.FindPropertyRelative(Attribute.ListVarName);

            float totalHeight = EditorGUI.GetPropertyHeight(property, label);
            if (listProp.isExpanded)
            {
                
                totalHeight += EditorGUIUtility.singleLineHeight * (listProp.arraySize * 1.6f + 2f);// * 0.3f;
                for (int i = 0; i < listProp.arraySize; i++) {
                    if (listProp.GetArrayElementAtIndex(i).isExpanded) {
                        totalHeight += EditorGUIUtility.singleLineHeight * 1.9f;
                    }
                }
            }

            //Debug.Log("Height was: " + totalHeight);
            return totalHeight;
        }

        private void DrawElementProperty(Rect position, SerializedProperty property)
        {
            SerializedProperty enumProp = property.FindPropertyRelative(Attribute.EnumVarName);
            SerializedProperty referenceProp = property.FindPropertyRelative(Attribute.ReferenceVarName);


            Rect enumPropPosition = new Rect(position.x, position.y,
                position.width / 2f, position.height);

            Rect referencePropPosition = new Rect(
                enumPropPosition.x + enumPropPosition.width,
                position.y,
                position.width / 2f, position.height);

            //Object reference = property.serializedObject.FindProperty(Attribute.ReferenceVarName).objectReferenceValue;
            //referenceProp.objectReferenceValue = reference;

            EditorGUI.ObjectField(referencePropPosition, referenceProp, GUIContent.none);

            //EditorGUI.EnumPopup(enumPropPosition, enumProp.enumValueIndex);

            if (GUI.Button(enumPropPosition,
                (enumProp.enumNames.Length > enumProp.enumValueIndex)
                    ? enumProp.enumNames[enumProp.enumValueIndex]
                    : "Select value",
                EditorStyles.popup))
            {
                GenericMenu menu = new GenericMenu();
                foreach (string parameter in enumProp.enumNames)
                {
                    menu.AddItem(new GUIContent(parameter),
                        parameter == enumProp.enumNames[enumProp.enumValueIndex],
                        enumValue =>
                        {
                            enumProp.enumValueIndex = System.Array.IndexOf(enumProp.enumNames, parameter);
                            //enumProp.enumValueIndex = IndexOf(enumProp.enumNames, parameter);
                            property.serializedObject.ApplyModifiedProperties(); //Should be specifically string prop?
                            //idProp.intValue = Animator.StringToHash(nameProp.stringValue);
                            //idProp.serializedObject.ApplyModifiedProperties();
                            //Debug.Log("Set hash for parameter " + nameProp.stringValue + " to: " + idProp.intValue);
                        }
                        , parameter);
                }

                menu.ShowAsContext();
            }
        }
    }
}