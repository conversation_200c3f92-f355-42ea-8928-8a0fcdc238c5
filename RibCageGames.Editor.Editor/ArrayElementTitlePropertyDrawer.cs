namespace RibCageGames.Editor.Editor
{
    using UnityEditor;
    using UnityEngine;

    [CustomPropertyDrawer(typeof(ArrayElementTitleAttribute))]
    public class ArrayElementTitlePropertyDrawer : PropertyDrawer
    {
        public override float GetPropertyHeight(SerializedProperty property,
            GUIContent label)
        {
            return EditorGUI.GetPropertyHeight(property, label, true);
        }

        protected virtual ArrayElementTitleAttribute Attribute => (ArrayElementTitleAttribute) attribute;

        SerializedProperty m_titleNameProp;

        public override void OnGUI(Rect position,
            SerializedProperty property,
            GUIContent label)
        {
            string fullPathName = property.propertyPath + "." + Attribute.VarName;
            m_titleNameProp = property.serializedObject.FindProperty(fullPathName);
            string newlabel = GetTitle();
            if (string.IsNullOrEmpty(newlabel))
                newlabel = label.text;
            EditorGUI.PropertyField(position, property, new GUIContent(newlabel, label.tooltip), true);
        }

        private string GetTitle()
        {
            switch (m_titleNameProp.propertyType)
            {
                case SerializedPropertyType.Generic:
                    break;
                case SerializedPropertyType.Integer:
                    return m_titleNameProp.intValue.ToString();
                case SerializedPropertyType.Boolean:
                    return m_titleNameProp.boolValue.ToString();
                case SerializedPropertyType.Float:
                    return m_titleNameProp.floatValue.ToString();
                case SerializedPropertyType.String:
                    return m_titleNameProp.stringValue;
                case SerializedPropertyType.Color:
                    return m_titleNameProp.colorValue.ToString();
                case SerializedPropertyType.ObjectReference:
                    return m_titleNameProp.objectReferenceValue != null
                        ? m_titleNameProp.objectReferenceValue.ToString()
                        : "Null";
                case SerializedPropertyType.LayerMask:
                    break;
                case SerializedPropertyType.Enum:
                    if (m_titleNameProp.enumValueIndex >= 0 &&
                        m_titleNameProp.enumValueIndex < m_titleNameProp.enumNames.Length) { return m_titleNameProp.enumNames[m_titleNameProp.enumValueIndex]; }
                    else { return "Enum value not set"; }
                case SerializedPropertyType.Vector2:
                    return m_titleNameProp.vector2Value.ToString();
                case SerializedPropertyType.Vector3:
                    return m_titleNameProp.vector3Value.ToString();
                case SerializedPropertyType.Vector4:
                    return m_titleNameProp.vector4Value.ToString();
                case SerializedPropertyType.Rect:
                    break;
                case SerializedPropertyType.ArraySize:
                    break;
                case SerializedPropertyType.Character:
                    break;
                case SerializedPropertyType.AnimationCurve:
                    break;
                case SerializedPropertyType.Bounds:
                    break;
                case SerializedPropertyType.Gradient:
                    break;
                case SerializedPropertyType.Quaternion:
                    break;
                default:
                    break;
            }

            return "";
        }
    }
}