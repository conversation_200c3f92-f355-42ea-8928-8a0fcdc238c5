namespace RibCageGames.Editor.Editor
{
    using RibCageGames.Editor;
    using UnityEditor;
    using UnityEditorInternal;
    using UnityEngine;

    [CustomPropertyDrawer(typeof(ReorderableListAttribute))]
    public class ReorderableListPropertyDrawer : PropertyDrawer
    {

        //protected override Animator GetAnimator(Rect position, SerializedProperty property) {
        //    return (Animator) property.serializedObject.FindProperty(Attribute.AnimatorVarName).objectReferenceValue;
        //}

        //protected virtual ReorderableListAttribute Attribute
        //{
        //    get { return (ReorderableListAttribute)attribute; }
        //}

        private ReorderableList list;
        //private SerializedProperty listData;

        //void OnEnable() {
        //
        //    list = new ReorderableList(serializedObject.FindProperty("testList"));
        //    listData = serializedObject.FindProperty("listData");
        //}

        //public override OnInspectorGUI() {
        //
        //    serializedObject.Update();
        //
        //    list.draggable = listData.FindPropertyRelative("draggable").boolValue;
        //    list.DoLayoutList();
        //
        //    serializedObject.ApplyModifiedProperties();
        //}

        // Draw the property inside the given rect
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            if (list == null)
            {
                list = new ReorderableList(property.serializedObject, property,
                    true, true, true, true);
            }

            property.serializedObject.Update();

            //list.draggable = listData.FindPropertyRelative("draggable").boolValue;
            list.DoLayoutList();

            property.serializedObject.ApplyModifiedProperties();

        }
    }
}