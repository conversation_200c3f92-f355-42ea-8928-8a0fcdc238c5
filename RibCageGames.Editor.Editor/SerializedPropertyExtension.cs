namespace RibCageGames.Editor.Editor
{
	using System;
	using System.Collections.Generic;
	using System.Linq.Expressions;
	using UnityEditor;


	public static class SerializedPropertyExtension
	{

		public static SerializedProperty GetProperty<T>(this SerializedObject obj, Expression<Func<T>> exp)
		{
			MemberExpression e = ExpressionUtils.GetMemberExpression(exp);
			List<string> components = new List<string>(2);
			while (e != null)
			{
				components.Insert(0, e.Member.Name);
				e = GetMemberExpression(e.Expression);
			}

			SerializedProperty sp = obj.FindProperty(components[1]);
			for (int i = 2; i < components.Count; i++)
			{
				sp = sp.FindPropertyRelative(components[i]);
			}

			return sp;
		}

		public static SerializedProperty GetProperty<T>(this SerializedProperty obj, Expression<Func<T>> exp)
		{
			MemberExpression e = ExpressionUtils.GetMemberExpression(exp);
			List<string> components = new List<string>(2);
			while (e != null)
			{
				components.Insert(0, e.Member.Name);
				e = GetMemberExpression(e.Expression);
			}

			SerializedProperty sp = obj.FindPropertyRelative(components[1]);
			for (int i = 2; i < components.Count; i++)
			{
				sp = sp.FindPropertyRelative(components[i]);
			}

			return sp;
		}

		private static MemberExpression GetMemberExpression(Expression exp)
		{
			// Try casting to member expression
			MemberExpression memberExpression = exp as MemberExpression;
			if (memberExpression != null) return memberExpression;

			try
			{
				// Try direct casting to Unary expresion (may throw exceptions)
				memberExpression = ((UnaryExpression) exp).Operand as MemberExpression;
			}
			catch (Exception exception)
			{
				// ignored
			}

			return memberExpression;
		}
	}
}