namespace RibCageGames.Music
{
    using UnityEngine;
    using UnityEngine.Audio;

    [CreateAssetMenu(fileName = "Sequence", menuName = "RibCageGames/CellosLastConcert/MusicTrack")]
    public class MusicTrack : ScriptableObject
    {
        public virtual AudioClip Track => m_track;
        public AudioMixerGroup MixerGroup => m_mixerGroup;
        public virtual MusicTrack NextTrack {
            get
            {
                Debug.Log($"m_nextTrack is {m_nextTrack} and ShouldTransition is {ShouldTransition}");
                if (m_nextTrack == null || !ShouldTransition)
                {
                    return this;
                }
                else
                {
                    return m_nextTrack;
                }
            }
        }
        
        public virtual int LengthInBeats => m_lengthInBeats;
        
        public bool ShouldTransition
        {
            set { m_shouldTransition = value;}
            protected get { return m_shouldTransition; }
        }
        
        [SerializeField] protected AudioClip m_track;
        [SerializeField] protected MusicTrack m_nextTrack;
        [SerializeField] private int m_lengthInBeats;
        [SerializeField] private bool m_shouldTransition;
        [SerializeField] private AudioMixerGroup m_mixerGroup;
    }
}