namespace RibCageGames.Music
{
    using System;
    using UnityEngine;

    [Serializable]
    public class GlobalShaderColorMusicVisual : MusicVisualBase
    {
        [SerializeField] private string m_shaderParameter;
        [SerializeField] private Gradient m_colorGradient;

        [HideInInspector] [SerializeField] private int m_shaderParameterHash;

        public override void Init()
        {
            m_shaderParameterHash = Shader.PropertyToID(m_shaderParameter);
            base.Init();
        }
        
        protected override void UpdateVisual(float value)
        {
            Shader.SetGlobalColor(m_shaderParameterHash,
                m_colorGradient.Evaluate(value));
        }
    }
}