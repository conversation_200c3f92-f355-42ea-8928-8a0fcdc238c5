namespace RibCageGames.Music
{
    using Animation;
    using System;
    using UnityEngine;

    [Serializable]
    public class AnimatorMusicVisual : MusicVisualBase
    {
        [SerializeField] private Animator m_animator;
        [SerializeField][Range(0f, 1f)] private float m_threshold;
        [SerializeField][AnimatorParameterControl("m_animator")]
        private AnimatorParameterControl m_animatorParameter;
        [SerializeField] private bool m_useDynamicValue;

        public override void Init()
        {
            base.Init();
            m_animatorParameter.Initialize(m_animator);
        }

        protected override void UpdateVisual(float value)
        {
            if (m_useDynamicValue)
            {
                if (value >= m_threshold)
                {
                    m_animatorParameter.SetValue(value, true);
                }  
            }
            else
            {
                bool thresholdTriggered =
                    m_lastValue < m_threshold &&
                    value >= m_threshold;
                if (thresholdTriggered)
                {
                    m_animatorParameter.ApplyParameter();
                }   
            }
        }

        public override void Validate(MusicVisualBase prev, GameObject target)
        {
            m_animator = target.GetComponent<Animator>();
            base.Validate(prev, target);
        }
    }
}