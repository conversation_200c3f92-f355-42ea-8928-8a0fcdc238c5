namespace RibCageGames.Music
{
    using UnityEngine;

    public class PositionMusicVisual : MusicVisualBase
    {
        [SerializeField] private Transform m_transformTarget;
        [SerializeField] private Vector3 m_minimalValue;
        [SerializeField] private Vector3 m_maximalValue;

        private Vector3 m_originalPos;

        public override void Init()
        {
            m_originalPos = m_transformTarget.localPosition;
        }

        protected override void UpdateVisual(float value)
        {
            m_transformTarget.localPosition = m_originalPos + Vector3.Lerp(
                                         m_minimalValue,
                                         m_maximalValue,
                                         value);
        }
        
        public override void Validate(MusicVisualBase prev, GameObject target)
        {
            m_transformTarget = target.transform;
            base.Validate(prev, target);
        }
    }
}