namespace RibCageGames.Music
{
    using System;
    using UnityEngine;

    [Serializable]
    public class ShaderColorSkinnedMusicVisual : MusicVisualBase
    {
        [SerializeField] private SkinnedMeshRenderer m_targetRenderer;
        [SerializeField] private string m_shaderParameter;
        [SerializeField] private Gradient m_colorGradient;

        [HideInInspector] [SerializeField] private int m_shaderParameterHash;

        public override void Init()
        {
            m_shaderParameterHash = Shader.PropertyToID(m_shaderParameter);
            base.Init();
        }
        
        protected override void UpdateVisual(float value)
        {
            m_targetRenderer.material
                .SetColor(m_shaderParameterHash,
                    m_colorGradient.Evaluate(value));
        }

        public override void Validate(MusicVisualBase prev, GameObject target)
        {
            m_targetRenderer = target.GetComponent<SkinnedMeshRenderer>();
            base.Validate(prev, target);
        }
    }
}