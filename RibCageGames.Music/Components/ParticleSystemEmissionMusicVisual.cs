namespace RibCageGames.Music
{
    using System;
    using UnityEngine;

    [Serializable]
    public class ParticleSystemEmissionMusicVisual : MusicVisualBase
    {
        [SerializeField] private ParticleSystem m_targetParticleSystem;
        [SerializeField] private int m_maxEmission;

        protected override void UpdateVisual(float value)
        {
            ParticleSystem.EmissionModule emission = m_targetParticleSystem.emission;
            emission.rateOverTime = (int) (value * m_maxEmission);
        }

        public override void Validate(MusicVisualBase prev, GameObject target)
        {
            m_targetParticleSystem = target.GetComponent<ParticleSystem>();
            base.Validate(prev, target);
        }
    }
}