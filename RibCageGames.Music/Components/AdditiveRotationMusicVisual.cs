namespace RibCageGames.Music
{
    using UnityEngine;

    public class AdditiveRotationMusicVisual : MusicVisualBase
    {
        [SerializeField] private Transform m_transformTarget;
        [SerializeField] private Vector3 m_rotationVector;

        protected override void UpdateVisual(float value)
        {
            m_transformTarget.Rotate(value * m_rotationVector);
        }
        
        public override void Validate(MusicVisualBase prev, GameObject target)
        {
            m_transformTarget = target.transform;
            base.Validate(prev, target);
        }
    }
}