namespace RibCageGames.Music
{
    using System;
    using UnityEngine;

    [Serializable]
    public class RotationMusicVisual : MusicVisualBase
    {
        [SerializeField] private Transform m_transformTarget;
        [SerializeField] private Vector3 m_minimalValue;
        [SerializeField] private Vector3 m_maximalValue;

        protected override void UpdateVisual(float value)
        {
            m_transformTarget.localRotation =
                Quaternion.Slerp(Quaternion.Euler(m_minimalValue), Quaternion.Euler(m_maximalValue), value);
        }

        public override void Validate(MusicVisualBase prev, GameObject target)
        {
            m_transformTarget = target.transform;
            base.Validate(prev, target);
        }
    }
}