namespace RibCageGames.Music
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using RibCageGames.Base;
    using RibCageGames.Editor;
    using UnityEngine;
    using UnityEngine.Events;

    public class MonoMusicVisualizer : MonoBehaviour
    {        
        [SerializeReference] private List<MusicVisualBase> m_musicVisuals;
        
        [NonSerialized] private List<Type> m_visualTypes;
        
        private void Start()
        {
            MonoService monoService = ServiceLocator.Get<MonoService>();
            MusicSystem musicSystem = ServiceLocator.Get<MusicSystem>();
            BeatSystem beatSystem = ServiceLocator.Get<BeatSystem>();

            foreach (MusicVisualBase visual in m_musicVisuals)
            {
                visual.Init();
                visual.RegisterVisualUpdate(monoService, beatSystem, musicSystem);
            }
        }

        private void OnValidate()
        {

            if (m_visualTypes == null)
            {
                m_visualTypes = MusicVisualBase.GetAllVisualTypes();
            }
            
            for (int i = 0; i < m_musicVisuals.Count; i++)
            {
                MusicVisualBase visual = m_musicVisuals[i];
                if (visual != null && visual.TypeIndex >= 0 && visual.TypeIndex != m_visualTypes.IndexOf(visual.GetType()))
                {
                    ChangeVisualType(i, visual.TypeIndex, m_visualTypes[visual.TypeIndex]);
                }
                else if(visual == null)
                {
                    ChangeVisualType(i, -1, typeof(MusicVisualBase));
                }
            }
        }

        private void OnDestroy()
        {
            MonoService monoService = ServiceLocator.Get<MonoService>();

            if (monoService != null)
            {
                foreach (MusicVisualBase visual in m_musicVisuals)
                {
                    visual.UnRegisterVisualUpdate(monoService);
                }
            }
        }

        private void ChangeVisualType(int index, int typeIndex, Type visualType)
        {
            if (m_musicVisuals.Count > index && m_musicVisuals[index] != null)
            {
                MusicVisualBase old = m_musicVisuals[index];
                m_musicVisuals[index] = Activator.CreateInstance(visualType) as MusicVisualBase;
                m_musicVisuals[index].TypeIndex = typeIndex;
                m_musicVisuals[index].Validate(old, old.Target ? old.Target : gameObject);
            }
            else
            {
                m_musicVisuals[index] = Activator.CreateInstance(visualType) as MusicVisualBase;
                m_musicVisuals[index].TypeIndex = typeIndex;
                m_musicVisuals[index].Validate(null, gameObject);
            }
        }
    }

    [Serializable]
    public class MusicVisualization
    {
        //Value selection
        [SerializeField] private MusicDataType m_musicDataType;
        [SerializeField] [HideInInspector] private bool m_hideChannel;

        [ConditionalHide("m_hideChannel")] [SerializeField]
        private int m_selectedChannel = 0;

        [SerializeField] private VisualizationType m_visualType;
        [SerializeField] private AnimationCurve m_growth = AnimationCurve.Linear(0f, 0f, 1f, 1f);
        [Range(0f, 0.99f)] [SerializeField] private float m_damping = 0f;

        //Vector3 parameters
        [SerializeField] [HideInInspector] private bool m_hideVector;

        [ConditionalHide("m_hideVector")]
        [SerializeField]
        private VectorModule m_vectorModule;

        //Shader color parameters
        [SerializeField] [HideInInspector] private bool m_hideGradient;

        [ConditionalHide("m_hideGradient")] [SerializeField]
        private ColorModule m_colorModule;

        
        [SerializeField]
        private ShaderModule m_shaderModule;

        private float m_lastValue = 0f;
        private UnityAction<float> m_registeredUpdate;
        private Func<float> m_valueResolver;
        private Action<float> m_visualUpdater;

        public void RegisterVisualUpdate(MonoService monoService, BeatSystem beatSystem, MusicSystem musicSystem)
        {
            switch (m_musicDataType)
            {
                case MusicDataType.DirectValue:
                    m_valueResolver = () => ProcessValue(musicSystem.LevelValues[m_selectedChannel]);
                    break;
                case MusicDataType.Buffered:
                    m_valueResolver = () => ProcessValue(musicSystem.LevelBuffers[m_selectedChannel]);
                    break;
                case MusicDataType.Amplitude:
                    m_valueResolver = () => ProcessValue(musicSystem.Amplitude);
                    break;
                case MusicDataType.BufferedAmplitude:
                    m_valueResolver = () => ProcessValue(musicSystem.AmplitudeBuffer);
                    break;
                case MusicDataType.MainBeatValue:
                    m_valueResolver = () => ProcessValue(beatSystem.MainBeatValue);
                    break;
                case MusicDataType.BeatValue:
                    m_valueResolver = () => ProcessValue(beatSystem.BeatValue);
                    break;
                case MusicDataType.HalfBeatValue:
                    m_valueResolver = () => ProcessValue(beatSystem.HalfBeatValue);
                    break;
                case MusicDataType.BeatPercentage:
                    m_valueResolver = () => ProcessValue(beatSystem.VisualBeatPercentage);
                    break;
                case MusicDataType.MainBeatPercentage:
                    m_valueResolver = () => ProcessValue(beatSystem.MainBeatPercentage);
                    break;
                case MusicDataType.InputAccuracy:
                    m_valueResolver = () => ProcessValue(beatSystem.OnBeatAccuracy);
                    break;
                case MusicDataType.RandomValue:
                    m_valueResolver = () => ProcessValue(UnityEngine.Random.value);
                    break;
            }

            switch (m_visualType)
            {
                case VisualizationType.ShaderColor:
                    m_visualUpdater = (float value) =>
                    {
                        Shader.SetGlobalColor(m_shaderModule.ShaderParameterHash,
                            m_colorModule.ColorGradiant.Evaluate(value));
                    };
                    break;
                case VisualizationType.ShaderVector:
                    m_visualUpdater = (float value) =>
                    {
                        Shader.SetGlobalVector(m_shaderModule.ShaderParameterHash,
                            Vector3.Lerp(
                                m_vectorModule.MinVectorValue,
                                m_vectorModule.MaxVectorValue,
                                value));
                    };
                    break;
            }

            m_registeredUpdate = (float deltaTime) => { m_visualUpdater?.Invoke(m_valueResolver()); };

            monoService.OnUpdate.AddListener(m_registeredUpdate);
        }

        public void UnRegisterVisualUpdate(MonoService monoService)
        {
            monoService.OnUpdate.RemoveListener(m_registeredUpdate);
        }

        private float ProcessValue(float value) => DampenValue(m_growth.Evaluate(value));

        private float DampenValue(float newValue)
        {
            m_lastValue =
                newValue * (1f - m_damping)
                + m_damping * m_lastValue;

            return m_lastValue;
        }

        public void Validate(Transform mainTransform)
        {
            m_hideVector = m_visualType != VisualizationType.ShaderVector;
            m_hideGradient = m_visualType != VisualizationType.ShaderColor;
            if (m_hideGradient)
            {
                m_colorModule = null;
            }

            m_shaderModule.ShaderParameterHash = Shader.PropertyToID(m_shaderModule.ShaderParameter);

            m_hideChannel =
                m_musicDataType != MusicDataType.DirectValue &&
                m_musicDataType != MusicDataType.Buffered;
        }

        [Serializable]
        private class VectorModule
        {
            public Vector3 MinVectorValue;
            public Vector3 MaxVectorValue;
        }

        [Serializable]
        private class ColorModule
        {
            public Gradient ColorGradiant;
        }

        [Serializable]
        private class ShaderModule
        {
            public string ShaderParameter;
            [HideInInspector] public int ShaderParameterHash;
        }
    }

    public enum VisualizationType
    {
        ShaderColor = 4,
        ShaderVector = 5,
    }

    public enum MusicDataType
    {
        DirectValue = 0,
        Buffered = 1,
        Amplitude = 2,
        BufferedAmplitude = 3,
        MainBeatValue = 4,
        HalfBeatValue = 5,
        BeatPercentage = 6,
        MainBeatPercentage = 7,
        InputAccuracy = 8,
        BeatValue = 9,
        RandomValue = 99,
    }
}