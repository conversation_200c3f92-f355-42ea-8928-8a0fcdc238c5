using RibCageGames.Editor;

namespace RibCageGames.Music
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Base;
    using UnityEngine;
    using UnityEngine.Events;

    [Serializable]
    public class MusicVisualBase
    {
        public enum MusicDataType
        {
            DirectValue = 0,
            Buffered = 1,
            Amplitude = 2,
            BufferedAmplitude = 3,
            BeatValue = 4,
            HalfBeatValue = 5,
            BeatPercentage = 6,
            MainBeatPercentage = 7,
            RandomValue = 99,
        }

        //TODO: use mutator collection on value
        public enum DataMutatorType
        {
            Dampen,
            PlayerDistanceCurve,
        }

        //Used to allow type selection in the editor
        [HideInInspector] [SerializeField] private int m_concreteTypeIndex = -1;

        [SerializeField] private GameObject m_target;
        [SerializeField] private MusicDataType m_dataType;
        [SerializeField] private int m_selectedChannel = 0;

        [SerializeField] private bool m_dampenValue = true;

        [ConditionalShow("m_dampenValue")] [Range(0f, 0.99f)] [SerializeField]
        private float m_damping = 0.5f;

        [SerializeField] private bool m_playerDistanceMutateValue = false;

        [ConditionalShow("m_playerDistanceMutateValue")] [SerializeField]
        private AnimationCurve m_playerDistanceCurve;

        [ConditionalShow("m_playerDistanceMutateValue")] [SerializeField]
        private float m_playerDistanceMaxRadius;

        [SerializeField] private bool m_useGrowthCurve = true;

        [ConditionalShow("m_useGrowthCurve")] [SerializeField]
        private AnimationCurve m_growth = AnimationCurve.Linear(0f, 0f, 1f, 1f);

        [NonSerialized] protected float m_lastValue = 0f;
        [NonSerialized] private bool m_initialized = false;

        public GameObject Target => m_target;

        public int TypeIndex
        {
            get => m_concreteTypeIndex;
            set => m_concreteTypeIndex = value;
        }

        [NonSerialized] private UnityAction<float> m_registeredUpdate;
        [NonSerialized] private Func<float> m_valueResolver;
        [NonSerialized] private Action<float> m_visualUpdater;

        public void RegisterVisualUpdate(MonoService monoService, BeatSystem beatSystem, MusicSystem musicSystem)
        {
            switch (m_dataType)
            {
                case MusicDataType.DirectValue:
                    m_valueResolver = () => ProcessValue(musicSystem.LevelValues[m_selectedChannel]);
                    break;
                case MusicDataType.Buffered:
                    m_valueResolver = () => ProcessValue(musicSystem.LevelBuffers[m_selectedChannel]);
                    break;
                case MusicDataType.Amplitude:
                    m_valueResolver = () => ProcessValue(musicSystem.Amplitude);
                    break;
                case MusicDataType.BufferedAmplitude:
                    m_valueResolver = () => ProcessValue(musicSystem.AmplitudeBuffer);
                    break;
                case MusicDataType.BeatValue:
                    m_valueResolver = () => ProcessValue(beatSystem.VisualBeatValue);
                    break;
                case MusicDataType.HalfBeatValue:
                    m_valueResolver = () => ProcessValue(beatSystem.VisualHalfBeatValue);
                    break;
                case MusicDataType.BeatPercentage:
                    m_valueResolver = () => ProcessValue(beatSystem.VisualBeatPercentage);
                    break;
                case MusicDataType.MainBeatPercentage:
                    m_valueResolver = () => ProcessValue(beatSystem.VisualMainBeatPercentage);
                    break;
                case MusicDataType.RandomValue:
                    m_valueResolver = () => ProcessValue(UnityEngine.Random.value);
                    break;
            }

            m_visualUpdater = UpdateVisual;

            m_registeredUpdate = (float deltaTime) =>
            {
                float value = m_valueResolver();
                m_visualUpdater?.Invoke(value);
                m_lastValue = value;
            };

            monoService.OnUpdate.AddListener(m_registeredUpdate);
        }

        public void UnRegisterVisualUpdate(MonoService monoService)
        {
            if (m_registeredUpdate != null && monoService.OnUpdate != null && m_registeredUpdate != null)
            {
                monoService.OnUpdate.RemoveListener(m_registeredUpdate);
            }
        }

        private Func<float, float> ConstructProcessingFunction()
        {
            Func<float,float> func = (f) => f;
            
            if (m_useGrowthCurve)
            {
                var func1 = func;
                func = f => m_growth.Evaluate(func1(f));
            }
            
            if (m_dampenValue)
            {
                var func1 = func;
                func = f => DampenValue(func1(f));
            }
            
            //if (m_playerDistanceMutateValue)
            //{
            //    var func1 = func;
            //    func = f => PlayerDistanceMutateValue(func1(f));
            //}
            
            return func;
        }

        private float ProcessValue(float value) => DampenValue(m_growth.Evaluate(value));

        private float DampenValue(float newValue) => newValue * (1f - m_damping) + m_damping * m_lastValue;
        
        
        //private float PlayerDistanceMutateValue(float newValue) => newValue * 
        //                                                           m_playerDistanceCurve.Evaluate(
        //                                                               Mathf.Clamp(
        //                                                                   Vector3.Distance(m_target.transform, ),
        //                                                                   0f, m_playerDistanceMaxRadius));

        public static List<Type> GetAllVisualTypes()
        {
            return AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(assembly => assembly.GetTypes())
                .Where(type => type.IsSubclassOf(typeof(MusicVisualBase))).ToList();
        }

        protected virtual void UpdateVisual(float value) { }
        
        public virtual void Init() { }

        public virtual void Validate(MusicVisualBase prev, GameObject target)
        {
            m_target = target;
            if (prev != null)
            {
                m_dataType = prev.m_dataType;
                m_selectedChannel = prev.m_selectedChannel;
                m_growth = prev.m_growth;
                m_damping = prev.m_damping;
            }
        }
    }
}
