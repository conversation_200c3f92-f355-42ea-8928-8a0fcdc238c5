namespace RibCageGames.Music
{
    using System;
    using UnityEngine;

    [Serializable]
    public class ParticleSystemPlayMusicVisual : MusicVisualBase
    {
        [SerializeField] private ParticleSystem m_targetParticleSystem;
        [SerializeField][Range(0f, 1f)] private float m_threshold;

        protected override void UpdateVisual(float value)
        {
            bool thresholdTriggered =
                m_lastValue < m_threshold &&
                value >= m_threshold;
            if (thresholdTriggered)
            {
                m_targetParticleSystem.Play();
            }
        }

        public override void Validate(MusicVisualBase prev, GameObject target)
        {
            m_targetParticleSystem = target.GetComponent<ParticleSystem>();
            base.Validate(prev, target);
        }
    }
}