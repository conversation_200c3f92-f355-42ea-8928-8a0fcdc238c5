namespace RibCageGames.Music
{
    using Animation;
    using System;
    using UnityEngine;

    [Serializable]
    public class DynamicAnimatorMusicVisual : MusicVisualBase
    {
        [SerializeField] private Animator m_animator;
        [SerializeField][TargetedAnimatorParameter("m_animator")]
        private TargetedAnimatorParameter m_parameter;
        [SerializeField] private float m_minValue;
        [SerializeField] private float m_maxValue;

        protected override void UpdateVisual(float value)
        {
            m_animator.SetFloat(m_parameter.Value, Mathf.Lerp(m_minValue, m_maxValue, value));
        }

        public override void Validate(MusicVisualBase prev, GameObject target)
        {
            m_animator = target.GetComponent<Animator>();
            base.Validate(prev, target);
        }
    }
}