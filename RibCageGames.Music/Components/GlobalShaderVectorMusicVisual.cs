namespace RibCageGames.Music
{
    using System;
    using UnityEngine;

    [Serializable]
    public class GlobalShaderVectorMusicVisual : MusicVisualBase
    {
        [SerializeField] private string m_shaderParameter;
        [SerializeField] private Vector3 m_minimalValue;
        [SerializeField] private Vector3 m_maximalValue;

        [HideInInspector] [SerializeField] private int m_shaderParameterHash;

        public override void Init()
        {
            m_shaderParameterHash = Shader.PropertyToID(m_shaderParameter);
            base.Init();
        }

        protected override void UpdateVisual(float value)
        {
            Shader.SetGlobalVector(m_shaderParameterHash,
                Vector3.Lerp(
                    m_minimalValue,
                    m_maximalValue,
                    value));
        }
    }
}