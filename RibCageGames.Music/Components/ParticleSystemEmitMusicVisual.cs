namespace RibCageGames.Music
{
    using System;
    using UnityEngine;

    [Serializable]
    public class ParticleSystemEmitMusicVisual : MusicVisualBase
    {
        [SerializeField] private ParticleSystem m_targetParticleSystem;
        [SerializeField][Range(0f, 1f)] private float m_threshold;
        [SerializeField] private int m_maxEmission;

        protected override void UpdateVisual(float value)
        {
            bool thresholdTriggered =
                m_lastValue < m_threshold &&
                value >= m_threshold;
            if (thresholdTriggered)
            {
                m_targetParticleSystem.Emit(
                    (int) (value * m_maxEmission));
            }
        }

        public override void Validate(MusicVisualBase prev, GameObject target)
        {
            m_targetParticleSystem = target.GetComponent<ParticleSystem>();
            base.Validate(prev, target);
        }
    }
}