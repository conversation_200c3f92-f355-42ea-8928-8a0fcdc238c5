namespace RibCageGames.Music
{
    using System;
    using UnityEngine;

    [Serializable]
    public class ShaderVectorSkinnedMusicVisual : MusicVisualBase
    {
        [SerializeField] private SkinnedMeshRenderer m_targetRenderer;
        [SerializeField] private string m_shaderParameter;
        [SerializeField] private Vector3 m_minimalValue;
        [SerializeField] private Vector3 m_maximalValue;

        [HideInInspector] [SerializeField] private int m_shaderParameterHash;

        public override void Init()
        {
            m_shaderParameterHash = Shader.PropertyToID(m_shaderParameter);
            base.Init();
        }

        protected override void UpdateVisual(float value)
        {
            m_targetRenderer.material
                .SetVector(m_shaderParameterHash,
                    Vector3.Lerp(
                        m_minimalValue,
                        m_maximalValue,
                        value));
        }
        
        public override void Validate(MusicVisualBase prev, GameObject target)
        {
            m_targetRenderer = target.GetComponent<SkinnedMeshRenderer>();
            base.Validate(prev, target);
        }
    }
}