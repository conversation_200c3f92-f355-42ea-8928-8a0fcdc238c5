namespace RibCageGames.Music
{
    using System;
    using UnityEngine;

    [Serializable]
    public class ScaleMusicVisual : MusicVisualBase
    {
        [SerializeField] private Transform m_transformTarget;
        [SerializeField] private Vector3 m_minimalValue;
        [SerializeField] private Vector3 m_maximalValue;

        protected override void UpdateVisual(float value)
        {
            Vector3 res = Vector3.Lerp(m_minimalValue, m_maximalValue, value);
            if (!float.IsNaN(res.x))
            {
                m_transformTarget.localScale = Vector3.Lerp(m_minimalValue, m_maximalValue, value);   
            }
        }
        
        public override void Validate(MusicVisualBase prev, GameObject target)
        {
            m_transformTarget = target.transform;
            base.Validate(prev, target);
        }
    }
}