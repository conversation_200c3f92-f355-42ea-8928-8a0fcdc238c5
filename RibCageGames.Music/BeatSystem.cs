using System.Threading;

namespace RibCageGames.Music
{
    using System;
    using System.Runtime.InteropServices;
    using FMODUnity;
    using DG.Tweening;
    using Base;
    using UnityEngine;
    using UnityEngine.Events;
    using MonoUtils;
    using System.Collections.Generic;
    using Cysharp.Threading.Tasks;
    using fmod;

    [CreateAssetMenu(fileName = "BeatSystem", menuName = "RibCageGames/Services/BeatSystem")]
    public class BeatSystem : BaseService
    {
        const string visualOffsetSettingKey = "VisualOffsetSetting";
        
        [Serializable]
        public class OnBeatHandler : UnityEvent<bool> { }
        public class OnMainBeatHandler : UnityEvent { }
        public class OnHalfBeatHandler : UnityEvent { }
        public class OnBeatAttackScheduledHandler : UnityEvent<int, Action> { }

        /// <summary>
        /// Triggers when beat is hit with true for full beat and false for half beat
        /// </summary>
        public OnBeatHandler OnBeat { get; } = new OnBeatHandler();

        [SerializeField] private OnBeatHandler OnBeatEvent;
        
        public OnMainBeatHandler OnMainBeat { get; } = new OnMainBeatHandler();
        public OnHalfBeatHandler OnHalfBeat { get; } = new OnHalfBeatHandler();
        public OnBeatHandler VisualOnBeat { get; } = new OnBeatHandler();
        public OnMainBeatHandler VisualOnMainBeat { get; } = new OnMainBeatHandler();
        public OnHalfBeatHandler VisualOnHalfBeat { get; } = new OnHalfBeatHandler();
        public OnBeatAttackScheduledHandler OnBeatAttackScheduled { get; set; } = new OnBeatAttackScheduledHandler();
        public class OnInputWindowHandler : UnityEvent<bool> { }
        public OnInputWindowHandler OnInputWindow { get; } = new OnInputWindowHandler();

        /// <summary>
        /// Value of either beat 1 when exactly on beat
        /// </summary>
        public float BeatValue => Mathf.Max(MainBeatValue, HalfBeatLength);

        
        /// <summary>
        /// Value of a full beat 1 when exactly on beat
        /// </summary>
        public float MainBeatValue { get; private set; }

        /// <summary>
        /// Value of a full beat 1 when exactly visual offset away from full beat
        /// </summary>
        public float VisualBeatValue { get; private set; }

        /// <summary>
        /// Value of a half beat 1 when exactly on half beat
        /// </summary>
        public float HalfBeatValue { get; private set; }

        /// <summary>
        /// Value of a half beat 1 when exactly visual offset away from half beat
        /// </summary>
        public float VisualHalfBeatValue { get; private set; }

        public bool IsOnBeat => IsOnMainBeat || IsOnHalfBeat;
        

        public bool VisualIsOnBeat => IsOnVisualMainBeat || IsOnVisualHalfBeat;

        /// <summary>
        /// Is the system considering inputs as onBeat right now
        /// </summary>
        public bool IsOnMainBeat =>
            (m_inputTimeSinceLastBeat > BeatLength - (m_inputWindow / 2f) || //before beat
             m_inputTimeSinceLastBeat < m_inputWindow / 2f); //after beat

        public bool IsOnVisualMainBeat =>
            (m_visualTimeSinceLastBeat > BeatLength - (m_inputWindow / 2f) || //before beat
            (m_visualTimeSinceLastBeat < (m_inputWindow / 2f))); //after beat

        public bool IsOnHalfBeat =>
            (m_inputTimeSinceLastBeat > HalfBeatLength - (m_inputWindow / 2f)) && //before beat
            (m_inputTimeSinceLastBeat < HalfBeatLength + (m_inputWindow / 2f)); //after beat

        public bool IsOnVisualHalfBeat =>
            (m_visualTimeSinceLastBeat > HalfBeatLength - (m_inputWindow / 2f)) && //before beat
            (m_visualTimeSinceLastBeat < HalfBeatLength + (m_inputWindow / 2f)); //after beat

        /// <summary>
        /// Is the system within half an input window of a beat or half beat
        /// </summary>
        public bool IsInGracePeriod => 
            (!m_mainBeat && (m_inputTimeSinceLastBeat > BeatLength - ((m_inputWindow / 2f) + m_inputWindowGrace)))
        || (m_mainBeat && (m_inputTimeSinceLastBeat > HalfBeatLength - ((m_inputWindow / 2f) + m_inputWindowGrace)));

        public bool VisualIsInGracePeriod =>
            (!m_visualMainBeat && (m_visualTimeSinceLastBeat > BeatLength - ((m_inputWindow / 2f) + m_inputWindowGrace)))
            || (m_visualMainBeat && (m_visualTimeSinceLastBeat > HalfBeatLength - ((m_inputWindow / 2f) + m_inputWindowGrace)));

        /// <summary>
        /// The amount of beat that was traversed currently
        /// </summary>
        public float MainBeatPercentage => m_timeSinceLastBeat / BeatLength;

        /// <summary>
        /// The amount of beat that was traversed currently including the visual offset
        /// </summary>
        public float VisualMainBeatPercentage => m_visualTimeSinceLastBeat / BeatLength;

        public float BeatPercentage
        {
            get
            {
                float value = m_inputTimeSinceLastBeat / HalfBeatLength;
                
                if (value > 1f)
                {
                    value -= 1f;
                }

                return value;
            }
        }

        public float VisualBeatPercentage
        {
            get
            {
                float value = m_visualTimeSinceLastBeat / HalfBeatLength;
                
                if (value > 1f)
                {
                    value -= 1f;
                }

                return value;
            }
        }

        public bool FullBeatCloser
        {
            get
            {
                float distToHalf = m_inputTimeSinceLastBeat > HalfBeatLength ? m_inputTimeSinceLastBeat - HalfBeatLength : HalfBeatLength - m_inputTimeSinceLastBeat; // Branchless absolute value
                float distToBound = m_inputTimeSinceLastBeat < BeatLength - m_inputTimeSinceLastBeat ? m_inputTimeSinceLastBeat : BeatLength - m_inputTimeSinceLastBeat; // Min distance to 0 or x
    
                return distToHalf > distToBound;
            }
        }

        public float CurrentBeatDistance
        {
            get
            {
                if (FullBeatCloser)
                {
                    return m_inputTimeSinceLastBeat > HalfBeatLength
                        ? m_inputTimeSinceLastBeat - 2f * HalfBeatLength
                        : m_inputTimeSinceLastBeat;
                }
                else //Half beat closer
                {
                    return m_inputTimeSinceLastBeat - HalfBeatLength;
                }
            }
        }

        public float BeatLength => m_beatTime;

        public static float TempoMultiplier { get; private set; }

        //TODO: cache this
        public float HalfBeatLength { get; private set; }

        [SerializeField] private bool m_isOnBeat;
        [SerializeField] private bool m_isOnMainBeat;
        [SerializeField] private bool m_isOnHalfBeat;
        
        [SerializeField] private bool m_visualIsOnBeat;
        [SerializeField] private bool m_visualIsOnMainBeat;
        [SerializeField] private bool m_visualIsOnHalfBeat;
        
        [SerializeField] private float m_beatPercentageDisplay;
        [SerializeField] private float m_visualbeatPercentageDisplay;

        [SerializeField] private int m_tempo = 60;

        [Range(0.01f, 1f)] [SerializeField] private float m_beatTime = 0.5f;

        /// <summary>
        /// Used mainly for visual effects driven by beat to make it consistent
        /// </summary>
        [Range(0.01f, 1f)] [SerializeField] private float m_beatFadeDuration = 0.25f;

        /// <summary>
        /// Maybe shouldn't be here
        /// </summary>
        [Range(0.01f, 0.5f)] [SerializeField] private float m_inputWindow = 0.2f;
        [Range(0.01f, 0.5f)] [SerializeField] private float m_inputWindowGrace = 0.2f;
        [Range(0.01f, 0.5f)] [SerializeField] private float m_maxInputGrace = 0.2f;
        [SerializeField] private float m_minVisualOffset;
        [SerializeField] private float m_maxVisualOffset;
        
        [SerializeField] private float m_minInputOffset;
        [SerializeField] private float m_maxInputOffset;
        
        [SerializeField] private float m_minMetronomeOffset;
        [SerializeField] private float m_maxMetronomeOffset;
        [SerializeField] private AnimationCurve m_accuracyDropCurve;
        [SerializeField] private float m_accuracyDropCurveDuration;
        [SerializeField] private int m_autoTuneInputs;
        [Tooltip("In seconds")] [SerializeField] [Range(-0.15f, 0.15f)]
        private float m_defaultVisualOffset;
        
        [Tooltip("In seconds")] [SerializeField] [Range(-0.15f, 0.15f)]
        private float m_defaultMetronomeSoundOffset = -0.0667f;
        
        [SerializeField] [Range(0f, 1f)]
        private float m_defaultMasterVolume = 1f;
        [SerializeField] [Range(0f, 1f)]
        private float m_defaultMusicVolume = 1f;
        [SerializeField] [Range(0f, 1f)]
        private float m_defaultSFXVolume = 1f;
        [SerializeField] [Range(0f, 1f)]
        private float m_defaultFootstepVolume = 1f;
        [SerializeField] [Range(0f, 1f)]
        private float m_defaultMetronomeVolume = 0.5f;
        
        [SerializeField]
        private bool m_metronomeActive = false;
        
        [SerializeField] private float m_onBeatAccuracy;
        private float m_timeSinceLastInput;
        private float m_lastInputAccuracy;
        private bool m_visualBeatTriggered;
        [SerializeField] private int m_metronomeSelection = 0;

        [SerializeField] private fmodEmitterExclusiveGroup m_backgroundTrackGroup;
        [SerializeField] private fmodEmitterExclusiveGroup m_metronomeGroup;
        [SerializeField] private List<fmodGlobalParameterSetter> m_effectParameterSetters;

        private Dictionary<VolumeGroup, (float value, Action<float, bool> UpdateValue)> m_volumeDict;
        private Dictionary<EffectType, fmodGlobalParameterSetter> m_effectsDict;

        //private float m_metronomeSoundOffset;
        [SerializeField] private float m_visualOffset;
        
        [SerializeField] private float m_inputWindowOffset;
        
        //[SerializeField] private float m_inputWindowOffset;
        
        public float MetronomeSoundOffset => m_defaultMetronomeSoundOffset;
        public int MetronomeSelection => m_metronomeSelection;
        public bool MetronomeActive => m_metronomeActive;
        public float NormalizedMetronomeSoundOffset => ((m_defaultMetronomeSoundOffset - m_minMetronomeOffset) / (m_maxMetronomeOffset - m_minMetronomeOffset));
        
        public float VisualOffset => m_visualOffset;
        public float OnBeatAccuracy => m_onBeatAccuracy;
        public float InputWindow => m_inputWindow;
        public float InputWindowGrace => m_inputWindowGrace;
        public float NormalizedBeatVisualOffset => ((m_visualOffset - m_minVisualOffset) / (m_maxVisualOffset - m_minVisualOffset));
        
        public float NormalizedInputWindowOffset => ((m_inputWindowOffset - m_minInputOffset) / (m_maxInputOffset - m_minInputOffset));
        public float DefaultMetronomeVolume => m_defaultMetronomeVolume;
        
        [SerializeField] private float m_timeSinceLastBeat;
        [SerializeField] private float m_visualTimeSinceLastBeat;
        
        [SerializeField] private float m_inputTimeSinceLastBeat;

        private bool m_mainBeat = true;
        private bool m_visualMainBeat = true;
        private List<float> m_inputOffsets;

        // Variables that are modified in the callback need to be part of a seperate class.
        // This class needs to be 'blittable' otherwise it can't be pinned in memory.
        [StructLayout(LayoutKind.Sequential)]
        class TimelineInfo
        {
            public int currentMusicBar = 0;
            public FMOD.StringWrapper lastMarker = new FMOD.StringWrapper();
        }
        
        TimelineInfo m_timelineInfo;
        GCHandle m_timelineHandle;

        FMOD.Studio.EVENT_CALLBACK m_beatCallback;
        FMOD.Studio.EventInstance m_musicInstance;
        private FMOD.ChannelGroup m_masterChannelGroup;
        private int m_masterSampleRate;
        private double m_currentSamples = 0;
        private double m_currentBeatTime = 0f;

        private ulong m_dspClock;
        private ulong m_parentDSP;

        public override void Init(GameObject servicePrefab = null)
        {
            HalfBeatLength = m_beatTime * 0.5f;
            //Not sure why this is needed
            OnBeatAttackScheduled = new OnBeatAttackScheduledHandler();
            m_volumeDict = new Dictionary<VolumeGroup, (float value, Action<float, bool> UpdateValue)>
            {
                {VolumeGroup.Master, (m_defaultMasterVolume, SetMasterVolume)},
                {VolumeGroup.Music, (m_defaultMusicVolume, SetMusicVolume)},
                {VolumeGroup.AttackSFX, (m_defaultSFXVolume, SetSFXVolume)},
                {VolumeGroup.Footsteps, (m_defaultFootstepVolume, SetFootStepsVolume)},
                {VolumeGroup.Metronome, (m_defaultMetronomeVolume, SetMetronomeVolume)},
            };

            m_effectsDict = new Dictionary<EffectType, fmodGlobalParameterSetter>();
            foreach (fmodGlobalParameterSetter setter in m_effectParameterSetters)
            {
                m_effectsDict.Add(setter.EffectType, setter);
            }
        }

        public override void StartService(MonoInjector injector)
        {
            ServiceLocator.Get<MonoService>().OnUpdate.AddListener(ControlledUpdate);
            m_mainBeat = false;
            m_visualMainBeat = false;
            MainBeatValue = 0f;
            HalfBeatValue = 0f;
            m_timeSinceLastBeat = 0f;
            m_inputTimeSinceLastBeat = 0f;
            m_inputWindowOffset = 0f;
            m_visualOffset = m_defaultVisualOffset;//PlayerPrefs.GetFloat(visualOffsetSettingKey, m_defaultVisualOffset);
            m_visualTimeSinceLastBeat = m_visualOffset;
            TempoMultiplier = 60f / m_tempo;
            m_inputOffsets = new List<float>();
            //m_metronomeSoundOffset = m_defaultMetronomeSoundOffset;
            m_backgroundTrackGroup.OnTrackChanged.AddListener(RegisterBeatCallback);
            
            m_backgroundTrackGroup.OnTrackChanged.AddListener(PlayMetronomeTrack);
            
            RuntimeManager.CoreSystem.getMasterChannelGroup(out m_masterChannelGroup);
            RuntimeManager.CoreSystem.getSoftwareFormat(out m_masterSampleRate, out _, out _);

        }
        
        public void DemoReset()
        {
            m_metronomeGroup.CurrentEmitter.Stop();
            m_backgroundTrackGroup.CurrentEmitter.Stop();
            m_volumeDict = new Dictionary<VolumeGroup, (float value, Action<float, bool> UpdateValue)>
            {
                {VolumeGroup.Master, (m_defaultMasterVolume, SetMasterVolume)},
                {VolumeGroup.Music, (m_defaultMusicVolume, SetMusicVolume)},
                {VolumeGroup.AttackSFX, (m_defaultSFXVolume, SetSFXVolume)},
                {VolumeGroup.Footsteps, (m_defaultFootstepVolume, SetFootStepsVolume)},
                {VolumeGroup.Metronome, (m_defaultMetronomeVolume, SetMetronomeVolume)},
            };
            
            m_mainBeat = false;
            m_visualMainBeat = false;
            MainBeatValue = 0f;
            HalfBeatValue = 0f;
            m_timeSinceLastBeat = 0f;
            m_inputTimeSinceLastBeat = 0f;
            m_visualOffset = m_defaultVisualOffset;
            m_visualTimeSinceLastBeat = m_visualOffset;
            m_inputOffsets = new List<float>();
            m_inputWindowOffset = 0f;
        }

        public void SetMetronomeOffset(float value, bool playMetronome = false)
        {
            //float newValue = Mathf.Lerp(m_minMetronomeOffset, m_maxMetronomeOffset, value);

            //m_metronomeSoundOffset = newValue;

            //if (playMetronome)
            //{
            //    m_metronomeGroup.GroupEmitters[m_metronomeSelection].Stop();
            //    PlayMetronomeTrack(null);
            //}
        }
        
        private void SetMasterVolume(float value, bool b)
        {
            if (Mathf.Approximately(value, m_volumeDict[VolumeGroup.Master].value))
            {
                return;
            }

            (float value, Action<float, bool> UpdateValue) tuple = m_volumeDict[VolumeGroup.Master];
            tuple.value = value;
            m_volumeDict[VolumeGroup.Master] = tuple;

            m_volumeDict[VolumeGroup.Music].UpdateValue(m_volumeDict[VolumeGroup.Music].value, true);
            m_volumeDict[VolumeGroup.Metronome].UpdateValue(m_volumeDict[VolumeGroup.Metronome].value, true);
            m_volumeDict[VolumeGroup.AttackSFX].UpdateValue(m_volumeDict[VolumeGroup.AttackSFX].value, true);
            m_volumeDict[VolumeGroup.Footsteps].UpdateValue(m_volumeDict[VolumeGroup.Footsteps].value, true);
        }
        
        public void SetMetronomeActive(bool value)
        {
            m_metronomeActive = value;
            SetMetronomeVolume(m_volumeDict[VolumeGroup.Metronome].value, true);
        }
        
        private void SetMetronomeVolume(float value, bool force = false)
        {
            if (Mathf.Approximately(value, m_volumeDict[VolumeGroup.Metronome].value) && !force)
            {
                return;
            }
            (float value, Action<float, bool> UpdateValue) tuple = m_volumeDict[VolumeGroup.Metronome];
            tuple.value = value;
            m_volumeDict[VolumeGroup.Metronome] = tuple;
            m_metronomeGroup.CurrentEmitter.EventInstance.setVolume(value * m_volumeDict[VolumeGroup.Master].value * (m_metronomeActive ? 1f : 0f));
        }
        
        private void SetMusicVolume(float value, bool force = false)
        {
            if (Mathf.Approximately(value, m_volumeDict[VolumeGroup.Music].value) && !force)
            {
                return;
            }
            (float value, Action<float, bool> UpdateValue) tuple = m_volumeDict[VolumeGroup.Music];
            tuple.value = value;
            m_volumeDict[VolumeGroup.Music] = tuple;
            m_backgroundTrackGroup.CurrentEmitter.EventInstance.setVolume(value * m_volumeDict[VolumeGroup.Master].value);
        }
        
        private void SetSFXVolume(float value, bool force = false)
        {
            if (Mathf.Approximately(value, m_volumeDict[VolumeGroup.AttackSFX].value) && !force)
            {
                return;
            }
            (float value, Action<float, bool> UpdateValue) tuple = m_volumeDict[VolumeGroup.AttackSFX];
            tuple.value = value;
            m_volumeDict[VolumeGroup.AttackSFX] = tuple;
        }
        
        private void SetFootStepsVolume(float value, bool force = false)
        {
            if (Mathf.Approximately(value, m_volumeDict[VolumeGroup.Footsteps].value) && !force)
            {
                return;
            }
            (float value, Action<float, bool> UpdateValue) tuple = m_volumeDict[VolumeGroup.Footsteps];
            tuple.value = value;
            m_volumeDict[VolumeGroup.Footsteps] = tuple;
        }
        
        public void SetMetronomeSelection(int value, bool play)
        {
            m_metronomeSelection = value % m_metronomeGroup.GroupEmitters.Count;
            if (play)
            {
                StopCalibrationMetronome();
                PlayMetronomeTrack(null);
            }
        }
        
        public void StartCalibrationMetronome()
        {
            var emitter = m_metronomeGroup.GroupEmitters[m_metronomeSelection];
            emitter.Play();
            RegisterBeatCallback(emitter);
            SetMetronomeActive(true);
            PlayMetronomeTrack(null);
        }
        
        public void StopCalibrationMetronome()
        {
            m_metronomeGroup.GroupEmitters[m_metronomeSelection].Stop();
            SetMetronomeActive(false);
        }

        private CancellationTokenSource m_cancelMetronome;
        
        private void PlayMetronomeTrack(fmodEventEmitter emitter)
        {
            m_cancelMetronome?.Cancel();
            m_cancelMetronome = new CancellationTokenSource();

            //OnMainBeat.AddSingleUseListener(() =>
            //{
                OnMainBeat.AddSingleUseListener(async () =>
                {
                    float offset = m_defaultMetronomeSoundOffset;
                    if (offset < 0f)
                    {
                        offset = BeatLength + offset;
                    }

                    await UniTask.WaitForSeconds(offset);
                    m_metronomeGroup.GroupEmitters[m_metronomeSelection].Play();
                    SetMetronomeVolume(m_defaultMetronomeVolume, true);
                }, m_cancelMetronome.Token);
            //}, m_cancelMetronome.Token);
        }
        
        public void PlayMetronomeTrackDirect()
        {
            //m_metronomeGroup.GroupEmitters[m_metronomeSelection].Play();
        }

        private void RegisterBeatCallback(fmodEventEmitter eventEmitter)
        {
            m_timelineInfo = new TimelineInfo();
            // Explicitly create the delegate object and assign it to a member so it doesn't get freed
            // by the garbage collected while it's being used
            m_beatCallback = BeatEventCallback;
            m_musicInstance = eventEmitter.EventInstance;
            // Pin the class that will store the data modified during the callback
            m_timelineHandle = GCHandle.Alloc(m_timelineInfo, GCHandleType.Pinned);
            // Pass the object through the userdata of the instance
            m_musicInstance.setUserData(GCHandle.ToIntPtr(m_timelineHandle));

            m_musicInstance.setCallback(m_beatCallback, FMOD.Studio.EVENT_CALLBACK_TYPE.TIMELINE_BEAT | FMOD.Studio.EVENT_CALLBACK_TYPE.TIMELINE_MARKER);
        }
        
        private void OnDestroy()
        {
            m_musicInstance.setUserData(IntPtr.Zero);
            m_musicInstance.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
            m_musicInstance.release();
            m_timelineHandle.Free();
        }


        [AOT.MonoPInvokeCallback(typeof(FMOD.Studio.EVENT_CALLBACK))]
        private FMOD.RESULT BeatEventCallback(FMOD.Studio.EVENT_CALLBACK_TYPE type, IntPtr instancePtr,
            IntPtr parameterPtr)
        {
            FMOD.Studio.EventInstance instance = new FMOD.Studio.EventInstance(instancePtr);

            // Retrieve the user data
            IntPtr timelineInfoPtr;
            FMOD.RESULT result = instance.getUserData(out timelineInfoPtr);

            if (result != FMOD.RESULT.OK)
            {
                Debug.LogError("Timeline Callback error: " + result);
            }
            else if (timelineInfoPtr != IntPtr.Zero)
            {
                // Get the object to store beat and marker details
                GCHandle timelineHandle = GCHandle.FromIntPtr(timelineInfoPtr);
                TimelineInfo timelineInfo = (TimelineInfo)timelineHandle.Target;

                switch (type)
                {
                    case FMOD.Studio.EVENT_CALLBACK_TYPE.TIMELINE_BEAT:
                        var parameter = (FMOD.Studio.TIMELINE_BEAT_PROPERTIES)Marshal.PtrToStructure(parameterPtr,
                            typeof(FMOD.Studio.TIMELINE_BEAT_PROPERTIES));
                        timelineInfo.currentMusicBar = parameter.bar;
                        BeatReceived();
                        break;
                }
            }
            return FMOD.RESULT.OK;
        }

        private void BeatReceived()
        {
            m_timeSinceLastBeat = 0f;
            m_inputTimeSinceLastBeat = m_inputWindowOffset;

            if (m_inputTimeSinceLastBeat < 0f)
            {
                m_inputTimeSinceLastBeat += BeatLength;
            }
            else if (m_inputTimeSinceLastBeat > BeatLength)
            {
                m_inputTimeSinceLastBeat -= BeatLength;
            }
            UpdateBeatTime();
            
            MainBeatValue = 1f;
            DOTween.To(value => MainBeatValue = value, MainBeatValue, 0f, m_beatFadeDuration);
            
            m_mainBeat = true;
            OnBeat?.Invoke(true);
            OnBeatEvent?.Invoke(true);
            QueueVisualBeat(true);
            OnMainBeat?.Invoke();

            MonoProcess.WaitForSecondsProcess(HalfBeatLength).Do(() =>
            {
                //UpdateBeatTime();
                HalfBeatValue = 1f;
                DOTween.To(value => HalfBeatValue = value, HalfBeatValue, 0f, m_beatFadeDuration);
                //m_timeSinceLastBeat = 0f;
                m_mainBeat = false;
                OnBeat?.Invoke(false);
                OnBeatEvent?.Invoke(false);
                QueueVisualBeat(false);
                OnHalfBeat?.Invoke();
            });
        }

        public void DebugBeatInfo()
        {
            Debug.LogError($"m_timeSinceLastBeat {m_timeSinceLastBeat} IsOnMainBeat? {IsOnMainBeat} IsOnHalfBeat {IsOnHalfBeat}");
        }

        private async void QueueVisualBeat(bool mainBeat)
        {
            float visualOffset = 0f;

            //Need to leave early to be after beat when it hits
            //Need to leave late to not get to beat when it does
            //float flippedVisualOffset = - m_visualOffset;
            
            if (m_visualOffset < 0)
            {
                visualOffset = HalfBeatLength + m_visualOffset;
                mainBeat = !mainBeat;
            }
            else
            {
                visualOffset = m_visualOffset;
            }
            
            await UniTask.WaitForSeconds(visualOffset);
            TriggerVisualBeat(mainBeat);
        }
        
        private void TriggerVisualBeat(bool mainBeat)
        {
            if (mainBeat)
            {
                VisualBeatValue = 1f;
                m_visualMainBeat = true;
                DOTween.To(value => VisualBeatValue = value,
                    VisualBeatValue, 0f, m_beatFadeDuration);
                //m_visualTimeSinceLastBeat = 0f;
            }
            else
            {
                VisualHalfBeatValue = 1f;
                m_visualMainBeat = false;
                DOTween.To(value => VisualHalfBeatValue = value,
                    VisualHalfBeatValue, 0f, m_beatFadeDuration);
                
            }
            
            VisualOnBeat?.Invoke(mainBeat);
                
            if (mainBeat)
            {
                VisualOnMainBeat?.Invoke();
            }
            else
            {
                VisualOnHalfBeat?.Invoke();
            }
        }

        private void UpdateBeatTime()
        {
            float relativeTime = (float)(m_currentSamples / m_masterSampleRate);
            int beats = (int) Mathf.Floor(relativeTime / m_beatTime); 
            m_currentBeatTime = relativeTime - (m_beatTime * beats);
            //m_timeSinceLastBeat = ((float)m_currentBeatTime) * m_beatTime;
            SetVisualOffset();
        }

        private void SetVisualOffset()
        {
            m_visualTimeSinceLastBeat = m_timeSinceLastBeat - m_visualOffset;

            if (m_visualTimeSinceLastBeat < 0f)
            {
                m_visualTimeSinceLastBeat += BeatLength;
            }
            else if(m_visualTimeSinceLastBeat > BeatLength)
            {
                m_visualTimeSinceLastBeat -= BeatLength;
            }
        }
        
        private void UpdateDSPClock()
        {
            m_masterChannelGroup.getDSPClock(out m_dspClock, out m_parentDSP);

            m_currentSamples = m_dspClock;
        }

        private void ControlledUpdate(float deltaTime)
        {
            if (deltaTime < Mathf.Epsilon)
            {
                return;
            }
            
            UpdateDSPClock();

            m_timeSinceLastBeat += deltaTime;
            m_inputTimeSinceLastBeat += deltaTime;
            
            if (m_inputTimeSinceLastBeat < 0f)
            {
                m_inputTimeSinceLastBeat += BeatLength;
            }
            else if (m_inputTimeSinceLastBeat > BeatLength)
            {
                m_inputTimeSinceLastBeat -= BeatLength;
            }

            SetVisualOffset();
            
            if (m_timeSinceLastInput < m_accuracyDropCurveDuration)
            {
                m_timeSinceLastInput += deltaTime;
                m_onBeatAccuracy = m_lastInputAccuracy * m_accuracyDropCurve.Evaluate(m_timeSinceLastInput / m_accuracyDropCurveDuration);   
            }
            else
            {
                m_onBeatAccuracy = 0f;
            }
            
            m_isOnBeat = IsOnBeat;
            m_isOnMainBeat = IsOnMainBeat;
            m_isOnHalfBeat = IsOnHalfBeat;
            m_visualIsOnBeat = VisualIsOnBeat;
            m_visualIsOnMainBeat = IsOnVisualMainBeat;
            m_visualIsOnHalfBeat = IsOnVisualHalfBeat;
            m_beatPercentageDisplay = MainBeatPercentage;
            m_visualbeatPercentageDisplay = VisualMainBeatPercentage;

            return;
            
            m_timeSinceLastBeat += deltaTime;

            if (m_timeSinceLastBeat > m_beatTime)
            {
                m_timeSinceLastBeat -= m_beatTime;
            }
            
            //m_visualTimeSinceLastBeat = m_timeSinceLastBeat + m_visualOffset;
            
            if (!m_visualBeatTriggered && m_visualTimeSinceLastBeat >= HalfBeatLength)
            {
                m_visualBeatTriggered = true;
                
                if (m_visualMainBeat)
                {
                    VisualBeatValue = 1f;
                    DOTween.To(value => VisualBeatValue = value,
                        VisualBeatValue, 0f, m_beatFadeDuration);
                }
                else
                {
                    VisualHalfBeatValue = 1f;
                    DOTween.To(value => VisualHalfBeatValue = value,
                        VisualHalfBeatValue, 0f, m_beatFadeDuration);
                }
                
                VisualOnBeat?.Invoke(m_visualMainBeat);
                
                if (m_visualMainBeat)
                {
                    VisualOnMainBeat?.Invoke();
                }
                else
                {
                    VisualOnHalfBeat?.Invoke();
                }

                m_visualMainBeat = !m_visualMainBeat;
            }
        }

        public void SaveVisualSettingData()
        {
            //PlayerPrefs.SetFloat(visualOffsetSettingKey, m_visualOffset);
        }

        public void AddInputSample()
        {
            float offsetValue = (m_timeSinceLastBeat < (HalfBeatLength / 2f) ? m_timeSinceLastBeat : m_timeSinceLastBeat - BeatLength);
            
            float relativeVal1 = MainBeatPercentage;
            float relativeVal2 = 1f -  MainBeatPercentage;
            m_onBeatAccuracy = 1f - Mathf.Min(relativeVal1, relativeVal2);
            m_onBeatAccuracy = m_onBeatAccuracy.CastToRange(
                0.5f, 1f,
                0f, 1f);
            m_lastInputAccuracy = m_onBeatAccuracy;
            m_timeSinceLastInput = 0f;

            AddInputSample(Mathf.Clamp(-offsetValue / 2f, m_minInputOffset, m_maxInputOffset));
        }

        private void AddInputSample(float sampleDelay)
        {
            float discardedInput = 0f;
            if (m_inputOffsets.Count > m_autoTuneInputs - 1)
            {
                discardedInput = m_inputOffsets[0];
                m_inputOffsets.RemoveAt(0);
            }

            m_inputOffsets.Add(sampleDelay);
            float oldOffset = m_visualOffset;
            m_inputWindowOffset -= discardedInput / m_autoTuneInputs;
            m_inputWindowOffset += sampleDelay / m_autoTuneInputs;

            m_inputWindowOffset = Mathf.Clamp(m_inputWindowOffset, m_minInputOffset, m_maxInputOffset);
            SetVisualOffset(1f - (m_inputWindowOffset - m_minInputOffset) / (m_maxInputOffset - m_minInputOffset));
            //Debug.LogError($"m_visualOffset updated to {m_visualOffset}");
            //m_visualTimeSinceLastBeat += m_visualOffset - oldOffset;
        }

        public void SetVisualOffset(float value)
        {
            float newValue = Mathf.Lerp(m_minVisualOffset, m_maxVisualOffset, value);
            float delta = newValue - m_visualOffset;
            m_visualOffset += delta;
            m_visualTimeSinceLastBeat += delta;
            SaveVisualSettingData();
        }
        
        public void SetInputWindowOffset(float value)
        {
            float newValue = Mathf.Lerp(m_minInputOffset, m_maxInputOffset, value);
            float delta = newValue - m_inputWindowOffset;
            m_inputWindowOffset += delta;
            m_inputTimeSinceLastBeat += delta;
            //m_visualOffset += delta;
            //m_visualTimeSinceLastBeat += delta;
            //SaveVisualSettingData();
        }
        
        public void SetGracePeriod(float value)
        {
            m_inputWindowGrace = Mathf.Lerp(value, 0f, m_maxInputGrace);
        }

        public override void Dispose()
        {
            m_mainBeat = false;
            m_visualMainBeat = false;
            MainBeatValue = 0f;
            HalfBeatValue = 0f;
            m_timeSinceLastBeat = 0f;
            m_inputTimeSinceLastBeat = 0f;
            m_visualOffset = 0f;
            m_visualTimeSinceLastBeat = m_visualOffset;
        }

        private void OnValidate()
        {
            //Only full beats for half beats also divide by 2
            m_beatTime = 60f / m_tempo;
        }

        public void PauseMusic()
        {
            if (m_backgroundTrackGroup.CurrentEmitter != null)
            {
                m_backgroundTrackGroup.CurrentEmitter.EventInstance.setPaused(true);
            }

            if (m_metronomeGroup.GroupEmitters[m_metronomeSelection] != null)
            {
                m_metronomeGroup.GroupEmitters[m_metronomeSelection].EventInstance.setPaused(true);
            }
            //m_metronomeGroup.CurrentEmitter.Stop();
        }

        public void UnPauseMusic()
        {
            if (m_backgroundTrackGroup.CurrentEmitter != null)
            {
                m_backgroundTrackGroup.CurrentEmitter.EventInstance.setPaused(false);
            }

            if (m_metronomeGroup.GroupEmitters[m_metronomeSelection] != null)
            {
                m_metronomeGroup.GroupEmitters[m_metronomeSelection].EventInstance.setPaused(false);
            }
            //PlayMetronomeTrack(m_backgroundTrackGroup.CurrentEmitter);
        }

        public float GetVolume(VolumeGroup group)
        {
            return group == VolumeGroup.Master ? 
                m_volumeDict[VolumeGroup.Master].value : 
                m_volumeDict[group].value * m_volumeDict[VolumeGroup.Master].value;
        }
        
        public float GetDirectVolume(VolumeGroup group)
        {
            return m_volumeDict[group].value;
        }
        
        //TODO: should not be in BeatSystem
        public void SetVolume(VolumeGroup group, float value)
        {
            m_volumeDict[group].UpdateValue(value, false);
        }
        
        public void SetEffectValue(EffectType type, int level)
        {
            m_effectsDict[type].SetParameterLevel(level, 3);
        }
    }

    public enum VolumeGroup
    {
        Master = 0,
        Music = 10,
        AttackSFX = 20,
        Footsteps = 30,
        Metronome = 40,
    }
    
    public enum EffectType
    {
        MultiBandEQ = 0,
        Delay = 10,
        Reverb = 20,
        Flanger = 30,
        Custom = 900,
    }
}