namespace RibCageGames.Music
{
    using System;
    using System.Runtime.InteropServices;
    using FMOD;
    using Base;
    using UnityEngine;
    using UnityEngine.Audio;
    using Debug = UnityEngine.Debug;

    [CreateAssetMenu(fileName = "MusicSystem", menuName = "RibCageGames/Services/MusicSystem")]
    public class MusicSystem : BaseService {
        [Header("Music analysis settings")]
        [SerializeField] private int m_sampleAmount = 512;
        [SerializeField] private int m_channelAmount = 32;
        [SerializeField] private int m_minimalChannelRange = 1;
        [SerializeField] private float m_growthFactor;
        [SerializeField] private int[] m_channelRanges;
        [SerializeField] private float m_initialBufferDecrease = 0.005f;
        [SerializeField] private float m_bufferDecreaseGrowth = 1.2f;
        [SerializeField] private Texture2D m_musicDataTexture;
        [SerializeField] private float[] m_channelValues;
        private float[] m_directAudioSamples;
        private float[] m_channelBuffers;
        private float[] m_channelBufferDecrease;
        private float[] m_channelMaximums;
        private float[] m_normalizedChannelValues;
        private float[] m_normalizedChannelBuffers;
        private float m_amplitude;
        private float m_amplitudeMaximum;
        private float m_amplitudeBuffer;
        private BeatSystem m_beatService;
        private Action<float> m_cancelMusic;
    
        public float[] AudioSamples => m_directAudioSamples;    
        public int ChannelCount => m_channelAmount;
        public float[] ChannelValues => m_channelValues;
        public float[] ChannelBuffers => m_channelBuffers;
        public float[] LevelValues => m_normalizedChannelValues;
        public float[] LevelBuffers => m_normalizedChannelBuffers;
        public float Amplitude => m_amplitude;
        public float AmplitudeBuffer => m_amplitudeBuffer;

        #region FMOD
    
        private DSP m_fft;
    
        private void StartFMOD()
        {
            if (FMODUnity.RuntimeManager.CoreSystem.createDSPByType(DSP_TYPE.FFT, out m_fft) != RESULT.OK)
            {
                Debug.LogError($"Fmod initiation failed!");
                return;
            }
            m_fft.setParameterInt((int)DSP_FFT.WINDOW, (int)DSP_FFT_WINDOW_TYPE.BLACKMAN);
            m_fft.setParameterInt((int)DSP_FFT.WINDOWSIZE, m_sampleAmount * 2);
            FMODUnity.RuntimeManager.StudioSystem.flushCommands();
        
            FMOD.Studio.Bus selectedBus = FMODUnity.RuntimeManager.GetBus("bus:/");
            if (selectedBus.hasHandle())
            {
                // Get the channel group
                if (selectedBus.getChannelGroup(out var channelGroup) == RESULT.OK)
                {
                    // Add fft to the channel group
                    if (channelGroup.addDSP(CHANNELCONTROL_DSP_INDEX.HEAD, m_fft) == RESULT.OK)
                    {
                        Debug.Log($"FMOD initialized");
                    }
                }
            }
        }
    
        private void DisableFMOD()
        {
            FMOD.Studio.Bus selectedBus = FMODUnity.RuntimeManager.GetBus("bus:/");
            if (selectedBus.hasHandle())
            {
                if (selectedBus.getChannelGroup(out var channelGroup) == RESULT.OK)
                {
                    if(m_fft.hasHandle())
                    {
                        channelGroup.removeDSP(m_fft);
                    }
                }
            }
        }

        private void UpdateFMOD()
        {
            m_fft.getParameterData((int)DSP_FFT.SPECTRUMDATA, out IntPtr unmanagedData, out _);
            DSP_PARAMETER_FFT fftData = (DSP_PARAMETER_FFT)Marshal.PtrToStructure(unmanagedData, typeof(DSP_PARAMETER_FFT));
            float[][] spectrum = fftData.spectrum;

            if (fftData.numchannels > 0)
            {
                for (int i = 0; i < m_sampleAmount; ++i)
                {
                    m_directAudioSamples[i] = 0f;
                    for (int j = 0; j < fftData.numchannels; j++)
                    {
                        m_directAudioSamples[i] += spectrum[j][i];
                    }
                }
            }
        }

        #endregion
    
        public override void Init(GameObject servicePrefab = null) {
            m_channelValues = new float[m_channelAmount];
            m_channelBuffers = new float[m_channelAmount];
            m_channelBufferDecrease = new float[m_channelAmount];
            m_channelMaximums = new float[m_channelAmount];
            m_normalizedChannelValues = new float[m_channelAmount];
            m_normalizedChannelBuffers = new float[m_channelAmount];
            m_directAudioSamples = new float[m_sampleAmount];
            for (int i = 0; i < m_channelAmount; i++) {
                m_channelMaximums[i] = 0.1f;
            }
        }

        public override void StartService(MonoInjector injector) {
            ServiceLocator.Get<MonoService>().OnUpdate.AddListener(ControlledUpdate);
            m_beatService = ServiceLocator.Get<BeatSystem>();
        
            StartFMOD();
        }

        private float m_stopTime;
        public void PauseMusic()
        {
            //if (m_audioSource != null)
            //{
            //    m_stopTime = m_audioSource.time;
            //    m_cancelMusic?.Invoke(0f);
            //}
        
            m_cancelMusic?.Invoke(0f);
        }
    
        public void UnPauseMusic()
        {
            //if (m_currentTrack != null && m_audioSource != null)
            //{
            //PlayClip(m_currentTrack.Track, m_currentTrack.MixerGroup);
            //m_audioSource.time = m_stopTime;
            //}
        }

        public override void Dispose()
        {
            DisableFMOD();
        }

        private void ControlledUpdate(float deltaTime) {
            UpdateFMOD();
            MakeFrequencyBands();
            SetBandBuffers(deltaTime);
            MakeAudioBands();
            SetAmplitude();
            SetTextureData();
        }

        private void MakeFrequencyBands() {
            int channelIndex = 0;
            for (int i = 0; i < m_channelAmount; i++)
            {
                int sampleCount = m_channelRanges[i];
                float average = 0f;
                for (int j = channelIndex; j < channelIndex + sampleCount; j++)
                {
                    average += m_directAudioSamples[j] * (j + 1);
                }

                average /= sampleCount;
                channelIndex += sampleCount;
                m_channelValues[i] = average;
            }
        
        }
    
        private void SetBandBuffers(float deltaTime) {
        
            for (int i = 0; i < m_channelAmount; i++) {
                if (m_channelValues[i] >= m_channelBuffers[i]) {
                    m_channelBuffers[i] = m_channelValues[i];
                    m_channelBufferDecrease[i] = m_initialBufferDecrease;
                } else {
                    m_channelBuffers[i] -= deltaTime * m_channelBufferDecrease[i];
                    m_channelBufferDecrease[i] += deltaTime * m_bufferDecreaseGrowth;
                }
            }
        }
    
        private void MakeAudioBands() {
            for (int i = 0; i < m_channelAmount; i++) {
                if (m_channelValues[i] > m_channelMaximums[i]) {
                    m_channelMaximums[i] = m_channelValues[i];
                }

                m_normalizedChannelValues[i] = Mathf.Clamp01(m_channelValues[i] / m_channelMaximums[i]);
                m_normalizedChannelBuffers[i] = Mathf.Clamp01(m_channelBuffers[i] / m_channelMaximums[i]);
            }
        }
    
        private void SetAmplitude() {
            float amplitudeSum = 0f;
            float amplitudeBuffer = 0f;
            for (int i = 0; i < m_channelAmount; i++) {
                amplitudeSum += m_normalizedChannelValues[i];
                amplitudeBuffer += m_normalizedChannelBuffers[i];
            }

            if (amplitudeSum > m_amplitudeMaximum) {
                m_amplitudeMaximum = amplitudeSum;
            }

            m_amplitude = (amplitudeSum / m_amplitudeMaximum);
            m_amplitudeBuffer = (amplitudeBuffer / m_amplitudeMaximum);
        }
    
        private void SetTextureData() {
            if (m_musicDataTexture == null) { return; }
            Texture2D texture = m_musicDataTexture;
            for (int y = 0; y < m_musicDataTexture.height; y++)
            {
                for (int x = 0; x < m_musicDataTexture.width; x++)
                {
                    Color color = new Color(LevelValues[x], LevelBuffers[x], Amplitude);
                    texture.SetPixel(x, y, color);
                }
            }
            texture.Apply();
        }
    
#if UNITY_EDITOR
        private void OnValidate()
        {
            //m_mixerGroup.audioMixer.SetFloat("", 0f);
            m_channelRanges = new int[m_channelAmount];

            int sum = 0;
            for (int i = 0; i < m_channelRanges.Length ; i++)
            {
                if (i == m_channelAmount - 1)
                {
                    m_channelRanges[i] = m_sampleAmount - sum;
                }
                else
                {
                    m_channelRanges[i] = Mathf.Min(Mathf.RoundToInt(Mathf.Pow(m_growthFactor, i)), m_sampleAmount - sum);
                    sum += m_channelRanges[i];
                }
            }
        }
#endif
    }
}
