using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

[CreateAssetMenu(fileName = "BeatSystemSettings", menuName = "RibCageGames/CellosLastConcert/BeatSystemSettings", order = 1)]
public class BeatSystemSettings : ScriptableObject
{
    
    //[Range(0.01f, 1f)]
    //[SerializeField] private float mainBeatTime = 0.5f;
    //
    //[Range(0.01f, 1f)]
    //[SerializeField] private float m_secondaryBeatTimePrecentage = 1f;
//
    //[Range(0.01f, 1f)]
    //[SerializeField] private float m_beatFadeDuration = 0.5f;
    //
    //[Range(0.01f, 0.5f)]
    //[SerializeField] private float m_inputWindow = 0.1f;
    //
    //public float MainBeatTime => mainBeatTime;
    //
    //public float SecondaryBeatTime => mainBeatTime * m_secondaryBeatTimePrecentage;
   //
    //public float BeatFadeDuration => m_beatFadeDuration;
    //
    //public float InputWindow => m_inputWindow;

#if UNITY_EDITOR
    public static BeatSystemSettings Load() {
        string[] guids = UnityEditor.AssetDatabase.FindAssets("t:BeatSystemSettings");
        if (guids.Length == 0)
        {
            Debug.LogWarning("Could not find DisplaySettings asset. Will use default settings instead.");
            return ScriptableObject.CreateInstance<BeatSystemSettings>();
        }
        else
        {
            string path = UnityEditor.AssetDatabase.GUIDToAssetPath(guids[0]);
            return UnityEditor.AssetDatabase.LoadAssetAtPath<BeatSystemSettings>(path);
        }
    }
#endif
}
