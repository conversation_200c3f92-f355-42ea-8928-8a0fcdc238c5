using System.Collections.Generic;
using FMODUnity;
using RibCageGames.Base;
using UnityEngine;
using UnityEngine.Events;

namespace RibCageGames.Music.fmod
{
    [CreateAssetMenu(fileName = "fmodEventEmitter", menuName = "RibCageGames/Music/fmodEventEmitter")]
    public class fmodEventEmitter : MonoScriptableObject, ISoundSource
    {
        public EventReference EventReference;
        
        public bool AllowFadeout = true;
        public bool TriggerOnce = false;
        public bool Preload = false;
        public ParamRef[] Params = new ParamRef[0];
        public bool OverrideAttenuation = false;
        public float OverrideMinDistance = -1.0f;
        public float OverrideMaxDistance = -1.0f;

        [SerializeField] private float m_startTime;
        [SerializeField] private int m_startTimeMilliseconds;
        [SerializeField] VolumeGroup m_group = VolumeGroup.Master;

        protected FMOD.Studio.EventDescription eventDescription;

        protected FMOD.Studio.EventInstance instance;

        private bool hasTriggered = false;
        private bool isQuitting = false;
        private bool isOneshot = false;
        private List<ParamRef> cachedParams = new List<ParamRef>();
        private BeatSystem m_beatSystem;
        
        private const string SnapshotString = "snapshot";

        public FMOD.Studio.EventDescription EventDescription { get { return eventDescription; } }

        public FMOD.Studio.EventInstance EventInstance { get { return instance; } }

        public bool IsActive { get; private set; }

        public UnityEvent<fmodEventEmitter> OnPlay = new UnityEvent<fmodEventEmitter>();

        private float MaxDistance
        {
            get
            {
                if (OverrideAttenuation)
                {
                    return OverrideMaxDistance;
                }

                if (!eventDescription.isValid())
                {
                    Lookup();
                }

                float minDistance, maxDistance;
                eventDescription.getMinMaxDistance(out minDistance, out maxDistance);
                return maxDistance;
            }
        }
        

        private void UpdatePlayingStatus(bool force = false, Transform attachedTransform = null)
        {
            // If at least one listener is within the max distance, ensure an event instance is playing
            bool playInstance = attachedTransform == null || StudioListener.DistanceSquaredToNearestListener(attachedTransform.position) <= (MaxDistance * MaxDistance);

            if (force || playInstance != IsPlaying())
            {
                if (playInstance)
                {
                    PlayInstance(attachedTransform);
                }
                else
                {
                    StopInstance();
                }
            }
        }

        public override void Initialize()
        {
            RuntimeUtils.EnforceLibraryOrder();
            if (Preload)
            {
                Lookup();
                eventDescription.loadSampleData();
            }

            m_startTimeMilliseconds = (int)(1000f * m_startTimeMilliseconds);
            m_beatSystem = ServiceLocator.Get<BeatSystem>();
        }

        private void OnApplicationQuit()
        {
            //TODO: hook this up to monoinjector
            isQuitting = true;
        }

        protected void OnDestroy()
        {
            if (!isQuitting)
            {
                if (instance.isValid())
                {
                    RuntimeManager.DetachInstanceFromGameObject(instance);
                    if (eventDescription.isValid() && isOneshot)
                    {
                        instance.release();
                        instance.clearHandle();
                    }
                }
                
                if (Preload)
                {
                    eventDescription.unloadSampleData();
                }
            }
        }

        private void Lookup()
        {
            eventDescription = RuntimeManager.GetEventDescription(EventReference);

            if (eventDescription.isValid())
            {
                for (int i = 0; i < Params.Length; i++)
                {
                    FMOD.Studio.PARAMETER_DESCRIPTION param;
                    eventDescription.getParameterDescriptionByName(Params[i].Name, out param);
                    Params[i].ID = param.id;
                }
            }
        }

        [ContextMenu("Play")]
        public void Play()
        {
            Play(null);
        }
        
        private void Play(Transform attachedTransform)
        {
            if (TriggerOnce && hasTriggered)
            {
                return;
            }

            if (EventReference.IsNull)
            {
                return;
            }

            cachedParams.Clear();

            if (!eventDescription.isValid())
            {
                Lookup();
            }

            bool isSnapshot;
            eventDescription.isSnapshot(out isSnapshot);

            if (!isSnapshot)
            {
                eventDescription.isOneshot(out isOneshot);
            }

            bool is3D;
            eventDescription.is3D(out is3D);

            IsActive = true;

            if (is3D && !isOneshot && Settings.Instance.StopEventsOutsideMaxDistance)
            {
                UpdatePlayingStatus(true);
            }
            else
            {
                PlayInstance(attachedTransform);
            }
        }

        private void PlayInstance(Transform attachedTransform = null)
        {
            if (!instance.isValid())
            {
                instance.clearHandle();
            }

            // Let previous oneshot instances play out
            if (isOneshot && instance.isValid())
            {
                instance.release();
                instance.clearHandle();
            }

            bool is3D;
            eventDescription.is3D(out is3D);

            if (!instance.isValid())
            {
                eventDescription.createInstance(out instance);

                // Only want to update if we need to set 3D attributes
                if (is3D && attachedTransform != null)
                {

                    instance.set3DAttributes(RuntimeUtils.To3DAttributes(attachedTransform.gameObject));
                    RuntimeManager.AttachInstanceToGameObject(instance, attachedTransform.gameObject);
                }
            }

            foreach (var param in Params)
            {
                instance.setParameterByID(param.ID, param.Value);
            }

            foreach (var cachedParam in cachedParams)
            {
                instance.setParameterByID(cachedParam.ID, cachedParam.Value);
            }

            //Possibly cache and use a callback to update
            instance.setVolume(m_beatSystem.GetVolume(m_group));

            if (is3D && OverrideAttenuation)
            {
                instance.setProperty(FMOD.Studio.EVENT_PROPERTY.MINIMUM_DISTANCE, OverrideMinDistance);
                instance.setProperty(FMOD.Studio.EVENT_PROPERTY.MAXIMUM_DISTANCE, OverrideMaxDistance);
            }
            
            OnPlay?.Invoke(this);
            
            if (m_startTimeMilliseconds > 0)
            {
                instance.setTimelinePosition(m_startTimeMilliseconds);
            }
            instance.start();
            
            hasTriggered = true;
        }

        public void Stop()
        {
            IsActive = false;
            cachedParams.Clear();
            StopInstance();
        }

        private void StopInstance()
        {
            if (instance.isValid())
            {
                instance.stop(AllowFadeout ? FMOD.Studio.STOP_MODE.ALLOWFADEOUT : FMOD.Studio.STOP_MODE.IMMEDIATE);
                instance.release();
                if (!AllowFadeout)
                {
                    instance.clearHandle();
                }
            }
        }

        public void SetParameter(string name, float value, bool ignoreseekspeed = false)
        {
            if (Settings.Instance.StopEventsOutsideMaxDistance && IsActive)
            {
                string findName = name;
                ParamRef cachedParam = cachedParams.Find(x => x.Name == findName);

                if (cachedParam == null)
                {
                    FMOD.Studio.PARAMETER_DESCRIPTION paramDesc;
                    eventDescription.getParameterDescriptionByName(name, out paramDesc);

                    cachedParam = new ParamRef();
                    cachedParam.ID = paramDesc.id;
                    cachedParam.Name = paramDesc.name;
                    cachedParams.Add(cachedParam);
                }

                cachedParam.Value = value;
            }

            if (instance.isValid())
            {
                instance.setParameterByName(name, value, ignoreseekspeed);
            }
        }

        public void SetParameter(FMOD.Studio.PARAMETER_ID id, float value, bool ignoreseekspeed = false)
        {
            if (Settings.Instance.StopEventsOutsideMaxDistance && IsActive)
            {
                FMOD.Studio.PARAMETER_ID findId = id;
                ParamRef cachedParam = cachedParams.Find(x => x.ID.Equals(findId));

                if (cachedParam == null)
                {
                    FMOD.Studio.PARAMETER_DESCRIPTION paramDesc;
                    eventDescription.getParameterDescriptionByID(id, out paramDesc);

                    cachedParam = new ParamRef();
                    cachedParam.ID = paramDesc.id;
                    cachedParam.Name = paramDesc.name;
                    cachedParams.Add(cachedParam);
                }

                cachedParam.Value = value;
            }

            if (instance.isValid())
            {
                instance.setParameterByID(id, value, ignoreseekspeed);
            }
        }

        public bool IsPlaying()
        {
            if (instance.isValid())
            {
                FMOD.Studio.PLAYBACK_STATE playbackState;
                instance.getPlaybackState(out playbackState);
                return (playbackState != FMOD.Studio.PLAYBACK_STATE.STOPPED);
            }
            return false;
        }
    }
}
