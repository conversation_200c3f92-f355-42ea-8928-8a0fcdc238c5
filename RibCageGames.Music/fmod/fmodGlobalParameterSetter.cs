using FMOD;
using RibCageGames.Base;
using RibCageGames.Editor;
using RibCageGames.Music;
using UnityEngine;
using Debug = UnityEngine.Debug;

namespace FMODUnity
{
    [CreateAssetMenu(fileName = "fmodGlobalParameterSetter", menuName = "RibCageGames/Music/fmodGlobalParameterSetter")]
    public class fmodGlobalParameterSetter : MonoScriptableObject
    {
        [SerializeField] private EffectType m_effectType;
        public EffectType EffectType => m_effectType;
        public float Value;
        public bool DelayedSet;
        [ConditionalShow("DelayedSet")]
        public float DelayedValue;
        [ConditionalShow("DelayedSet")]
        public float DelayedSetWait;
        public string Parameter;

        private float m_lastValue;
        
        private FMOD.Studio.PARAMETER_DESCRIPTION parameterDescription;

        public override void Initialize()
        {
            RuntimeManager.StudioSystem.getParameterDescriptionByName(Parameter, out parameterDescription);
            m_lastValue = Value;
        }

        public void TriggerParameter()
        {
            RuntimeManager.StudioSystem.setParameterByID(parameterDescription.id, Value);
            if (DelayedSetWait > 0f)
            {
                MonoProcess.WaitForSecondsProcess(DelayedSetWait).Do(() =>
                {
                    RuntimeManager.StudioSystem.setParameterByID(parameterDescription.id, DelayedValue);
                });
            }
        }

        public virtual void SetParameterLevel(int level, int maxlevel)
        {
            TriggerDynamicParameter((float)level / maxlevel);
        }

        public void TriggerDynamicParameter(float value)
        {
            OriginalTriggerParameter(value);
            if (DelayedSet && DelayedSetWait > 0f)
            {
                MonoProcess.WaitForSecondsProcess(DelayedSetWait).Do(() =>
                {
                    OriginalTriggerParameter(DelayedValue);
                });
            }
            return;
            Debug.LogError($"Set global fmod parameter {name}");
            
            RESULT result = RuntimeManager.StudioSystem.setParameterByID(parameterDescription.id, value);
            if (result != RESULT.OK)
            {
                Debug.LogError("setParameterByName failed with result: " + result);
            }
            if (DelayedSet && DelayedSetWait > 0f)
            {
                MonoProcess.WaitForSecondsProcess(DelayedSetWait).Do(() =>
                {
                    RuntimeManager.StudioSystem.setParameterByID(parameterDescription.id, DelayedValue);
                });
            }
        }

        private void OriginalTriggerParameter(float value)
        {
            if(Mathf.Approximately(m_lastValue, value))
            {
                return;
            }

            m_lastValue = value;
            
            bool paramNameSpecified = !string.IsNullOrEmpty(Parameter);
            if (paramNameSpecified)
            {
                RESULT result;
                bool paramIDNeedsLookup = string.IsNullOrEmpty(parameterDescription.name);
                if (paramIDNeedsLookup)
                {
                    result = RuntimeManager.StudioSystem.getParameterDescriptionByName(Parameter, out parameterDescription);
                    if (result != RESULT.OK)
                    {
                        RuntimeUtils.DebugLogError(string.Format(("[FMOD] StudioGlobalParameterTrigger failed to lookup parameter {0} : result = {1}"), Parameter, result));
                        Debug.LogError("setParameterByName failed with result: " + result);
                        return;
                    }
                }

                result = RuntimeManager.StudioSystem.setParameterByID(parameterDescription.id, value);
                if (result != RESULT.OK)
                {
                    RuntimeUtils.DebugLogError(string.Format(("[FMOD] StudioGlobalParameterTrigger failed to set parameter {0} : result = {1}"), Parameter, result));
                }
            }
        }
    }
}