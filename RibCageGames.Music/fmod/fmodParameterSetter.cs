using System;
using FMOD;
using RibCageGames.Base;
using RibCageGames.Editor;
using RibCageGames.Music.fmod;
using UnityEngine;
using Debug = UnityEngine.Debug;

namespace FMODUnity
{
    [Serializable]
    public class EmitterRef
    {
        public fmodEventEmitter Target;
        public ParamRef[] Params;
        public bool DelayedSet;
        [ConditionalShow("DelayedSet")]
        public float DelayedValue;
        [ConditionalShow("DelayedSet")]
        public float DelayedSetWait;
    }

    [CreateAssetMenu(fileName = "fmodParameterSetter", menuName = "RibCageGames/Music/fmodParameterSetter")]
    public class fmodParameterSetter: MonoScriptableObject
    {
        public EmitterRef[] Emitters;

        public override void Initialize()
        {
            for (int i = 0; i < Emitters.Length; i++)
            {
                var emitterRef = Emitters[i];
                if (emitterRef.Target != null && !emitterRef.Target.EventReference.IsNull)
                {
                    FMOD.Studio.EventDescription eventDesc = RuntimeManager.GetEventDescription(emitterRef.Target.EventReference);
                    if (eventDesc.isValid())
                    {
                        for (int j = 0; j < Emitters[i].Params.Length; j++)
                        {
                            FMOD.Studio.PARAMETER_DESCRIPTION param;
                            eventDesc.getParameterDescriptionByName(emitterRef.Params[j].Name, out param);
                            emitterRef.Params[j].ID = param.id;
                        }
                    }
                }
            }
        }

        public void TriggerParameters()
        {
            //Debug.LogError($"Set fmod parameter {name}");
            if (Emitters.Length > 0)
            {
                for (int i = 0; i < Emitters.Length; i++)
                {
                    var emitterRef = Emitters[i];
                    if (emitterRef.Target != null && emitterRef.Target.EventInstance.isValid())
                    {
                        for (int j = 0; j < Emitters[i].Params.Length; j++)
                        {
                            emitterRef.Target.EventInstance.setParameterByID(Emitters[i].Params[j].ID,
                                Emitters[i].Params[j].Value);
                            if (emitterRef.DelayedSet && emitterRef.DelayedSetWait > 0f)
                            {
                                var i1 = i;
                                var j1 = j;
                                MonoProcess.WaitForSecondsProcess(emitterRef.DelayedSetWait).Do(() =>
                                {
                                    RESULT result = emitterRef.Target.EventInstance.setParameterByID(Emitters[i1].Params[j1].ID,
                                        emitterRef.DelayedValue);
                                    if (result != RESULT.OK)
                                    {
                                        Debug.LogError($"Parameter setting failed for {name} with {result}");
                                    }
                                });
                            }
                        }
                    }
                }
            }
            else
            {
                //Sets globaL
                //RuntimeManager.StudioSystem.setParameterByID(parameterDescription.id, Value);
            }
        }

        public void TriggerDynamicParameters(float value)
        {
            for (int i = 0; i < Emitters.Length; i++)
            {
                var emitterRef = Emitters[i];
                if (emitterRef.Target != null && emitterRef.Target.EventInstance.isValid())
                {
                    for (int j = 0; j < Emitters[i].Params.Length; j++)
                    {
                        emitterRef.Target.EventInstance.setParameterByID(Emitters[i].Params[j].ID, value);
                        if (emitterRef.DelayedSetWait > 0f)
                        {
                            var i1 = i;
                            var j1 = j;
                            MonoProcess.WaitForSecondsProcess(emitterRef.DelayedSetWait).Do(() =>
                            {
                                emitterRef.Target.EventInstance.setParameterByID(Emitters[i1].Params[j1].ID, emitterRef.DelayedValue);
                            });
                        }
                    }
                }
            }
        }
    }
}
