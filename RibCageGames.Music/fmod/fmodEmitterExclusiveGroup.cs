using System.Collections.Generic;
using RibCageGames.Music.fmod;
using UnityEngine;
using UnityEngine.Events;

[CreateAssetMenu(fileName = "fmodEmitterExclusiveGroup", menuName = "RibCageGames/Music/fmodEmitterExclusiveGroup")]
public class fmodEmitterExclusiveGroup : MonoScriptableObject
{
    [SerializeField] private List<fmodEventEmitter> m_backgroundTrackEmitters = new();

    public List<fmodEventEmitter> GroupEmitters => m_backgroundTrackEmitters;

    public fmodEventEmitter CurrentEmitter => m_currentTrack;
    
    private UnityEvent<fmodEventEmitter> m_trackChanged = new();

    public UnityEvent<fmodEventEmitter> OnTrackChanged
    {
        get => m_trackChanged;
        set => m_trackChanged = value;
    }
    
    private fmodEventEmitter m_currentTrack;
    
    public override void Initialize()
    {
        m_currentTrack = null;
        foreach (fmodEventEmitter emitter in m_backgroundTrackEmitters)
        {
            emitter.OnPlay.RemoveListener(DisablePreviousTrack);
            emitter.OnPlay.AddListener(DisablePreviousTrack);
        }
    }

    private void DisablePreviousTrack(fmodEventEmitter newTrack)
    {
        if (m_currentTrack != null && newTrack != m_currentTrack)
        {
            m_currentTrack.Stop();
        }
        
        m_trackChanged?.Invoke(newTrack);
        m_currentTrack = newTrack;
    }
}
