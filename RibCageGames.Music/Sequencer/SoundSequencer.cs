namespace RibCageGames.Music
{
    using UnityEngine;
    using RibCageGames.Base;

    public class SoundSequencer : MonoBeh<PERSON><PERSON>
    {

        [SerializeField] private AudioSource m_source;
        [SerializeField] private Sequence m_sequence;

        private int m_subSequenceIndex = 0;
        private int m_beatIndex = 0;

        private void Start()
        {
            ServiceLocator.Get<BeatSystem>().OnBeat.AddListener(PlayBeat);
        }

        private void PlayBeat(bool mainBeat)
        {
            SubSequence subSequence = m_sequence.SubSequences[m_subSequenceIndex];
            if (mainBeat)
            {
                PlaySoundGroup(subSequence.BeatSounds);
            }

            PlaySoundGroup(subSequence.SequencedSounds[m_beatIndex]);


            int beatCount = subSequence.SequencedSounds.Count;
            m_beatIndex++;
            if (m_beatIndex == beatCount)
            {
                m_beatIndex = 0;
                m_subSequenceIndex = (m_subSequenceIndex + 1) % m_sequence.SubSequences.Count;
            }
        }

        private void PlaySoundGroup(ClipGroup group)
        {
            foreach (AudioClip sound in group.Sounds)
            {
                m_source.PlayOneShot(sound);
            }
        }
    }
}
