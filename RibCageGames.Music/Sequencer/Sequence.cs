namespace RibCageGames.Music
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using UnityEngine;

    [CreateAssetMenu(fileName = "Sequence", menuName = "RibCageGames/CellosLastConcert/Sequence")]
    public class Sequence : ScriptableObject
    {
        public List<SubSequence> SubSequences => m_subSequences;

        [SerializeField] public List<SubSequence> m_subSequences;


    }

    /// <summary>
    /// A subsequence made for one full house
    /// </summary>
    [Serializable]
    public class SubSequence
    {
        public ClipGroup BeatSounds => m_beatSounds;

        public List<ClipGroup> SequencedSounds => m_sequencedSounds;

        [SerializeField] private ClipGroup m_beatSounds;

        [SerializeField] private List<ClipGroup> m_sequencedSounds;
    }

    [Serializable]
    public class ClipGroup
    {
        public List<AudioClip> Sounds => m_sounds;

        [SerializeField] private List<AudioClip> m_sounds;
    }
}