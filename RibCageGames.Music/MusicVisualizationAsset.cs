
namespace RibCageGames.Music
{
    using System.Collections.Generic;
    using RibCageGames.Base;
    using RibCageGames.Editor;
    using UnityEngine;

    [CreateAssetMenu(fileName = "MusicVisualizationAsset", menuName = "RibCageGames/Services/MusicVisualizationAsset")]
    public class MusicVisualizationAsset : BaseService
    {
        [ArrayElementTitle("m_visualType")] [SerializeField]
        private List<MusicVisualization> m_musicVisualizations;

        public override void Init(GameObject servicePrefab = null) { }

        public override void StartService(MonoInjector injector)
        {
            MonoService monoService = ServiceLocator.Get<MonoService>();
            MusicSystem musicSystem = ServiceLocator.Get<MusicSystem>();
            BeatSystem beatSystem = ServiceLocator.Get<BeatSystem>();

            foreach (MusicVisualization visualization in m_musicVisualizations)
            {
                visualization.RegisterVisualUpdate(monoService, beatSystem, musicSystem);
            }
        }

        public override void Dispose()
        {
            MonoService monoService = ServiceLocator.Get<MonoService>();
            foreach (MusicVisualization visualization in m_musicVisualizations)
            {
                visualization.UnRegisterVisualUpdate(monoService);
            }
        }
        
        private void OnValidate()
        {
            foreach (MusicVisualization visualization in m_musicVisualizations)
            {
                visualization.Validate(null);
            }
        }
    }
}