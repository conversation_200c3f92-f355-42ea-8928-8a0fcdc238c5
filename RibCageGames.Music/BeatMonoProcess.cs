using System.Collections;
using System.Collections.Generic;
using RibCageGames.Base;
using RibCageGames.Music;
using UnityEngine;

public class BeatMonoProcess : MonoProcess
{
    private enum BeatStepType
    {
        WaitForFullBeat,
        WaitForFixedHalfBeat,
    }
    
    private BeatSystem m_beatSystem;
    
    public BeatMonoProcess() : base()
    {
        m_beatSystem = ServiceLocator.Get<BeatSystem>();
    }
    
    public static BeatMonoProcess WaitForSecondsBeats(float duration)
    {
        BeatMonoProcess process = new BeatMonoProcess();
        process.m_stepQueue.Enqueue(new MonoProcessStep
        {
            StepType = StepType.WaitForSecondExact,
            Action = null,
            TimeRequired = duration,
        });

        NewManual()
            .WaitForFrame()
            .Do(process.Run)
            .Run();

        return process;
    }
    
    
    /*public void Dispose()
    {
        if (m_disposed)
        {
            return;
        }
            
        m_disposed = true;

        if (m_debug)
        {
            Debug.Log($"Dispose {debugId}");
        }

        m_monoService.OnUpdate.RemoveListener(UpdateStep);
        m_monoService.OnFixedUpdate.RemoveListener(UpdateFixedStep);
        m_monoService.OnPerSecondUpdate.RemoveListener(UpdatePerSecondStep);
        m_monoService.OnUpdate.RemoveListener(UpdateStepExact);
        m_monoService.OnUpdate.RemoveListener(UpdateStepPredicate);

        m_stepQueue = null;
        m_predicate = null;
        m_monoService = null;
        m_done = true;
    }*/
}
