namespace RibCageGames.Music
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using UnityEngine;

    [CreateAssetMenu(fileName = "Sequence", menuName = "RibCageGames/CellosLastConcert/CompositeMusicTrack")]
    public class CompositeMusicTrack : MusicTrack
    {
        [SerializeField] private List<MusicTrack> m_chunks;
        [NonSerialized] private int m_currentTrackIndex;
        
        public override AudioClip Track => m_chunks[m_currentTrackIndex].Track;

        public override MusicTrack NextTrack {
            get
            {
                m_currentTrackIndex++;
                
                if (m_nextTrack == null || !ShouldTransition || m_currentTrackIndex < m_chunks.Count)
                {
                    m_currentTrackIndex %= m_chunks.Count;
                    return this;
                }
                else
                {
                    m_currentTrackIndex %= m_chunks.Count;
                    return m_nextTrack;
                }
            }
        }
        
        public override int LengthInBeats => m_chunks[m_currentTrackIndex].LengthInBeats;
    }
}