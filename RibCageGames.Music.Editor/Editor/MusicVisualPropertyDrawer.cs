using System.Collections.Generic;
using System.Linq;
using RibCageGames.Music;
using UnityEditor;
using UnityEngine;

[CustomPropertyDrawer(typeof(MusicVisualBase))]
public class MusicVisualPropertyDrawer : PropertyDrawer
{
    private List<string> m_visualTypeStrings;
    
    public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
    {
        return EditorGUI.GetPropertyHeight(property) + EditorGUIUtility.singleLineHeight;
    }

    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        SerializedProperty typeIndexProp = property.FindPropertyRelative("m_concreteTypeIndex");

        if (typeIndexProp == null)
        {
            EditorGUI.PropertyField(position, property, label, true);
            return;
        }

        if (m_visualTypeStrings == null)
        {
            m_visualTypeStrings = MusicVisualBase.GetAllVisualTypes().Select(type => type.ToString()).ToList();
            for (int i = 0; i < m_visualTypeStrings.Count; i++)
            {
                string typeString = m_visualTypeStrings[i];
                string[] splitTypeString = typeString.Split('.');
                m_visualTypeStrings[i] = RemoveFromEnd(splitTypeString[splitTypeString.Length - 1], "MusicVisual");
            }
        }

        Rect typeSelectorPosition = position;
        typeSelectorPosition.height = EditorGUIUtility.singleLineHeight;

        if (GUI.Button(typeSelectorPosition,
            typeIndexProp.intValue == -1 ? "Select a command type" : m_visualTypeStrings[typeIndexProp.intValue],
            EditorStyles.popup))
        {
            GenericMenu menu = new GenericMenu();
            for (int i = 0; i < m_visualTypeStrings.Count; i++)
            {
                string commandName = m_visualTypeStrings[i];
                int index = i;
                menu.AddItem(new GUIContent(commandName),
                    typeIndexProp.intValue == i,
                    selectionValue =>
                    {
                        typeIndexProp.intValue = index;
                        property.serializedObject.ApplyModifiedProperties();
                    }, commandName);
            }

            menu.ShowAsContext();
        }

        Rect basePosition = position;
        basePosition.y += EditorGUIUtility.singleLineHeight;
        basePosition.height -= EditorGUIUtility.singleLineHeight;

        if (typeIndexProp.intValue != -1)
        {
            EditorGUI.PropertyField(basePosition, property, label, true);
        }
    }
    
    public static string RemoveFromEnd(string s, string suffix)
    {
        if (s.EndsWith(suffix))
        {
            return s.Substring(0, s.Length - suffix.Length);
        }

        return s;
    }
}
