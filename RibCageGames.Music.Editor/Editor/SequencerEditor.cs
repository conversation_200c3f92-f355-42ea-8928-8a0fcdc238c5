namespace RibCageGames.Music.Editor
{   
    using UnityEditor;
    using RibCageGames.Editor.Editor;

    [CustomEditor(typeof(Sequence))]
    public class SequencerEditor : UnityEditor.Editor
    {
        private SerializedProperty m_subSequences;
        private bool m_sameLengthHouses;

        void OnEnable()
        {
            m_sameLengthHouses = true;
            //m_subSequences = serializedObject.FindProperty("m_subSequences");
            m_subSequences = serializedObject.GetProperty(() => ((target) as Sequence).m_subSequences);
        }

        public override void OnInspectorGUI()
        {
            //TODO: finish
            serializedObject.UpdateIfRequiredOrScript();

            m_sameLengthHouses = EditorGUILayout.Toggle("Same Length Houses", m_sameLengthHouses);

            //All houses, vertical
            EditorGUILayout.BeginVertical();

            //Single house
            //EditorGUILayout.; 


            EditorGUILayout.EndVertical();

            EditorGUILayout.PropertyField(m_subSequences);
            //base.OnInspectorGUI();
            serializedObject.ApplyModifiedProperties();
        }
    }
}